/* Copyright (c) Business Objects 2006. All rights reserved. */

var L_bobj_crv_MainReport = "\u4E3B\u5831\u8868";
// Viewer Toolbar tooltips
var L_bobj_crv_FirstPage = "\u79FB\u81F3\u7B2C\u4E00\u9801";
var L_bobj_crv_PrevPage = "\u79FB\u81F3\u4E0A\u4E00\u9801";
var L_bobj_crv_NextPage = "\u79FB\u81F3\u4E0B\u4E00\u9801";
var L_bobj_crv_LastPage = "\u79FB\u81F3\u6700\u5F8C\u4E00\u9801";
var L_bobj_crv_ParamPanel = "\u53C3\u6578\u9762\u677F";
var L_bobj_crv_Parameters = "\u53C3\u6578";
var L_bobj_crv_GroupTree = "\u7FA4\u7D44\u6A39\u72C0\u7D50\u69CB";
var L_bobj_crv_DrillUp = "\u5F80\u4E0A\u947D\u53D6";
var L_bobj_crv_Refresh = "\u91CD\u65B0\u6574\u7406\u5831\u8868";
var L_bobj_crv_Zoom = "\u7E2E\u653E";
var L_bobj_crv_PageNav = "\u9801\u9762\u5C0E\u89BD";
var L_bobj_crv_SelectPage = "\u79FB\u81F3\u9801\u9762";
var L_bobj_crv_SearchText = "\u641C\u5C0B\u6587\u5B57";
var L_bobj_crv_Export = "\u532F\u51FA\u9019\u4EFD\u5831\u8868";
var L_bobj_crv_Print = "\u5217\u5370\u9019\u4EFD\u5831\u8868";
var L_bobj_crv_TabList = "\u7D22\u5F15\u6A19\u7C64\u6E05\u55AE";
var L_bobj_crv_Close = "\u95DC\u9589";
var L_bobj_crv_Logo=  "Business Objects \u6A19\u8A8C";
var L_bobj_crv_FileMenu = "[\u6A94\u6848] \u529F\u80FD\u8868";

var L_bobj_crv_File = "\u6A94\u6848";

var L_bobj_crv_Show = "\u986F\u793A";
var L_bobj_crv_Hide = "\u96B1\u85CF";

var L_bobj_crv_Find = "\u5C0B\u627E...";
var L_bobj_crv_of = "%1 (\u5171 %2 \u500B)"; // Example: Page "1 of 3"

var L_bobj_crv_submitBtnLbl = "\u532F\u51FA";
var L_bobj_crv_ActiveXPrintDialogTitle = "\u5217\u5370";
var L_bobj_crv_PDFPrintDialogTitle = "\u5217\u5370\u6210 PDF";
var L_bobj_crv_PrintRangeLbl = "\u9801\u9762\u7BC4\u570D:";
var L_bobj_crv_PrintAllLbl = "\u6240\u6709\u9801\u9762";
var L_bobj_crv_PrintPagesLbl = "\u9078\u53D6\u9801\u9762";
var L_bobj_crv_PrintFromLbl = "\u5F9E:";
var L_bobj_crv_PrintToLbl = "\u5230:";
var L_bobj_crv_PrintInfoTitle = "\u5217\u5370\u6210 PDF:";
var L_bobj_crv_PrintInfo1 = '\u6AA2\u8996\u5668\u5FC5\u9808\u532F\u51FA\u81F3 PDF \u624D\u80FD\u5217\u5370\u3002\u8ACB\u5728\u6587\u4EF6\u958B\u555F\u5F8C\uFF0C\u5F9E PDF \u95B1\u8B80\u61C9\u7528\u7A0B\u5F0F\u9078\u64C7\u5217\u5370\u9078\u9805\u3002';
var L_bobj_crv_PrintInfo2 = '\u6CE8\u610F: \u60A8\u5FC5\u9808\u5DF2\u7D93\u5B89\u88DD PDF \u95B1\u8B80\u7A0B\u5F0F (\u4F8B\u5982 Adobe Reader) \u624D\u80FD\u5217\u5370\u3002';
var L_bobj_crv_PrintPageRangeError = "\u8ACB\u8F38\u5165\u6709\u6548\u7684\u9801\u9762\u7BC4\u570D\u3002";

var L_bobj_crv_ExportBtnLbl = "\u532F\u51FA";
var L_bobj_crv_ExportDialogTitle = "\u532F\u51FA";
var L_bobj_crv_ExportFormatLbl = "\u6A94\u6848\u683C\u5F0F:";
var L_bobj_crv_ExportInfoTitle = "\u82E5\u8981\u532F\u51FA:";

var L_bobj_crv_ParamsApply = "\u5957\u7528";
var L_bobj_crv_ParamsAdvDlg = "\u7DE8\u8F2F\u53C3\u6578\u503C";
var L_bobj_crv_ParamsDeleteTooltip = "\u522A\u9664\u53C3\u6578\u503C";
var L_bobj_crv_ParamsAddValue = "\u6309\u4E00\u4E0B\u52A0\u5165...";
var L_bobj_crv_ParamsApplyTip = "[\u5957\u7528] \u6309\u9215 (\u5DF2\u555F\u7528)";
var L_bobj_crv_ParamsApplyDisabledTip = "[\u5957\u7528] \u6309\u9215 (\u5DF2\u505C\u7528)";
var L_bobj_crv_ParamsDlgTitle = "\u8F38\u5165\u503C";
var L_bobj_crv_ParamsCalBtn = "[\u65E5\u66C6] \u6309\u9215";
var L_bobj_crv_Reset= "\u91CD\u8A2D";
var L_bobj_crv_ResetTip = "[\u91CD\u8A2D] \u6309\u9215 (\u5DF2\u555F\u7528)";
var L_bobj_crv_ResetDisabledTip = "[\u91CD\u8A2D] \u6309\u9215 (\u5DF2\u505C\u7528)";
var L_bobj_crv_ParamsDirtyTip = "\u53C3\u6578\u503C\u5DF2\u8B8A\u66F4\u3002\u6309\u4E00\u4E0B [\u5957\u7528] \u6309\u9215\u5957\u7528\u8B8A\u66F4\u3002";
var L_bobj_crv_ParamsDataTip = "\u6B64\u70BA\u8CC7\u6599\u64F7\u53D6\u53C3\u6578";
var L_bobj_crv_ParamsMaxNumDefaultValues = "\u6309\u4E00\u4E0B\u9019\u88E1\u4EE5\u53D6\u5F97\u5176\u4ED6\u9805\u76EE...";
var L_bobj_crv_paramsOpenAdvance = " \'%1\' \u7684\u9032\u968E\u63D0\u793A\u6309\u9215";

var L_bobj_crv_ParamsInvalidTitle = "\u53C3\u6578\u503C\u7121\u6548";
var L_bobj_crv_ParamsTooLong = "\u53C3\u6578\u503C\u7684\u9577\u5EA6\u4E0D\u53EF\u8D85\u904E %1 \u500B\u5B57\u5143";
var L_bobj_crv_ParamsTooShort = "\u53C3\u6578\u503C\u7684\u9577\u5EA6\u5FC5\u9808\u81F3\u5C11\u6709 %1 \u500B\u5B57\u5143";
var L_bobj_crv_ParamsBadNumber = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u6578\u5B57]\uFF0C\u53EA\u80FD\u5305\u542B\u8CA0\u865F\u3001\u6578\u5B57 (\"0-9\")\u3001\u6578\u5B57\u5206\u7D44\u7B26\u865F\u6216\u5C0F\u6578\u9EDE\u7B26\u865F\u3002";
var L_bobj_crv_ParamsBadCurrency = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u8CA8\u5E63]\uFF0C\u53EA\u80FD\u5305\u542B\u8CA0\u865F\u3001\u6578\u5B57 (\"0-9\")\u3001\u6578\u5B57\u5206\u7D44\u7B26\u865F\u6216\u5C0F\u6578\u9EDE\u7B26\u865F\u3002";
var L_bobj_crv_ParamsBadDate = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u65E5\u671F]\uFF0C\u4E14\u6B63\u78BA\u7684\u683C\u5F0F\u70BA \"%1\"\uFF0C\u5176\u4E2D \"yyyy\" \u70BA\u56DB\u4F4D\u6578\u7684\u5E74\u4EFD\uFF0C\"mm\" \u70BA\u6708\u4EFD (\u4F8B\u5982\uFF0C\u4E00\u6708 = 1)\uFF0C\u800C \"dd\" \u5247\u70BA\u6307\u5B9A\u6708\u4EFD\u4E2D\u7684\u65E5\u6578\u3002";
var L_bobj_crv_ParamsBadTime = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u6642\u9593]\uFF0C\u4E14\u6B63\u78BA\u7684\u683C\u5F0F\u70BA \"hh:mm:ss\"\uFF0C\u5176\u4E2D \"hh\" \u70BA 24 \u6642\u5236\u7684\u6642\u6578\uFF0C\"mm\" \u70BA\u5206\u9418\u6578\uFF0C\u800C \"ss\" \u5247\u70BA\u79D2\u6578\u3002";
var L_bobj_crv_ParamsBadDateTime = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u65E5\u671F\u6642\u9593]\uFF0C\u4E14\u6B63\u78BA\u7684\u683C\u5F0F\u70BA \"%1 hh:mm:ss\"\u3002\"yyyy\" \u70BA\u56DB\u4F4D\u6578\u7684\u5E74\u4EFD\uFF0C\"mm\" \u70BA\u6708\u4EFD (\u4F8B\u5982\uFF0C\u4E00\u6708 = 1)\uFF0C\"dd\" \u70BA\u7576\u6708\u7684\u65E5\u671F\uFF0C\"hh\" \u70BA 24 \u6642\u5236\u7684\u6642\u6578\uFF0C\"mm\" \u70BA\u5206\u9418\u6578\uFF0C\u800C \"ss\" \u5247\u70BA\u79D2\u6578\u3002";
var L_bobj_crv_ParamsMinTooltip = "\u8ACB\u6307\u5B9A\u5927\u65BC\u6216\u7B49\u65BC %2 \u7684 %1 \u503C\u3002";
var L_bobj_crv_ParamsMaxTooltip = "\u8ACB\u6307\u5B9A\u5C0F\u65BC\u6216\u7B49\u65BC %2 \u7684 %1 \u503C\u3002";
var L_bobj_crv_ParamsMinAndMaxTooltip = "\u8ACB\u6307\u5B9A\u4ECB\u65BC %2 \u548C %3 \u4E4B\u9593\u7684 %1 \u503C\u3002";
var L_bobj_crv_ParamsStringMinOrMaxTooltip = "\u9019\u500B\u6B04\u4F4D\u7684 %1 \u9577\u5EA6\u662F %2\u3002";
var L_bobj_crv_ParamsStringMinAndMaxTooltip = "\u9019\u500B\u503C\u7684\u9577\u5EA6\u5FC5\u9808\u4ECB\u65BC %1 \u5230 %2 \u500B\u5B57\u5143\u4E4B\u9593\u3002";
var L_bobj_crv_ParamsYearToken = "yyyy";
var L_bobj_crv_ParamsMonthToken = "mm";
var L_bobj_crv_ParamsDayToken = "dd";
var L_bobj_crv_ParamsReadOnly = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u552F\u8B80]\u3002";
var L_bobj_crv_ParamsNoValue = "\u6C92\u6709\u503C";
var L_bobj_crv_ParamsDuplicateValue = "\u4E0D\u5141\u8A31\u91CD\u8907\u7684\u503C\u3002";
var L_bobj_crv_ParamsEnterOptional = "\u8F38\u5165 %1 (\u9078\u64C7\u6027)";
var L_bobj_crv_ParamsNoneSelected= "(\u672A\u9078\u64C7\u4EFB\u4F55\u9805\u76EE)";
var L_bobj_crv_ParamsClearValues= "\u6E05\u9664\u503C";
var L_bobj_crv_ParamsMoreValues= "%1 \u66F4\u591A\u503C...";
var L_bobj_crv_ParamsMoreValue= "%1 \u66F4\u591A\u503C...";
var L_bobj_crv_Error = "\u932F\u8AA4";
var L_bobj_crv_OK = "\u78BA\u5B9A";
var L_bobj_crv_Cancel = "\u53D6\u6D88";
var L_bobj_crv_showDetails = "\u986F\u793A\u8A73\u7D30\u8CC7\u6599";
var L_bobj_crv_hideDetails = "\u96B1\u85CF\u8A73\u7D30\u8CC7\u6599";
var L_bobj_crv_RequestError = "\u7121\u6CD5\u8655\u7406\u60A8\u7684\u8981\u6C42";
var L_bobj_crv_ServletMissing = "\u6AA2\u8996\u5668\u7121\u6CD5\u8207\u8655\u7406\u975E\u540C\u6B65\u8981\u6C42\u7684 CrystalReportViewerServlet \u9023\u7DDA\u3002\n\u8ACB\u78BA\u5B9A\u5DF2\u5728\u61C9\u7528\u7A0B\u5F0F\u7684 web.xml \u6A94\u6848\u4E2D\u9069\u7576\u5BA3\u544A Servlet \u548C Servlet \u5C0D\u61C9\u3002";
var L_bobj_crv_FlashRequired = "\u6B64\u5167\u5BB9\u9700\u8981 Adobe Flash Player 9 \u6216\u4EE5\u4E0A\u7248\u672C\u3002{0}\u8ACB\u6309\u4E00\u4E0B\u9019\u88E1\u9032\u884C\u5B89\u88DD";
var L_bobj_crv_ReadOnlyInPanel= "\u7121\u6CD5\u5728\u9762\u677F\u4E2D\u7DE8\u8F2F\u6B64\u53C3\u6578\u3002\u958B\u555F\u9032\u968E\u63D0\u793A\u5C0D\u8A71\u65B9\u584A\u4EE5\u4FEE\u6539\u5176\u503C";

var L_bobj_crv_Tree_Drilldown_Node = "\u5C0D\u7BC0\u9EDE %1 \u64F7\u53D6\u7D30\u76EE";

var L_bobj_crv_ReportProcessingMessage = "\u6587\u4EF6\u8655\u7406\u4E2D\uFF0C\u8ACB\u7A0D\u5019\u3002";
var L_bobj_crv_PrintControlProcessingMessage = "\u6B63\u5728\u8F09\u5165 Crystal Reports \u5217\u5370\u63A7\u5236\uFF0C\u8ACB\u7A0D\u5019\u3002";

var L_bobj_crv_SundayShort = "\u9031\u65E5";
var L_bobj_crv_MondayShort = "\u9031\u4E00";
var L_bobj_crv_TuesdayShort = "\u9031\u4E8C";
var L_bobj_crv_WednesdayShort = "\u9031\u4E09";
var L_bobj_crv_ThursdayShort = "\u9031\u56DB";
var L_bobj_crv_FridayShort = "\u9031\u4E94";
var L_bobj_crv_SaturdayShort = "\u9031\u516D";

var L_bobj_crv_Minimum = "\u6700\u5C0F";
var L_bobj_crv_Maximum = "\u6700\u5927";

var L_bobj_crv_Date = "\u65E5\u671F";
var L_bobj_crv_Time = "\u6642\u9593";
var L_bobj_crv_DateTime = "\u65E5\u671F\u6642\u9593";
var L_bobj_crv_Boolean = "\u5E03\u6797";
var L_bobj_crv_Number = "\u6578\u5B57";
var L_bobj_crv_Text = "\u6587\u5B57";

var L_bobj_crv_InteractiveParam_NoAjax = "\u60A8\u6240\u4F7F\u7528\u7684 Web \u700F\u89BD\u5668\u4E26\u672A\u8A2D\u5B9A\u6210\u986F\u793A\u53C3\u6578\u9762\u677F\u3002";
var L_bobj_crv_AdvancedDialog_NoAjax= "\u6AA2\u8996\u5668\u7121\u6CD5\u958B\u555F\u9032\u968E\u63D0\u793A\u5C0D\u8A71\u65B9\u584A\u3002";

var L_bobj_crv_EnableAjax= "\u8ACB\u9023\u7D61\u60A8\u7684\u7CFB\u7D71\u7BA1\u7406\u54E1\u4EE5\u555F\u7528\u975E\u540C\u6B65\u8981\u6C42\u3002";

var L_bobj_crv_LastRefreshed = "\u4E0A\u6B21\u91CD\u65B0\u6574\u7406";

var L_bobj_crv_Collapse = "\u6536\u5408";

var L_bobj_crv_CatalystTip = "\u7DDA\u4E0A\u8CC7\u6E90";
// <script>
/*
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: labels.js


=============================================================
*/

_default="預設"
_black="黑色"
_brown="棕色"
_oliveGreen="橄欖綠"
_darkGreen="深橄欖綠"
_darkTeal="深藍綠"
_navyBlue="海軍藍"
_indigo="靛藍色"
_darkGray="深灰色"
_darkRed="深紅色"
_orange="橙色"
_darkYellow="深黃色"
_green="綠色"
_teal="藍綠色"
_blue="藍色"
_blueGray="藍灰色"
_mediumGray="中灰色"
_red="紅色"
_lightOrange="淺橙色"
_lime="黃綠色"
_seaGreen="海藻綠"
_aqua="水藍色"
_lightBlue="淺藍色"
_violet="紫色"
_gray="灰色"
_magenta="洋紅色"
_gold="金色"
_yellow="黃色"
_brightGreen="亮綠色"
_cyan="青色"
_skyBlue="天藍色"
_plum="梅紅色"
_lightGray="淺灰色"
_pink="粉紅色"
_tan="黃褐色"
_lightYellow="淺黃色"
_lightGreen="淺綠色"
_lightTurquoise="淺綠藍"
_paleBlue="淡藍色"
_lavender="淡紫色"
_white="白色"
_lastUsed="上一次使用:"
_moreColors="其他色彩..."

_month=new Array

_month[0]="一月"
_month[1]="二月"
_month[2]="三月"
_month[3]="四月"
_month[4]="五月"
_month[5]="六月"
_month[6]="七月"
_month[7]="八月"
_month[8]="九月"
_month[9]="十月"
_month[10]="十一月"
_month[11]="十二月"

_day=new Array
_day[0]="週日"
_day[1]="週一"
_day[2]="週二"
_day[3]="週三"
_day[4]="週四"
_day[5]="週五"
_day[6]="週六"

_today="今天"

_AM="上午"
_PM="下午"

_closeDialog="關閉視窗"

_lstMoveUpLab="往上移動"
_lstMoveDownLab="往下移動"
_lstMoveLeftLab="往左移動" 
_lstMoveRightLab="往右移動"
_lstNewNodeLab="新增巢狀篩選器"
_lstAndLabel="AND"
_lstOrLabel="OR"
_lstSelectedLabel="已選取"
_lstQuickFilterLab="新增快速篩選器"

_openMenu="按一下這裡以存取{0}選項"
_openCalendarLab="開啟日曆"

_scroll_first_tab="捲動至第一個標籤"
_scroll_previous_tab="捲動至上一個標籤"
_scroll_next_tab="捲動至下一個標籤"
_scroll_last_tab="捲動至最後一個標籤"

_expandedLab="展開的"
_collapsedLab="收合的"
_selectedLab="已選取"

_expandNode="展開節點 %1"
_collapseNode="收合節點 %1"

_checkedPromptLab="已設定"
_nocheckedPromptLab="未設定"
_selectionPromptLab="值等於"
_noselectionPromptLab="沒有值"

_lovTextFieldLab="在此輸入值"
_lovCalendarLab="在此輸入日期"
_lovPrevChunkLab="移至上一個區塊"
_lovNextChunkLab="移至下一個區塊"
_lovComboChunkLab="區塊"
_lovRefreshLab="重新整理"
_lovSearchFieldLab="在此輸入要搜尋的文字"
_lovSearchLab="搜尋"
_lovNormalLab="一般"
_lovMatchCase="大小寫須相符"
_lovRefreshValuesLab="重新整理值"

_calendarNextMonthLab="移至下個月"
_calendarPrevMonthLab="移至上個月"
_calendarNextYearLab="移至後一年"
_calendarPrevYearLab="移至前一年"
_calendarSelectionLab="選取的日 "

_menuCheckLab="已檢查"
_menuDisableLab="已停用"
	
_level="層級"
_closeTab="關閉索引標籤"
_of="/"

_RGBTxtBegin= "RGB("
_RGBTxtEnd= ")"

_helpLab="說明"

_waitTitleLab="請稍候"
_cancelButtonLab="取消"

_modifiers= new Array
_modifiers[0]="Ctrl+"
_modifiers[1]="Shift+"
_modifiers[2]="Alt+"

_bordersMoreColorsLabel="其他框線..."
_bordersTooltip=new Array
_bordersTooltip[0]="無框線"
_bordersTooltip[1]="左框線"
_bordersTooltip[2]="右框線"
_bordersTooltip[3]="下框線"
_bordersTooltip[4]="適中下框線"
_bordersTooltip[5]="粗下框線"
_bordersTooltip[6]="上下框線"
_bordersTooltip[7]="上方和適中下框線"
_bordersTooltip[8]="上方和粗下框線"
_bordersTooltip[9]="所有框線"
_bordersTooltip[10]="所有適中框線"
_bordersTooltip[11]="所有粗框線"/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u4ECA\u5929";
var L_January   = "\u4E00\u6708";
var L_February  = "\u4E8C\u6708";
var L_March     = "\u4E09\u6708";
var L_April     = "\u56DB\u6708";
var L_May       = "\u4E94\u6708";
var L_June      = "\u516D\u6708";
var L_July      = "\u4E03\u6708";
var L_August    = "\u516B\u6708";
var L_September = "\u4E5D\u6708";
var L_October   = "\u5341\u6708";
var L_November  = "\u5341\u4E00\u6708";
var L_December  = "\u5341\u4E8C\u6708";
var L_Su        = "\u9031\u65E5";
var L_Mo        = "\u9031\u4E00";
var L_Tu        = "\u9031\u4E8C";
var L_We        = "\u9031\u4E09";
var L_Th        = "\u9031\u56DB";
var L_Fr        = "\u9031\u4E94";
var L_Sa        = "\u9031\u516D";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u6578\u5B57]\uFF0C\u53EA\u80FD\u5305\u542B\u8CA0\u865F\u3001\u6578\u5B57 (\"0-9\")\u3001\u6578\u5B57\u5206\u7D44\u7B26\u865F\u6216\u5C0F\u6578\u9EDE\u7B26\u865F\u3002\u8ACB\u66F4\u6B63\u6240\u8F38\u5165\u7684\u53C3\u6578\u503C\u3002";
var L_BadCurrency   = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u8CA8\u5E63]\uFF0C\u53EA\u80FD\u5305\u542B\u8CA0\u865F\u3001\u6578\u5B57 (\"0-9\")\u3001\u6578\u5B57\u5206\u7D44\u7B26\u865F\u6216\u5C0F\u6578\u9EDE\u7B26\u865F\u3002\u8ACB\u66F4\u6B63\u6240\u8F38\u5165\u7684\u53C3\u6578\u503C\u3002";
var L_BadDate       = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u65E5\u671F]\uFF0C\u4E14\u683C\u5F0F\u5FC5\u9808\u70BA \"%1\"\uFF0C\u5176\u4E2D \"yyyy\" \u70BA\u56DB\u4F4D\u6578\u7684\u5E74\u4EFD\uFF0C\"mm\" \u70BA\u6708\u4EFD (\u4F8B\u5982\uFF0C\u4E00\u6708 = 1)\uFF0C\u800C \"dd\" \u5247\u70BA\u6307\u5B9A\u6708\u4EFD\u4E2D\u7684\u65E5\u6578\u3002";
var L_BadDateTime   = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u65E5\u671F\u6642\u9593]\uFF0C\u4E14\u6B63\u78BA\u7684\u683C\u5F0F\u70BA \"%1 hh:mm:ss\"\u3002\"yyyy\" \u70BA\u56DB\u4F4D\u6578\u7684\u5E74\u4EFD\uFF0C\"mm\" \u70BA\u6708\u4EFD (\u4F8B\u5982\uFF0C\u4E00\u6708 = 1)\uFF0C\"dd\" \u70BA\u7576\u6708\u7684\u65E5\u671F\uFF0C\"hh\" \u70BA 24 \u6642\u5236\u7684\u6642\u6578\uFF0C\"mm\" \u70BA\u5206\u9418\u6578\uFF0C\u800C \"ss\" \u5247\u70BA\u79D2\u6578\u3002";
var L_BadTime       = "\u6B64\u53C3\u6578\u7684\u985E\u578B\u70BA [\u6642\u9593]\uFF0C\u4E14\u683C\u5F0F\u5FC5\u9808\u70BA \"hh:mm:ss\"\uFF0C\u5176\u4E2D \"hh\" \u70BA 24 \u6642\u5236\u7684\u6642\u6578\uFF0C\"mm\" \u70BA\u5206\u9418\u6578\uFF0C\u800C \"ss\" \u5247\u70BA\u79D2\u6578\u3002";
var L_NoValue       = "\u6C92\u6709\u503C";
var L_BadValue      = "\u82E5\u8981\u8A2D\u5B9A\u6210 [\u6C92\u6709\u503C]\uFF0C\u5FC5\u9808\u540C\u6642\u5C07 [\u5F9E] \u548C [\u5230] \u7684\u503C\u8A2D\u6210 [\u6C92\u6709\u503C]\u3002";
var L_BadBound      = "[\u7121\u4E0B\u9650] \u4E0D\u80FD\u8207 [\u7121\u4E0A\u9650] \u4E00\u8D77\u8A2D\u5B9A\u3002";
var L_NoValueAlready = "\u6B64\u53C3\u6578\u5DF2\u7D93\u8A2D\u5B9A\u6210 [\u6C92\u6709\u503C]\u3002\u5728\u52A0\u5165\u5176\u4ED6\u503C\u4E4B\u524D\uFF0C\u8ACB\u5148\u79FB\u9664 [\u6C92\u6709\u503C]\u3002";
var L_RangeError    = "\u7BC4\u570D\u8D77\u9EDE\u4E0D\u5F97\u5927\u65BC\u7BC4\u570D\u7D42\u9EDE\u3002";
var L_NoDateEntered = "\u5FC5\u9808\u8F38\u5165\u65E5\u671F\u3002";
var L_Empty         = "\u8ACB\u8F38\u5165\u503C\u3002";

// Strings for filter dialog
var L_closeDialog="\u95DC\u9589\u8996\u7A97";

var L_SetFilter = "\u8A2D\u5B9A\u7BE9\u9078";
var L_OK        = "\u78BA\u5B9A";
var L_Cancel    = "\u53D6\u6D88";

 /* Crystal Decisions Confidential Proprietary Information */
