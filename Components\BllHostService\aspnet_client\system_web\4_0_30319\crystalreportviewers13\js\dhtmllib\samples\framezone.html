<html>
	<head>
		<script language="javascript" src="../dom.js"></script>
		<script language="javascript">
			var skin=parent._skin?parent._skin:"skin_standard";
			var lang=parent._lang?parent._lang:"en";
			
			initDom("../images/"+skin+"/",lang)
			styleSheet()
		</script>
			
		<script language="javascript">
		
			var isLoaded=false

			function resizeCB()
			{
				if (isLoaded)
				{
					var w=winWidth()
					var h=winHeight()
				
					frameZoneResize.resize(Math.max(0,w-60),Math.max(0,h-280))
				}
			}
		
			function loadCB()
			{
				frameZone.init()
				frameZoneEnfonce.init()		
				frameZoneResize.init()
						
				isLoaded=true
				resizeCB()
			}

			frameZone=newFrameZoneWidget("frameZone1",300,100)
			frameZoneEnfonce=newFrameZoneWidget("frameZone2",300,100,true)

			frameZoneResize=newFrameZoneWidget("frameZone3",300,100)
			

		</script>
	</head>
	<body onload="loadCB()" onresize="resizeCB()">
	<div class="insetBorder"><div class="dialogzone" style="padding:15px">
		<table><tr align="left"><td>
		<script language="javascript">frameZone.begin()</script>
		Premier exemple fait par Minh
		<script language="javascript">frameZone.end()</script>
		</td></tr><tr align="left"><td>
		<script language="javascript">frameZoneEnfonce.begin()</script>
		date: 23 janvier 2004
		<script language="javascript">frameZoneEnfonce.end()</script>

		</td></tr><tr><td align="center">
		<script language="javascript">frameZoneResize.begin()</script>
		Zone Retaillable
		<script language="javascript">frameZoneResize.end()</script>


		</td></tr></table>
	</div>
	</body>
	
</html>