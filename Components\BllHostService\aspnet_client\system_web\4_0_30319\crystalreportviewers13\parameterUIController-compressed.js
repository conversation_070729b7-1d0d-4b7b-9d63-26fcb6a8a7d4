if(typeof bobj=="undefined"){bobj={}}bobj.Colors={BLACK:"#000000",GRAY:"#a5a5a5"};bobj.crv.params.newTextField=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),cleanValue:"",width:"100%",maxChar:null,tooltip:null,disabled:false,editable:true,password:false,focusCB:null,blurCB:null,changeCB:null,keyUpCB:null,enterCB:null,foreColor:"black",isTextItalic:false,canOpenAdvDialog:false},A);var B=newTextFieldWidget(A.id,A.changeCB,A.maxChar,A.keyUpCB,A.enterCB,true,A.tooltip,null,A.focusCB,A.blurCB);B.widgetType="TextField";bobj.fillIn(B,A);B.disabled=A.disabled;B.width=A.width;MochiKit.Base.update(B,bobj.crv.params.TextField);if(A.cleanValue){B.setValue(A.cleanValue)}return B};bobj.crv.params.TextField={setForeColor:function(A){this.foreColor=A;if(this.css){this.css.color=A}},setTextItalic:function(A){this.isTextItalic=A;if(this.css){this.css.fontStyle=A?"italic":""}},setTabDisabled:function(A){bobj.disableTabbingKey(this.layer,A)},eraseHelpTxt:MochiKit.Base.noop,getHTML:function(){var D={width:bobj.unitValue(this.width)};var C=MochiKit.Base.isIE();var B="iactTextField";var A={type:this.password?"password":"text",name:this.id,id:this.id,maxLength:this.maxChar,style:D,"class":B,oncontextmenu:"event.cancelBubble=true;return true",onfocus:"TextFieldWidget_focus(this)",onblur:"TextFieldWidget_blur(this)",onchange:"TextFieldWidget_changeCB(event, this)",onkeydown:"return TextFieldWidget_keyDownCB(event, this);",onkeyup:"return TextFieldWidget_keyUpCB(event, this);",onkeypress:"return TextFieldWidget_keyPressCB(event, this);",ondragstart:"event.cancelBubble=true; return true;",onselectstart:"event.cancelBubble=true; return true;"};if(this.disabled){A.disabled="disabled"}if(this.isTextItalic){D["font-style"]="italic"}D.color=this.foreColor;if(!this.editable){A.readonly="readonly";if(this.canOpenAdvDialog){D.cursor="pointer"}else{D.cursor="default"}}if(this.tooltip){A.title=this.tooltip.replace(/"/g,"&quot;")}return bobj.html.INPUT(A)},reset:function(A){this.value=A;this.cleanValue=A;this.setValue(A)},setValue:function(A){TextFieldWidget_setValue.call(this,A)},setCleanValue:function(A){this.cleanValue=A}};bobj.crv.params.newTextCombo=function(A){var D=MochiKit.Base.update;var B=bobj.crv.params;A=D({id:bobj.uniqueId(),width:"100%",maxChar:null,tooltip:null,disabled:false,editable:false,changeCB:null,enterCB:null,keyUpCB:null,isTextItalic:false},A);var C=newTextComboWidget(A.id,A.maxChar,A.tooltip,null,A.changeCB,null,null,null);C.widgetType="TextCombo";bobj.fillIn(C,A);C.width=A.width;C.init_TextCombo=C.init;D(C,B.TextCombo);C._createTextField();C._createArrow();C.arrow.dy+=2;C.arrow.disDy+=2;return C};bobj.crv.params.TextCombo={setTextItalic:function(A){if(this.text){this.text.setTextItalic(A)}},setForeColor:function(A){if(this.text){this.text.setForeColor(A)}},setTooltip:function(A){if(this.text){this.text.setTooltip(A)}},setTabDisabled:function(A){if(this.text){this.text.setTabDisabled(A)}if(this.arrow){bobj.disableTabbingKey(this.arrow.layer,A)}},setMenu:function(A){this.menu=A},init:function(){this.init_TextCombo();this.arrowContainer=getLayer(this.id+"_arrowCtn");if(this.arrow){this.arrow.layer.onfocus=IconWidget_realOverCB;this.arrow.layer.onblur=IconWidget_realOutCB}this.text.setValue(this.cleanValue)},toggleMenu:function(){var B=this.menu;B.parIcon=this;var A=!B.isShown();B.show(A);if(A){B.valueSelect(this.text.getValue()+"")}},_createArrow:function(){var A=_openMenu.replace("{0}",this.tooltip?this.tooltip:"");this.arrow=newIconWidget(this.id+"arrow_",bobj.skinUri("menus.gif"),bobj.bindFunctionToObject(this.toggleMenu,this),null,A,7,12,0,83,0,99);this.arrow.setClasses("iconnocheck","combobtnhover","combobtnhover","combobtnhover");this.arrow.par=this},_createTextField:function(){this.text=bobj.crv.params.newTextField({id:this.id+"_text",cleanValue:this.cleanValue,width:"100%",maxChar:null,tooltip:this.tooltip,disabled:false,editable:this.editable,password:false,focusCB:this.focusCB,blurCB:this.blurCB,keyUpCB:bobj.bindFunctionToObject(this._onKeyUp,this),enterCB:this.enterCB,foreColor:this.foreColor,isTextItalic:this.isTextItalic})},getHTML:function(){var D=bobj.html;var B="iactTextComboArrow";var A={};A.right="0px";if(MochiKit.Base.isIE()){A.height="18px"}else{A.height="16px"}var C=D.DIV({id:this.id,style:{width:"100%",position:"relative"}},D.DIV({style:{position:"relative"},"class":"iactTextComboTextField"},this.text.getHTML()),D.DIV({"class":B,id:this.id+"_arrowCtn",style:A},this.arrow.getHTML()));return C},reset:function(A){this.text.reset(A)},setValue:function(A){this.text.setValue(A)},setCleanValue:function(A){this.text.setCleanValue(A)},selectItem:function(A){if(A){this.val=A.value;this.text.setValue(A.value,true);this.menu.select(A.index)}},getValue:function(){return this.text.getValue()},_onKeyUp:function(A){var B=this.text.getValue();if(this.keyUpCB){this.keyUpCB(A)}}};bobj.crv.params.newScrollMenuWidget=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),originalValues:[],hasProperWidth:false,hasValueList:false,maxVisibleItems:10,openAdvDialogCB:null,maxNumParameterDefaultValues:null},A);var C=(A.originalValues.length>=A.maxVisibleItems)?A.maxVisibleItems:A.originalValues.length;if(C===1){C++}var B=newScrollMenuWidget("menu_"+A.id,bobj.crv.params.ScrollMenuWidget.onChange,false,null,C,null,null,null,false,"","",null,null);B.oldShow=B.show;MochiKit.Base.update(B,A,bobj.crv.params.ScrollMenuWidget);return B};bobj.crv.params.ScrollMenuWidget={onChange:function(){var B=this.parIcon;var A=this.getSelection();if(A){if(this.maxNumParameterDefaultValues&&A.index==this.maxNumParameterDefaultValues){if(this.openAdvDialogCB){this.openAdvDialogCB();this.clearSelection()}}else{B.val=A.value;B.text.setValue(A.value)}}else{B.val=null;B.text.setValue("")}if(B.changeCB){B.changeCB()}},getPosition:function(){if(this.parIcon===null){return }var C=this.parIcon.layer;var B=MochiKit.Style.getElementDimensions;var A=getPosScrolled(C);var E=A.x+2;var D=A.y+B(C).h+3;if(MochiKit.Base.isIE()){E-=1;if(bobj.isQuirksMode()){D-=2}}return{x:E,y:D}},setProperWidth:function(){if(this.hasProperWidth===false){this.css.display="block";this.orginalWidth=this.layer.offsetWidth;this.css.display="none";this.hasProperWidth=true}},setValueList:function(){if(this.hasValueList===false){this.hasValueList=true;var B=this.originalValues;for(var C=0,A=B.length;C<A;C++){this.add(B[C],B[C],false)}}},setFocus:function(A){if(A){var B=bobj.bindFunctionToObject(this.list.focus,this.list);setTimeout(B,300)}else{if(this.parIcon.selected===true){this.parIcon.arrow.focus()}}},show:function(A){if(this.layer===null){this.justInTimeInit()}if(this.hasValueList===false){this.setValueList()}if(this.parIcon===null){return }if(this.hasProperWidth===false){this.setProperWidth()}if(this.parIcon&&this.parIcon.layer){var B=this.parIcon.layer;if(B.clientWidth>this.orginalWidth){this.css.width=B.clientWidth+"px";this.list.css.width=B.clientWidth+"px"}else{this.css.width=this.orginalWidth+"px";this.list.css.width=this.orginalWidth+"px"}}var C=this.getPosition();this.oldShow(A,C.x,C.y);this.setFocus(A)}};bobj.crv.params.newRangeField=function(A){return new bobj.crv.params.RangeField(A)};bobj.crv.params.RangeField=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),cleanValue:{},foreColor:"black",isTextItalic:false,tooltip:""},A);this.widgetType="RangeField";this.value=A.cleanValue;bobj.fillIn(this,A)};bobj.crv.params.RangeField.prototype={setTabDisabled:function(B){if(this.layer){var D=this.layer.getElementsByTagName("TD");for(var C=0,A=D.length;C<A;C++){bobj.disableTabbingKey(D[C],B)}}},setTooltip:function(A){if(this.layer){this.layer.title=A}},setForeColor:function(A){if(this.layer){this.layer.style.color=A}},setTextItalic:function(A){if(this.layer){this.layer.style.fontStyle=A?"italic":""}},getValueHTML:function(C,A){C=C?C:"&nbsp;";var B={"text-align":A?"right":"left"};return bobj.html.TD({style:B,tabIndex:0},bobj.html.SPAN(null,C))},isValueValidRange:function(){return this.value!=null&&this.value.lowerBound!=null&&this.value.upperBound!=null},getHTML:function(){var B="";var A=bobj.html;if(this.isValueValidRange()){B=A.TR(null,this.getValueHTML(this.getValue().lowerBound.value,true),this.getMiddleImageHTML(),this.getValueHTML(this.getValue().upperBound.value,false))}else{B=A.TR(null,this.getValueHTML(this.getValue(),false))}return A.TABLE({id:this.id,"class":"iactRangeFieldTable",title:this.tooltip,style:{color:this.foreColor,"font-style":this.isTextItalic?"italic":""}},B)},getMiddleImageHTML:function(){var E="";var C="";var A="images/line.gif";switch(this.getValue().lowerBound.type){case bobj.crv.params.RangeBoundTypes.EXCLUSIVE:E="images/hollowCircle.gif";break;case bobj.crv.params.RangeBoundTypes.INCLUSIVE:E="images/filledCircle.gif";break;case bobj.crv.params.RangeBoundTypes.UNBOUNDED:E="images/leftTriangle.gif";break}switch(this.getValue().upperBound.type){case bobj.crv.params.RangeBoundTypes.EXCLUSIVE:C="images/hollowCircle.gif";break;case bobj.crv.params.RangeBoundTypes.INCLUSIVE:C="images/filledCircle.gif";break;case bobj.crv.params.RangeBoundTypes.UNBOUNDED:C="images/rightTriangle.gif";break}var B={"vertical-align":"middle"};var D=bobj.html.IMG;return bobj.html.TD({style:B},D({src:bobj.crvUri(E)}),D({src:bobj.crvUri(A)}),D({src:bobj.crvUri(C)}))},init:function(){this.layer=getLayer(this.id)},getLowerBoundValueWidth:function(){if(!this.isValueValidRange()){return 0}var C=this.getValue().lowerBound.value;var A=MochiKit.Style.computedStyle(this.layer,"fontFamily");var B=MochiKit.Style.computedStyle(this.layer,"fontSize");if(!A){A="arial , sans-serif"}if(!B){B="12px"}return bobj.getStringWidth(C,A,B)},getLowerBoundTD:function(){if(this.layer){return this.layer.getElementsByTagName("TD")[0]}return null},setLowerBoundValueWidth:function(A){if(this.getLowerBoundTD()){this.getLowerBoundTD().style.width=A+"px"}},reset:function(A){this.value=A;this.cleanValue=A;this.updateUI()},updateUI:function(){var A=this.layer.parentNode;MochiKit.DOM.removeElement(this.layer);append2(A,this.getHTML());this.init()},setValue:function(A){this.value=A;this.updateUI()},setCleanValue:function(A){this.cleanValue=A},getValue:function(){return this.value}};bobj.crv.params.ParameterInfoRow=function(A){this.layer=null;this.parentId=A;this.id=bobj.uniqueId()};bobj.crv.params.ParameterInfoRow.prototype={setTabDisabled:function(A){if(this.layer){bobj.disableTabbingKey(this.layer,A)}},init:function(){var A=getLayer(this.parentId);if(A){append2(A,this.getHTML());this.layer=getLayer(this.id)}if(this.layer){MochiKit.Signal.connect(this.layer,"onclick",this,"_onClick");MochiKit.Signal.connect(this.layer,"onkeydown",this,"_onKeyDown")}},getHTML:function(){return bobj.html.DIV({"class":"parameterInfoRow",id:this.id,tabIndex:"0"})},setText:function(A){if(!this.layer){this.init()}this.layer.innerHTML=A},setVisible:function(A){if(A){if(!this.layer){this.init()}this.shiftToLastRow();this.layer.style.display="block"}else{if(this.layer){this.layer.style.display="none"}}},shiftToLastRow:function(){var A=getLayer(this.parentId);if(this.layer&&A){A.removeChild(this.layer);A.appendChild(this.layer)}},_onClick:function(A){MochiKit.Signal.signal(this,"switch");A.stop()},_onKeyDown:function(A){if(A&&A.key()&&A.key().code==13){MochiKit.Signal.signal(this,"switch");A.stop()}}};bobj.crv.params.newParameterValueRow=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),value:"",defaultValues:null,isReadOnlyParam:true,canChangeOnPanel:false,isRangeValue:false,allowCustom:false,isPassword:false,calendarProperties:{displayValueFormat:"",isTimeShown:false,hasButton:false,iconUrl:"",clickCB:null},changeCB:null,enterCB:null,defaultValuesMenu:null,tooltip:null,canOpenAdvDialog:false},A);var B=newWidget(A.id);B.widgetType="ParameterValueRow";B._prevValueString=A.value;B._warning=null;bobj.fillIn(B,A);MochiKit.Base.update(B,bobj.crv.params.ParameterValueRow);return B};bobj.crv.params.ParameterValueRow={setTabDisabled:function(A){if(this._valueWidget){this._valueWidget.setTabDisabled(A)}if(this._calendarButton){bobj.disableTabbingKey(this._calendarButton.layer,A)}},reset:function(A){this.value=A;this._prevValueString=A;this.setWarning(null);MochiKit.DOM.removeElementClass(this.layer,"hasError");if(this._valueWidget){this._valueWidget.reset(A)}},init:function(){Widget_init.call(this);this._valueWidget.init();if(this._calendarButton){this._calendarButton.init()}this._valueCtn=getLayer(this.id+"_vc");this._btnCtn=getLayer(this.id+"_bc");this._valBtnCtn=getLayer(this.id+"_vab");if(MochiKit.Base.isIE()){var A=parseInt(MochiKit.Style.computedStyle(this._valueCtn,"margin-right"),10);if(bobj.isNumber(A)){this._valueWidget.layer.style.marginRight=(-1*A)+"px"}}},calendarSetValue:function(A){this.setValue(A);this.changeCB()},getHTML:function(){if(!this._valueWidget){this._valueWidget=this._getValueWidget()}if(this.calendarProperties.hasButton&&!this._calendarButton){var C=bobj.bindFunctionToObject(this.getValue,this);var B=bobj.bindFunctionToObject(this.calendarSetValue,this);this._calendarButton=bobj.crv.params.newCalendarButton({calendarProperties:this.calendarProperties,getValueCB:C,setValueCB:B})}var D=bobj.html.DIV;var E=bobj.html.IMG;var A=" iactParamRow";if(this.canChangeOnPanel){A+=" editable"}else{A+=" readOnly"}if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){A+=" iactParamRowIE"}return D({id:this.id,"class":A},this.calendarProperties.hasButton?this._getValueAndCalendarHTML():this._getValueHTML())},_getValueHTML:function(){var B=bobj.html.DIV;var A={};if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){A.position="absolute";A.top="0px";A.left="0px"}return B({id:this.id+"_vc","class":"iactParamValue",style:A},this._valueWidget.getHTML())},_getValueAndCalendarHTML:function(){var C={};if(MochiKit.Base.isIE()&&bobj.isQuirksMode()){C.width="100%"}var D=bobj.html.DIV;var A=(this._valueWidget.widgetType=="TextCombo")?16:0;A+="px";var B=D({id:this.id+"_vab",style:C,"class":"iactParamValueAndButton"},this._getValueHTML(),D({id:this.id+"_bc","class":"iactValueIcon",style:{position:"absolute",right:A,top:"0",cursor:_hand}},this._calendarButton.getHTML()));return B},getNewValueWidgetConstructor:function(){var A=this.defaultValuesMenu!==null&&!this.isReadOnlyParam&&this.canChangeOnPanel;if(this.isRangeValue){return bobj.crv.params.newRangeField}else{if(A){return bobj.crv.params.newTextCombo}else{return bobj.crv.params.newTextField}}},getNewValueWidgetArgs:function(){var A=this.allowCustom&&this.canChangeOnPanel;return{password:this.isPassword,cleanValue:this.value,editable:A,enterCB:bobj.bindFunctionToObject(this._onEnterPress,this),keyUpCB:A?bobj.bindFunctionToObject(this._onKeyUp,this):null,tooltip:this.tooltip,foreColor:this.isReadOnlyParam?bobj.Colors.GRAY:bobj.Colors.BLACK,focusCB:bobj.bindFunctionToObject(this.onFocus,this),blurCB:bobj.bindFunctionToObject(this.onBlur,this),canOpenAdvDialog:this.canOpenAdvDialog}},_getValueWidget:function(){var A=this.getNewValueWidgetConstructor();var B=A(this.getNewValueWidgetArgs());var C=this.defaultValuesMenu!==null&&!this.isReadOnlyParam&&this.canChangeOnPanel;if(C){B.setMenu(this.defaultValuesMenu);B.changeCB=bobj.bindFunctionToObject(this._onChange,this)}return B},onFocus:function(){this.refreshWarningPopup();MochiKit.DOM.removeElementClass(this.layer,"hasError")},refreshWarningPopup:function(){if(this._warning){var G=getPosScrolled(this.layer);var B=MochiKit.Style.computedStyle(this.layer,"fontFamily");var E=MochiKit.Style.computedStyle(this.layer,"fontSize");var A=bobj.getStringWidth(this.getValue(),B,E);var D=this.layer.offsetWidth<A?this.layer.offsetWidth:A;var C=33;bobj.crv.WarningPopup.getInstance().show(this._warning.message,G.x+D,G.y+C)}else{bobj.crv.WarningPopup.getInstance().hide();MochiKit.DOM.removeElementClass(this.layer,"hasError")}var F=this._warning?this.tooltip+this._warning.message:this.tooltip;if(this._valueWidget){this._valueWidget.setTooltip(F)}},onBlur:function(){if(this._warning){bobj.crv.WarningPopup.getInstance().hide();MochiKit.DOM.addElementClass(this.layer,"hasError")}},getValue:function(){return this.value},setValue:function(A){this.value=A;if(this._valueWidget){this._valueWidget.setValue(A)}},setCleanValue:function(A){if(this._valueWidget){this._valueWidget.setCleanValue(A)}},focus:function(){if(this._valueWidget.widgetType=="TextCombo"){this._valueWidget.text.focus()}else{this._valueWidget.focus()}},setWarning:function(A){this._warning=A;this.refreshWarningPopup()},getWarning:function(){return this._warning},resize:function(A,B){bobj.setOuterSize(this.layer,A,B)},deleteValue:function(){this._valueWidget.setValue("",true)},_onKeyUp:function(D){var C=new MochiKit.Signal.Event(src,D);var B=C.key().string;var A=this._valueWidget.getValue();switch(B){case"KEY_ESCAPE":this._valueWidget.setValue(this._valueWidget.cleanValue);this._onChange();break;case"KEY_ARROW_LEFT":case"KEY_ARROW_RIGHT":case"KEY_HOME":case"KEY_END":case"KEY_TAB":break;default:if(A!==this._prevValueString){this._onChange();this._prevValueString=A}break}},deleteValue:function(){this._valueWidget.setValue("",true)},_onChange:function(){this.value=this._valueWidget.getValue();if(this.changeCB){this.changeCB()}},_onEnterPress:function(){if(this.enterCB){this.enterCB()}}};bobj.crv.params.newCalendarButton=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),calendarProperties:null,getValueCB:null,setValueCB:null,calendarSignals:{okSignal:null,hideSignal:null,cancelSignal:null}},A);if(A.getValueCB==null||A.setValueCB==null||A.calendarProperties==null){throw"InvalidArgumentException"}var B=newIconWidget(A.id,A.calendarProperties.iconUrl,null,null,L_bobj_crv_ParamsCalBtn,14,14,0,0,0,0);B.setClasses("","","","iconcheckhover");bobj.fillIn(B,A);B.margin=0;B.oldInit=B.init;MochiKit.Base.update(B,bobj.crv.params.CalendarButton);B.clickCB=bobj.bindFunctionToObject(B.onClick,B);return B};bobj.crv.params.CalendarButton={onClick:function(){var E=bobj.crv.Calendar.getInstance();var D=bobj.external.date.getDateFromFormat(this.getValueCB(),this.calendarProperties.displayValueFormat);var C=MochiKit.Signal.connect;if(D){E.setDate(D)}this.calendarSignals={okSignal:C(E,E.Signals.OK_CLICK,this,"onClickCalendarOKButton"),cancelSignal:C(E,E.Signals.CANCEL_CLICK,this,"onClickCalendarCancelButton"),hideSignal:C(E,E.Signals.ON_HIDE,this,"onHideCalendar")};var B=getPosScrolled(this.layer);var A=B.x+this.getWidth();var F=B.y+this.getHeight()+1;E.setShowTime(this.calendarProperties.isTimeShown);E.show(true,A,F)},onClickCalendarOKButton:function(A){var B=bobj.external.date.formatDate(A,this.calendarProperties.displayValueFormat);this.setValueCB(B)},onClickCalendarCancelButton:function(){this.clearCalendarSignals()},clearCalendarSignals:function(){for(var A in this.calendarSignals){bobj.crv.SignalDisposer.dispose(this.calendarSignals[A],true);this.calendarSignals[A]=null}},onHideCalendar:function(){this.clearCalendarSignals();if(this.layer.focus){this.layer.focus()}},init:function(){this.oldInit();this.layer.onfocus=IconWidget_realOverCB;this.layer.onblur=IconWidget_realOutCB},getHTML:function(){var C;var B=bobj.html;if(this.src){C=B.DIV({style:{overflow:"hidden",height:"14px",position:"relative",top:"2px",width:this.w+"px"}},simpleImgOffset(this.src,this.w,this.h,this.dx,this.dy,"IconImg_"+this.id,null,this.alt,"cursor:"+_hand),this.extraHTML)}else{C=B.DIV({"class":"iconText",style:{width:"1px",height:(this.h+this.border)+"px"}})}var A={margin:this.margin+"px",padding:"1px"};if(this.width){A.width=this.width+"px"}if(!this.disp){A.display="none"}return B.DIV({style:A,id:this.id,"class":this.nocheckClass},(this.clickCB&&_ie)?lnk(C,null,null,null,' tabIndex="-1"'):C)}};bobj.crv.params.newOptionalParameterValueRow=function(A){A=MochiKit.Base.update({noValueDisplayText:"",isEmptyStringNoValue:true,clearValuesCB:null},A);var B=bobj.crv.params.newParameterValueRow(A);bobj.extendClass(B,bobj.crv.params.OptionalParameterValueRow,bobj.crv.params.ParameterValueRow);if(B.canChangeOnPanel){B._clearValueButton=newIconWidget(B.id+"_clearBtn",bobj.crvUri("images/clear_x.gif"),B.clearValuesCB,null,L_bobj_crv_ParamsClearValues,10,10,2,2);B._clearValueButton.setClasses("","iconcheck","","iconcheckhover");B._clearValueButton.margin=2}return B};bobj.crv.params.OptionalParameterValueRow={getNewValueWidgetArgs:function(){var A=this.superClass.getNewValueWidgetArgs();if(this.value==undefined){A.cleanValue=this.noValueDisplayText;A.foreColor=bobj.Colors.GRAY}return A},init:function(){this.superClass.init();if(this._clearValueButton){this._clearValueButton.init()}this.updateUI()},getHTML:function(){var A=this.superClass.getHTML();if(this.canChangeOnPanel){var B=bobj.html;A=B.DIV({style:{position:"relative"}},A,B.DIV({"class":"clearValueBtnCtn"},this._clearValueButton.getHTML()))}return A},onFocus:function(){this.superClass.onFocus();if(this.canChangeOnPanel){if(this.value==undefined){this._valueWidget.setValue("")}}},onBlur:function(){this.superClass.onBlur();if(this.canChangeOnPanel){if(this.value==undefined){this._valueWidget.setValue(this.noValueDisplayText)}}},_onChange:function(){this.value=this._valueWidget.getValue();if(this.isEmptyStringNoValue&&this.value!=null&&this.value.length==0){this.value=undefined}this.updateUI();if(this.changeCB){this.changeCB()}},updateUI:function(){if(this._valueWidget){if(this.isReadOnlyParam){this._valueWidget.setForeColor(bobj.Colors.GRAY);this._valueWidget.setTextItalic(false)}else{if(this.value==undefined){this._valueWidget.setForeColor(bobj.Colors.GRAY);if(this._clearValueButton){this._clearValueButton.setDisplay(false)}}else{this._valueWidget.setForeColor(bobj.Colors.BLACK);if(this._clearValueButton){this._clearValueButton.setDisplay(true)}}}}},getValueWidgetDisplayValue:function(A){return(A==undefined)?this.noValueDisplayText:A},setValue:function(A){this.value=A;this._valueWidget.setValue(this.getValueWidgetDisplayValue(A));this.updateUI();this.setWarning(null)},reset:function(A){this.superClass.reset(A);if(this._valueWidget){this._valueWidget.reset(this.getValueWidgetDisplayValue(A))}this.updateUI()}};bobj.crv.params.newParameterUI=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),canChangeOnPanel:false,allowCustom:false,isPassword:false,isReadOnlyParam:true,allowRange:false,values:[],defaultValues:null,width:"200px",changeValueCB:null,enterPressCB:null,openAdvDialogCB:null,maxNumParameterDefaultValues:200,tooltip:null,calendarProperties:{displayValueFormat:"",isTimeShown:false,hasButton:false,iconUrl:""},maxNumValuesDisplayed:7,canOpenAdvDialog:false},A);var B=newWidget(A.id);bobj.fillIn(B,A);B.displayAllValues=false;MochiKit.Base.update(B,bobj.crv.params.ParameterUI);B._createMenu();B._rows=[];B._infoRow=new bobj.crv.params.ParameterInfoRow(B.id);return B};bobj.crv.params.ParameterUI={_createMenu:function(){var A=this.defaultValues.length;if(A>0){var B={originalValues:this.defaultValues};if(A==this.maxNumParameterDefaultValues){B.originalValues[this.maxNumParameterDefaultValues]=L_bobj_crv_ParamsMaxNumDefaultValues;MochiKit.Base.update(B,{openAdvDialogCB:this.openAdvDialogCB,maxNumParameterDefaultValues:this.maxNumParameterDefaultValues})}this._defaultValuesMenu=bobj.crv.params.newScrollMenuWidget(B)}else{this._defaultValuesMenu=null}},setFocusOnRow:function(B){var A=this._rows[B];if(A){A.focus()}},setTabDisabled:function(B){for(var C=0,A=this._rows.length;C<A;C++){this._rows[C].setTabDisabled(B)}this._infoRow.setTabDisabled(B)},init:function(){Widget_init.call(this);var C=this._rows;for(var B=0,A=C.length;B<A;++B){C[B].init()}MochiKit.Signal.connect(this._infoRow,"switch",this,"_onSwitchDisplayAllValues");this.refreshUI()},_onSwitchDisplayAllValues:function(){this.displayAllValues=!this.displayAllValues;var A=10;var G=0;if(this.displayAllValues){if(this.values.length>this._rows.length){for(var F=this._rows.length,C=this.values.length;F<C;F++){var B=function(I,H){return function(){return I._addRow(H)}};G++;setTimeout(B(this,this.values[F]),A*G)}}}else{if(this._rows.length>this.maxNumValuesDisplayed){for(var F=this._rows.length-1;F>=this.maxNumValuesDisplayed;F--){var E=function(I,H){return function(){return I.deleteValue(H)}};G++;setTimeout(E(this,F),A*G)}}}var D=function(H){return function(){MochiKit.Signal.signal(H,"ParameterUIResized")}};setTimeout(D(this),A*G)},getHTML:function(){var E="";var A=this.values;var D=this._rows;var C=Math.min(A.length,this.maxNumValuesDisplayed);for(var B=0;B<C;++B){D.push(this._getRow(A[B]));E+=D[B].getHTML()}return bobj.html.DIV({id:this.id,style:{width:bobj.unitValue(this.width),"padding-left":"20px"}},E)},_getNewValueRowArgs:function(A){return{value:A,defaultValues:this.defaultValues,width:this.width,isReadOnlyParam:this.isReadOnlyParam,canChangeOnPanel:this.canChangeOnPanel,allowCustom:this.allowCustom,isPassword:this.isPassword,calendarProperties:this.calendarProperties,defaultValuesMenu:this._defaultValuesMenu,tooltip:this.tooltip,isRangeValue:this.allowRange,canOpenAdvDialog:this.canOpenAdvDialog}},_getNewValueRowConstructor:function(){return bobj.crv.params.newParameterValueRow},_getRow:function(A){var B=this._getNewValueRowConstructor()(this._getNewValueRowArgs(A));var C=MochiKit.Base.bind;B.changeCB=C(this._onChangeValue,this,B);B.enterCB=C(this._onEnterValue,this,B);return B},_addRow:function(A){var B=this._getRow(A);this._rows.push(B);append(this.layer,B.getHTML());B.init();this.refreshUI();return B},_onChangeValue:function(A){if(this.changeValueCB){this.changeValueCB(this._getRowIndex(A),A.getValue())}},_onEnterValue:function(A){if(this.enterPressCB){this.enterPressCB(this._getRowIndex(A))}},_getRowIndex:function(D){if(D){var C=this._rows;for(var B=0,A=C.length;B<A;++B){if(C[B]===D){return B}}}return -1},getNumValues:function(){return this._rows.length},refreshUI:function(){if(this.allowRange){this.alignRangeRows()}var B=false;var A="";if(this.values.length>this.maxNumValuesDisplayed){B=true;if(this.displayAllValues){A=L_bobj_crv_Collapse}else{var C=this.values.length-this.maxNumValuesDisplayed;A=(C==1)?L_bobj_crv_ParamsMoreValue:L_bobj_crv_ParamsMoreValues;A=A.replace("%1",C)}}this._infoRow.setText(A);this._infoRow.setVisible(B)},getValueAt:function(A){var B=this._rows[A];if(B){return B.getValue()}return null},getValues:function(){var B=[];for(var C=0,A=this._rows.length;C<A;++C){B.push(this._rows[C].getValue())}return B},setValueAt:function(A,B){var C=this._rows[A];if(C){C.setValue(B)}this.refreshUI()},resetValues:function(B){if(!B){return }this.values=B;var A=B.length;var D=this._rows.length;for(var C=0;C<A&&C<D;++C){this._rows[C].reset(B[C])}if(D>A){for(var C=D-1;C>=A;--C){this.deleteValue(C)}}else{if(A>D){for(var C=D;C<A&&(this.displayAllValues||C<this.maxNumValuesDisplayed);++C){var E=this._addRow(B[C])}}}MochiKit.Signal.signal(this,"ParameterUIResized");this.refreshUI()},alignRangeRows:function(){if(!this.allowRange){return }var C=0;for(var B=0,A=this._rows.length;B<A;B++){var E=this._rows[B];var D=E._valueWidget;C=Math.max(C,D.getLowerBoundValueWidth())}for(var B=0,A=this._rows.length;B<A;B++){var E=this._rows[B];var D=E._valueWidget;D.setLowerBoundValueWidth(C)}},setValues:function(B){if(!B){return }this.values=B;var A=B.length;var D=this._rows.length;for(var C=0;C<A&&C<D;++C){this._rows[C].setValue(B[C])}if(D>A){for(var C=D-1;C>=A;--C){this.deleteValue(C)}}else{if(A>D){for(var C=D;C<A&&(this.displayAllValues||C<this.maxNumValuesDisplayed);++C){this._addRow(B[C])}}}MochiKit.Signal.signal(this,"ParameterUIResized");this.refreshUI()},setCleanValue:function(A,B){var C=this._rows[A];if(C){C.setCleanValue(B)}},deleteValue:function(A){if(A>=0&&A<this._rows.length){var C=this._rows[A];C.layer.parentNode.removeChild(C.layer);_widgets[C.widx]=null;this._rows.splice(A,1);var B=this._rows.length}this.refreshUI()},setWarning:function(A,B){var C=this._rows[A];if(C){C.setWarning(B)}},getWarning:function(A){var B=this._rows[A];if(B){return B.getWarning()}return null},resize:function(A){if(A!==null){this.width=A;if(this.layer){bobj.setOuterSize(this.layer,A)}}}};bobj.crv.params.newOptionalParameterUI=function(A){A=MochiKit.Base.update({noValueDisplayText:"",isEmptyStringNoValue:true,clearValuesCB:null},A);var B=bobj.crv.params.newParameterUI(A);bobj.extendClass(B,bobj.crv.params.OptionalParameterUI,bobj.crv.params.ParameterUI);return B};bobj.crv.params.OptionalParameterUI={_getNewValueRowConstructor:function(){return bobj.crv.params.newOptionalParameterValueRow},_getNewValueRowArgs:function(B){var A=this.superClass._getNewValueRowArgs(B);A.noValueDisplayText=this.noValueDisplayText;A.isEmptyStringNoValue=this.isEmptyStringNoValue;A.clearValuesCB=this.clearValuesCB;return A}};bobj.crv.params.ParameterController=function(B,E,A){this._panel=B;this._viewerCtrl=E;this._paramOpts=A;this._paramList=null;this._unusedParamList=null;var D=bobj.bindFunctionToObject(this._onClickTbApplyButton,this);var C=bobj.bindFunctionToObject(this._onClickTbResetButton,this);this._panel.setToolbarCallBacks(D,C)};bobj.crv.params.ParameterController.prototype={setParameters:function(D,L){var K=bobj.crv.params.DataTypes;var W=MochiKit.Base.map;var X=MochiKit.Base.bind;this._deleteWidgets();var A=50;this._paramList=D;for(var Q=0;Q<D.length;++Q){var G=D[Q];var V=X(this._getDisplayText,this,G);var O=X(this._getDefaultValue,this,G.valueDataType,G.defaultDisplayType);var S=this._canPanelChangeValues(G);var P=this._getMinMaxText(G.valueDataType,G.minValue,G.maxValue);var C=X(this._onClickTbAdvButton,this,G);var R=X(this.clearParameterValues,this,G);var N=!G.isEditable;var J=G.getValue();var U=G.isOptionalPrompt||G.allowNullValue;var F={hasButton:G.allowCustomValue&&S&&(G.valueDataType===K.DATE||G.valueDataType===K.DATE_TIME),isTimeShown:G.valueDataType===K.DATE_TIME,displayValueFormat:this._getDateTimeFormat(G.valueDataType),iconUrl:bobj.crvUri("images/calendar.gif")};if(U){J=this.convertOptionalParameterValue(J)}var T=U?bobj.crv.params.newOptionalParameterUI:bobj.crv.params.newParameterUI;var B=G.isEditable&&(G.allowRangeValue||G.allowMultiValue||G.isDCP()||G.editMask);var I={values:W(V,J),canChangeOnPanel:S,allowCustom:G.allowCustomValue,allowRange:G.allowRangeValue,canAddValues:G.allowMultiValue&&S,isReadOnlyParam:N,isPassword:G.isPassword(),defaultValues:W(O,G.defaultValues||[]),openAdvDialogCB:C,maxNumParameterDefaultValues:this._paramOpts.maxNumParameterDefaultValues,tooltip:G.getTitle(),calendarProperties:F,clearValuesCB:R,canOpenAdvDialog:B};if(U){I.noValueDisplayText=this._getNoValueDisplayText(G.valueDataType,S);I.isEmptyStringNoValue=G.valueDataType!=K.STRING}var M=T(I);this._observeParamWidget(M);var E=function(Y,Z,a,g,b,d,f,c,e){return function(){Y.addParameter({paramUI:a,label:Z.getTitle(),name:Z.getName(),isDataFetching:Z.isDataFetching,openAdvCB:g});if(c&&e){e()}}};setTimeout(E(this._panel,G,M,B?C:null,U,S,R,Q==D.length-1,L),A*Q)}if(D){var H=function(Y){return function(){Y.resize();if(MochiKit.Base.isIE()){var Z=Y.isDisplayed();Y.setDisplay(!Z);Y.setDisplay(Z)}}};setTimeout(H(this._panel),A*D.length)}this._panel.setDisabled(!this._isCurrentViewMainReport());this._panel.setApplyButtonEnabled(false);this._panel.setResetButtonEnabled(false)},setUnusedParameters:function(A){this._unusedParamList=A},convertOptionalParameterValue:function(A){if(A===undefined||A.length===undefined){return[]}var B=bobj.cloneArray(A);if(B.length==0||B[0]==null){B[0]=undefined}return B},getParameters:function(){return this._paramList},onChangeView:function(){this._panel.setDisabled(!this._isCurrentViewMainReport())},_isPanelEditable:function(){return this._isCurrentViewMainReport()},_isCurrentViewMainReport:function(){var A=this._viewerCtrl.getCurrentView();return A&&A.isMainReport()},findParamIndexByName:function(B){for(var A=0;A<this._paramList.length;++A){if(this._paramList[A].paramName===B){return A}}return -1},findUnusedParamIndexByName:function(B){for(var A=0;A<this._unusedParamList.length;++A){if(this._unusedParamList[A].paramName===B){return A}}return -1},findParamByName:function(B){var A=this.findParamIndexByName(B);if(A==-1){return null}else{return this._paramList[A]}},updateParameter:function(C,F){if(C){var B=this.findParamIndexByName(C);if(B!=-1){var E=this._paramList[B];E.setValue(F);var G=this._panel.getParameter(B);var D=MochiKit.Base.bind(this._getDisplayText,this,this._paramList[B]);if(E.isOptionalPrompt||E.allowNullValue){F=this.convertOptionalParameterValue(F)}G.setValues(MochiKit.Base.map(D,F));var A=this._panel.getParameterTab(B);if(A){A.setDirty(true)}}else{B=this.findUnusedParamIndexByName(C);if(B!=-1){var E=this._unusedParamList[B];E.setValue(F)}}}},getFocusAdvButtonCB:function(A){return MochiKit.Base.bind(this._focusAdvButton,this,A)},_focusAdvButton:function(C){if(C){var B=this.findParamIndexByName(C);if(B!=-1){var A=this._panel.getParameterTab(B);if(A){A.focusAdvButton()}}}},_canPanelChangeValues:function(A){return A&&A.isEditable&&!A.allowRangeValue&&!A.editMask&&!A.isDCP()&&!A.allowMultiValue},_deleteWidgets:function(){var A=this._panel.getParameter(0);while(A){delete _widgets[A.widx];this._panel.removeParameter(0);A=this._panel.getParameter(0)}},resetParamPanel:function(){for(var F=0,B=this._paramList.length;F<B;F++){var C=this._paramList[F];C.reset();var A=this._findWidget(C);var G=MochiKit.Base.bind(this._getDisplayText,this,C);var D=C.getValue();if(C.isOptionalPrompt||C.allowNullValue){D=this.convertOptionalParameterValue(D)}var I=MochiKit.Base.map(G,D);A.resetValues(I)}for(var F=0,E=this._panel.getParameterCount();F<E;F++){var H=this._panel.getParameterTab(F);if(H){H.setDirty(false)}}this._viewerCtrl.clearAdvancedPromptData();this._panel.setApplyButtonEnabled(false);this._panel.setResetButtonEnabled(false)},_compareCustomDateObject:function(B,A){if(B.y!=A.y){return false}if(B.m!=A.m){return false}if(B.d!=A.d){return false}if(B.h!=A.h){return false}if(B.min!=A.min){return false}if(B.s!=A.s){return false}if(B.ms!=A.ms){return false}return true},_hasMinBound:function(B,C){if(C==null){return false}var A=bobj.crv.params.DataTypes;switch(B){case A.STRING:if(C==0){return false}return true;case A.DATE:case A.DATE_TIME:absoluteMin={y:1753,m:0,d:1,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMin,C);case A.TIME:absoluteMin={y:1899,m:11,d:30,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMin,C);case A.NUMBER:case A.CURRENCY:if(C==-3.40282346638529e+38){return false}return true}return false},_hasMaxBound:function(B,C){if(C==null){return false}var A=bobj.crv.params.DataTypes;switch(B){case A.STRING:if(C==65534){return false}return true;case A.DATE:absoluteMax={y:9999,m:11,d:12,h:0,min:0,s:0,ms:0};return !this._compareCustomDateObject(absoluteMax,C);case A.TIME:absoluteMax={y:1899,m:11,d:30,h:23,min:59,s:59,ms:0};return !this._compareCustomDateObject(absoluteMax,C);case A.DATE_TIME:absoluteMax={y:9999,m:11,d:12,h:23,min:59,s:59,ms:0};return !this._compareCustomDateObject(absoluteMax,C);case A.NUMBER:case A.CURRENCY:if(C==3.40282346638529e+38){return false}return true}return false},_getNoValueDisplayText:function(D,A){if(!A){return L_bobj_crv_ParamsNoneSelected}else{var C=bobj.crv.params.DataTypes;var B="";switch(D){case C.DATE:B=L_bobj_crv_Date;break;case C.DATE_TIME:B=L_bobj_crv_DateTime;break;case C.TIME:B=L_bobj_crv_Time;break;case C.STRING:B=L_bobj_crv_Text;break;case C.NUMBER:B=L_bobj_crv_Number;break;case C.CURRENCY:B=L_bobj_crv_Number;break;case C.BOOLEAN:B=L_bobj_crv_Boolean;break}return L_bobj_crv_ParamsEnterOptional.replace("%1",B)}},_getMinMaxText:function(G,I,F){var A=bobj.crv.params.DataTypes;var J,C;if(G==A.STRING){C=this._getValueText(A.NUMBER,I);J=this._getValueText(A.NUMBER,F)}else{C=this._getValueText(G,I);J=this._getValueText(G,F)}if(G==A.BOOLEAN||(I==null&&F==null)){return null}var E,H;switch(G){case A.DATE:E=L_bobj_crv_Date;break;case A.TIME:E=L_bobj_crv_Time;break;case A.DATE_TIME:E=L_bobj_crv_DateTime;break;case A.NUMBER:E=L_bobj_crv_Number;break;case A.CURRENCY:E=L_bobj_crv_Number;break}var D=this._hasMinBound(G,I);var B=this._hasMaxBound(G,F);switch(G){case A.STRING:if(D&&B){H=L_bobj_crv_ParamsStringMinAndMaxTooltip.replace("%1",C);H=H.replace("%2",J)}else{if(D){H=L_bobj_crv_ParamsStringMinOrMaxTooltip.replace("%1",L_bobj_crv_Minimum);H=H.replace("%2",C)}else{if(B){H=L_bobj_crv_ParamsStringMinOrMaxTooltip.replace("%1",L_bobj_crv_Maximum);H=H.replace("%2",J)}}}break;default:if(D&&B){H=L_bobj_crv_ParamsMinAndMaxTooltip.replace("%1",E);H=H.replace("%2",C);H=H.replace("%3",J)}else{if(D){H=L_bobj_crv_ParamsMinTooltip.replace("%1",E);H=H.replace("%2",C)}else{if(B){H=L_bobj_crv_ParamsMaxTooltip.replace("%1",E);H=H.replace("%2",J)}}}}return H},_getDefaultValue:function(E,D,F){var A=bobj.crv.params.DefaultDisplayTypes;var C=this._getValueText(E,F.value);var B;switch(D){case A.Description:if(F.desc!=null&&F.desc.length>0){B=F.desc}else{B=C}break;case A.DescriptionAndValue:B=C;if(F.desc!=null&&F.desc.length>0){B+=" - "+F.desc}break}return B},_getValueTextFromDefValueDesc:function(D,C){if(D.defaultValues&&bobj.isArray(D.defaultValues)){for(var A=0;A<D.defaultValues.length;A++){var B=this._getDefaultValue(D.valueDataType,D.defaultDisplayType,D.defaultValues[A]);if(B==C){return this._getValueText(D.valueDataType,D.defaultValues[A].value)}}}return null},_getValueText:function(B,C){if(C===undefined){return undefined}C=bobj.crv.params.getValue(C);var A=bobj.crv.params.DataTypes;switch(B){case A.DATE:return this._getDateTimeText(C,this._paramOpts.dateFormat);case A.TIME:return this._getDateTimeText(C,this._paramOpts.timeFormat);case A.DATE_TIME:return this._getDateTimeText(C,this._paramOpts.dateTimeFormat);case A.NUMBER:case A.CURRENCY:return this._getNumberText(C,this._paramOpts.numberFormat);case A.BOOLEAN:return this._getBooleanText(C,this._paramOpts.booleanFormat);case A.STRING:default:return""+C}},_getBooleanText:function(B,A){return A[""+B]},_getNumberText:function(H,G){var F=G.decimalSeperator;var A=G.groupSeperator;var J=(""+H).split(".");var I,B,E;var K=null;I=J[0];if(I.length>0&&I.slice(0,1)=="-"||I.slice(0,1)=="+"){K=I.slice(0,1);I=I.slice(1,I.length)}B=(J.length==2)?J[1]:null;formattedLeftVal=null;if(I.length<=3){formattedLeftVal=I}else{var D=null;var C=null;while(I.length>0){C=(I.length>3)?I.length-3:0;D=I.slice(C,I.length);I=I.slice(0,C);formattedLeftVal=(formattedLeftVal==null)?D:D+A+formattedLeftVal}}E=(B!=null)?formattedLeftVal+F+B:formattedLeftVal;E=(K!=null)?K+E:E;return E},_getDateTimeText:function(B,C){var A=bobj.crv.params.jsonToDate(B);if(A){return bobj.external.date.formatDate(A,C)}return""},_getValueTextFromDefaultValue:function(E,B){var D=bobj.crv.params.getDescription(B);if(D!==null){return this._getDefaultValue(E.valueDataType,E.defaultDisplayType,B)}B=bobj.crv.params.getValue(B);if(bobj.isArray(E.defaultValues)){var C=bobj.getValueHashCode(E.valueDataType,B);for(var A=0;A<E.defaultValues.length;A++){if(C==bobj.getValueHashCode(E.valueDataType,E.defaultValues[A].value)){return this._getDefaultValue(E.valueDataType,E.defaultDisplayType,E.defaultValues[A])}}}return null},_getDisplayText:function(C,B){if(B===undefined){return undefined}if(B.lowerBoundType!==undefined||B.upperBoundType!==undefined){return this._getRangeDisplayText(C,B)}var A=this._getValueTextFromDefaultValue(C,B);if(A==null){A=this._getValueText(C.valueDataType,B)}return A},_getRangeDisplayText:function(C,B){var A=new Object;A.lowerBound={type:B.lowerBoundType,value:this._getDisplayText(C,B.beginValue)};A.upperBound={type:B.upperBoundType,value:this._getDisplayText(C,B.endValue)};return A},_getParamValue:function(B,C){if(B===undefined){return undefined}var A=bobj.crv.params.DataTypes;switch(B){case A.DATE:return this._getDateTimeParamValue(C,this._paramOpts.dateFormat);case A.TIME:return this._getDateTimeParamValue(C,this._paramOpts.timeFormat);case A.DATE_TIME:return this._getDateTimeParamValue(C,this._paramOpts.dateTimeFormat);case A.NUMBER:case A.CURRENCY:return this._getNumberParamValue(C,this._paramOpts.numberFormat);case A.BOOLEAN:return this._getBooleanParamValue(C,this._paramOpts.booleanFormat);case A.STRING:default:return C}},_getBooleanParamValue:function(B,A){if(B!=null&&B.length!=0){return A["true"]==B}else{return null}},clearParameterValues:function(A){if(A.allowNullValue){this.updateParameter(A.paramName,[null])}else{this.updateParameter(A.paramName,[])}this._updateToolbar()},_getNumberParamValue:function(D,C){if(D==null){return null}var A="";if(/[ \f\n\r\t\v\u00A0\u2028\u2029]/.test(C.groupSeperator)){A=D.replace(/[ \f\n\r\t\v\u00A0\u2028\u2029]/g,"")}else{var B=new RegExp("\\"+C.groupSeperator,"g");A=D.replace(B,"")}return A.replace(C.decimalSeperator,".")},_getDateTimeParamValue:function(C,B){var A=bobj.external.date.getDateFromFormat(C,B);if(A){return bobj.crv.params.dateToJson(A)}return C},_observeParamWidget:function(A){if(A){var B=MochiKit.Base.bind;A.changeValueCB=B(this._onChangeValue,this,A);A.enterPressCB=B(this._onEnterPress,this,A)}},_onChangeValue:function(B,A,D){if(!this._isPanelEditable()){return }var C=this._panel.getParameterTabByWidget(B);if(C){C.setDirty(true)}this._checkAndSetValue(B,A);this._updateToolbar()},_onEnterPress:function(B,A){if(this._panel.isApplyButtonEnabled()){this._applyValues()}},_onClickTbApplyButton:function(){this._applyValues()},_onClickTbResetButton:function(){this.resetParamPanel()},_onClickTbAdvButton:function(A){if(this._isPanelEditable()){this._viewerCtrl.showAdvancedParamDialog(A)}},_applyValues:function(){var C=this._paramList.length;var G=-1;var A=-1;var I=null;var B=null;for(var E=0;(E<C)&&!I;++E){B=this._panel.getParameter(E);var H=B.getNumValues();for(var D=0;(D<H)&&!I;++D){I=B.getWarning(D);if(I){G=E;A=D}}}if(I){B=this._panel.getParameter(G);B.setFocusOnRow(A)}else{for(var E=0,F=this._paramList.length;E<F;E++){this._paramList[E].commitValue()}for(var E=0,F=this._unusedParamList.length;E<F;E++){this._unusedParamList[E].commitValue()}this._viewerCtrl.applyParams(this._paramList.concat(this._unusedParamList));this._panel.setApplyButtonEnabled(false)}},_findParam:function(A){return this._paramList[this._panel.getIndex(A)]},_findWidget:function(B){for(var A=0;A<this._paramList.length;A++){if(this._paramList[A].paramName==B.paramName){return this._panel.getParameter(A)}}return null},_updateToolbar:function(){if(!this._isPanelEditable()){return }this._panel.setApplyButtonEnabled(true);this._panel.setResetButtonEnabled(true)},_getDateTimeFormat:function(B){var A=bobj.crv.params.DataTypes;switch(B){case A.DATE:return this._paramOpts.dateFormat;case A.TIME:return this._paramOpts.timeFormat;case A.DATE_TIME:return this._paramOpts.dateTimeFormat;default:return null}},_checkAndSetValue:function(C,I){if(!C.canChangeOnPanel){return }var H=this._findParam(C);var D=C.getValueAt(I);var G=this._getValueTextFromDefValueDesc(H,D);if(G!=null){D=G}var B=this._getParamValue(H.valueDataType,D);if(I==0&&B==undefined){if(H.allowNullValue&&H.getValue().length==0){H.setValue(0,null)}if(H.isOptionalPrompt){H.clearValue()}C.setWarning(I,null);return }var F=bobj.crv.params.Validator.ValueStatus;var A=F.OK;var J=null;A=bobj.crv.params.Validator.getInstance().validateValue(H,B);if(F.OK===A){var E=this._getValueTextFromDefaultValue(H,B);if(E!=null){C.setValueAt(I,E)}C.setWarning(I,null);H.setValue(I,B)}else{J=this._getWarningText(H,A);C.setWarning(I,{code:A,message:J})}},_getWarningText:function(C,B){var A=bobj.crv.params.DataTypes;switch(C.valueDataType){case A.DATE:case A.TIME:case A.DATE_TIME:return this._getDateTimeWarning(C,B);case A.STRING:return this._getStringWarning(C,B);case A.NUMBER:case A.CURRENCY:return this._getNumberWarning(C,B);default:return null}},_getDateTimeWarning:function(E,D){var C=bobj.crv.params.DataTypes;var B=bobj.crv.params.Validator.ValueStatus;var A=this._paramOpts.dateFormat;A=A.replace("yyyy","%1");A=A.replace(/M+/,"%2");A=A.replace(/d+/,"%3");A=A.replace("%1",L_bobj_crv_ParamsYearToken);A=A.replace("%2",L_bobj_crv_ParamsMonthToken);A=A.replace("%3",L_bobj_crv_ParamsDayToken);if(D==B.ERROR||D==B.VALUE_INVALID_TYPE){switch(E.valueDataType){case C.DATE:return L_bobj_crv_ParamsBadDate.replace("%1",A);case C.DATE_TIME:return L_bobj_crv_ParamsBadDateTime.replace("%1",A);break;case C.TIME:return L_bobj_crv_ParamsBadTime;break}}else{if(D==B.VALUE_TOO_BIG||D==B.VALUE_TOO_SMALL){return this._getMinMaxText(E.valueDataType,E.minValue,E.maxValue)}}return null},_getStringWarning:function(C,B){var A=bobj.crv.params.Validator.ValueStatus;if(A.VALUE_TOO_LONG===B){return L_bobj_crv_ParamsTooLong.replace("%1",C.maxValue)}else{if(A.VALUE_TOO_SHORT===B){return L_bobj_crv_ParamsTooShort.replace("%1",C.minValue)}}return null},_getNumberWarning:function(D,C){var B=bobj.crv.params.Validator.ValueStatus;var A=bobj.crv.params.DataTypes;switch(C){case B.ERROR:case B.VALUE_INVALID_TYPE:if(D.valueDataType==A.NUMBER){return L_bobj_crv_ParamsBadNumber}else{if(D.valueDataType==A.CURRENCY){return L_bobj_crv_ParamsBadCurrency}}case B.VALUE_TOO_BIG:case B.VALUE_TOO_SMALL:return this._getMinMaxText(D.valueDataType,D.minValue,D.maxValue);default:return null}}};if(typeof bobj==="undefined"){bobj={}}if(typeof bobj.crv==="undefined"){bobj.crv={}}if(typeof bobj.crv.WarningPopup==="undefined"){bobj.crv.WarningPopup={}}bobj.crv.WarningPopup.getInstance=function(){if(bobj.crv.WarningPopup.__instance===undefined){bobj.crv.WarningPopup.__instance=new bobj.crv.WarningPopup.Class()}return bobj.crv.WarningPopup.__instance};bobj.crv.WarningPopup.Class=function(){this.layer=null;this.id=bobj.uniqueId()};bobj.crv.WarningPopup.Class.prototype={show:function(C,B,A){if(!this.layer){this.init()}this.layer.style.top=A+"px";this.layer.style.left=B+"px";this.txtLayer.innerHTML=C;this.layer.style.display="block"},hide:function(){if(this.layer){this.layer.style.display="none"}},getHTML:function(){return bobj.html.DIV({"class":"WarningPopup",id:this.id},bobj.html.IMG({id:this.id+"img",style:{position:"absolute",left:"10px",top:"-19px"},src:bobj.crvUri("images/WarningPopupTriangle.gif")}),bobj.html.DIV({id:this.id+"txt",style:{padding:"5px"}}))},init:function(){append2(document.body,this.getHTML());this.layer=getLayer(this.id);this.txtLayer=getLayer(this.id+"txt")}};