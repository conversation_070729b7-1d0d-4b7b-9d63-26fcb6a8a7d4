/* Copyright (c) Business Objects 2006. All rights reserved. */

var L_bobj_crv_MainReport = "\uC8FC \uBCF4\uACE0\uC11C";
// Viewer Toolbar tooltips
var L_bobj_crv_FirstPage = "\uCCAB \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_PrevPage = "\uC774\uC804 \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_NextPage = "\uB2E4\uC74C \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_LastPage = "\uB9C8\uC9C0\uB9C9 \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_ParamPanel = "\uB9E4\uAC1C \uBCC0\uC218 \uCC3D";
var L_bobj_crv_Parameters = "\uB9E4\uAC1C \uBCC0\uC218";
var L_bobj_crv_GroupTree = "\uADF8\uB8F9 \uD2B8\uB9AC";
var L_bobj_crv_DrillUp = "\uB4DC\uB9B4\uC5C5";
var L_bobj_crv_Refresh = "\uBCF4\uACE0\uC11C \uC0C8\uB85C \uACE0\uCE68";
var L_bobj_crv_Zoom = "\uD655\uB300/\uCD95\uC18C";
var L_bobj_crv_PageNav = "\uD398\uC774\uC9C0 \uD0D0\uC0C9";
var L_bobj_crv_SelectPage = "\uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_SearchText = "\uD14D\uC2A4\uD2B8 \uAC80\uC0C9";
var L_bobj_crv_Export = "\uBCF4\uACE0\uC11C \uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_Print = "\uBCF4\uACE0\uC11C \uC778\uC1C4";
var L_bobj_crv_TabList = "\uD0ED \uBAA9\uB85D";
var L_bobj_crv_Close = "\uB2EB\uAE30";
var L_bobj_crv_Logo=  "Business Objects \uB85C\uACE0";
var L_bobj_crv_FileMenu = "\uD30C\uC77C \uBA54\uB274";

var L_bobj_crv_File = "\uD30C\uC77C";

var L_bobj_crv_Show = "\uD45C\uC2DC";
var L_bobj_crv_Hide = "\uC228\uAE30\uAE30";

var L_bobj_crv_Find = "\uCC3E\uAE30...";
var L_bobj_crv_of = "%2 \uC911 %1"; // Example: Page "1 of 3"

var L_bobj_crv_submitBtnLbl = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ActiveXPrintDialogTitle = "\uC778\uC1C4";
var L_bobj_crv_PDFPrintDialogTitle = "PDF\uB85C \uC778\uC1C4";
var L_bobj_crv_PrintRangeLbl = "\uD398\uC774\uC9C0 \uBC94\uC704:";
var L_bobj_crv_PrintAllLbl = "\uBAA8\uB4E0 \uD398\uC774\uC9C0";
var L_bobj_crv_PrintPagesLbl = "\uD398\uC774\uC9C0 \uC120\uD0DD";
var L_bobj_crv_PrintFromLbl = "\uC2DC\uC791:";
var L_bobj_crv_PrintToLbl = "\uB05D:";
var L_bobj_crv_PrintInfoTitle = "PDF\uB85C \uC778\uC1C4:";
var L_bobj_crv_PrintInfo1 = '\uC778\uC1C4\uD558\uB824\uBA74 PDF\uB85C \uB0B4\uBCF4\uB0B4\uC57C \uD569\uB2C8\uB2E4. \uBB38\uC11C\uAC00 \uC5F4\uB824 \uC788\uC73C\uBA74 PDF \uC77D\uAE30 \uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC5D0\uC11C \uC778\uC1C4 \uC635\uC158\uC744 \uC120\uD0DD\uD558\uC2ED\uC2DC\uC624.';
var L_bobj_crv_PrintInfo2 = '\uCC38\uACE0: \uC778\uC1C4\uD558\uB824\uBA74 PDF \uC77D\uAE30 \uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC774 \uC124\uCE58\uB418\uC5B4 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4(\uC608: Adobe Reader).';
var L_bobj_crv_PrintPageRangeError = "\uC720\uD6A8\uD55C \uD398\uC774\uC9C0 \uBC94\uC704\uB97C \uC785\uB825\uD569\uB2C8\uB2E4.";

var L_bobj_crv_ExportBtnLbl = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ExportDialogTitle = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ExportFormatLbl = "\uD30C\uC77C \uD615\uC2DD:";
var L_bobj_crv_ExportInfoTitle = "\uB0B4\uBCF4\uB0B4\uAE30 \uBC29\uBC95:";

var L_bobj_crv_ParamsApply = "\uC801\uC6A9";
var L_bobj_crv_ParamsAdvDlg = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12 \uD3B8\uC9D1";
var L_bobj_crv_ParamsDeleteTooltip = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12 \uC0AD\uC81C";
var L_bobj_crv_ParamsAddValue = "\uD074\uB9AD\uD558\uC5EC \uCD94\uAC00...";
var L_bobj_crv_ParamsApplyTip = "\uC801\uC6A9 \uB2E8\uCD94(\uC0AC\uC6A9\uD568)";
var L_bobj_crv_ParamsApplyDisabledTip = "\uC801\uC6A9 \uB2E8\uCD94(\uC0AC\uC6A9 \uC548 \uD568)";
var L_bobj_crv_ParamsDlgTitle = "\uAC12 \uC785\uB825";
var L_bobj_crv_ParamsCalBtn = "\uB2EC\uB825 \uB2E8\uCD94";
var L_bobj_crv_Reset= "\uC6D0\uB798\uB300\uB85C";
var L_bobj_crv_ResetTip = "\uC7AC\uC124\uC815 \uB2E8\uCD94(\uC0AC\uC6A9\uD568)";
var L_bobj_crv_ResetDisabledTip = "\uC6D0\uB798\uB300\uB85C \uB2E8\uCD94(\uC0AC\uC6A9 \uC548 \uD568)";
var L_bobj_crv_ParamsDirtyTip = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC774 \uBCC0\uACBD\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uBCC0\uACBD \uB0B4\uC6A9\uC744 \uC801\uC6A9\uD558\uB824\uBA74 [\uC801\uC6A9] \uB2E8\uCD94\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsDataTip = "\uB370\uC774\uD130 \uBC18\uC785 \uB9E4\uAC1C \uBCC0\uC218\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsMaxNumDefaultValues = "\uB354 \uB9CE\uC740 \uD56D\uBAA9\uC744 \uBCF4\uB824\uBA74 \uC5EC\uAE30\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624...";
var L_bobj_crv_paramsOpenAdvance = "\'%1\'\uC758 \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB2E8\uCD94";

var L_bobj_crv_ParamsInvalidTitle = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsTooLong = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC740 %1\uC790\uB97C \uCD08\uACFC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsTooShort = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC740 \uCD5C\uC18C %1\uC790 \uC774\uC0C1\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadNumber = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC22B\uC790\"\uC774\uBA70, \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadCurrency = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uD1B5\uD654\"\uC774\uBA70, \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadDate = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC\"\uC774\uACE0 \"%1\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadTime = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC2DC\uAC04\"\uC774\uACE0 \"hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadDateTime = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC \uC2DC\uAC04\"\uC774\uACE0 \"%1 hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC, \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsMinTooltip = "%1 \uAC12\uC744 %2\uBCF4\uB2E4 \uD06C\uAC70\uB098 \uAC19\uAC8C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsMaxTooltip = "%1 \uAC12\uC744 %2\uBCF4\uB2E4 \uC791\uAC70\uB098 \uAC19\uAC8C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsMinAndMaxTooltip = "%1 \uAC12\uC744 %2\uACFC(\uC640) %3 \uC0AC\uC774\uC758 \uAC12\uC73C\uB85C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsStringMinOrMaxTooltip = "\uC774 \uD544\uB4DC\uC5D0 \uB300\uD55C %1 \uAE38\uC774\uB294 %2\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsStringMinAndMaxTooltip = "%1-%2\uC790 \uC0AC\uC774 \uAC12\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.";
var L_bobj_crv_ParamsYearToken = "yyyy";
var L_bobj_crv_ParamsMonthToken = "mm";
var L_bobj_crv_ParamsDayToken = "dd";
var L_bobj_crv_ParamsReadOnly = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC77D\uAE30 \uC804\uC6A9\"\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsNoValue = "\uAC12 \uC5C6\uC74C";
var L_bobj_crv_ParamsDuplicateValue = "\uC911\uBCF5 \uAC12\uC740 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsEnterOptional = "%1 \uC785\uB825(\uC120\uD0DD \uC0AC\uD56D)";
var L_bobj_crv_ParamsNoneSelected= "(\uC120\uD0DD\uD55C \uD56D\uBAA9 \uC5C6\uC74C)";
var L_bobj_crv_ParamsClearValues= "\uAC12 \uC9C0\uC6B0\uAE30";
var L_bobj_crv_ParamsMoreValues= "%1 \uB2E4\uB978 \uAC12...";
var L_bobj_crv_ParamsMoreValue= "%1 \uB2E4\uB978 \uAC12...";
var L_bobj_crv_Error = "\uC624\uB958";
var L_bobj_crv_OK = "\uD655\uC778";
var L_bobj_crv_Cancel = "\uCDE8\uC18C";
var L_bobj_crv_showDetails = "\uC138\uBD80 \uC815\uBCF4 \uD45C\uC2DC";
var L_bobj_crv_hideDetails = "\uC138\uBD80 \uC815\uBCF4 \uC228\uAE30\uAE30";
var L_bobj_crv_RequestError = "\uC694\uCCAD\uC744 \uCC98\uB9AC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ServletMissing = "\uBE44\uB3D9\uAE30 \uC694\uCCAD\uC744 \uCC98\uB9AC\uD558\uB294 CrystalReportViewerServlet\uC5D0 \uBDF0\uC5B4\uB97C \uC5F0\uACB0\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.\n\uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC758 web.xml \uD30C\uC77C\uC5D0\uC11C Servlet \uBC0F Servlet-Mapping\uC774 \uC62C\uBC14\uB974\uAC8C \uC120\uC5B8\uB418\uC5C8\uB294\uC9C0 \uD655\uC778\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_FlashRequired = "\uC774 \uCEE8\uD150\uD2B8\uB97C \uC0AC\uC6A9\uD558\uB824\uBA74 Adobe Flash Player 9 \uC774\uC0C1\uC774 \uD544\uC694\uD569\uB2C8\uB2E4. {0}\uC124\uCE58\uD558\uB824\uBA74 \uC5EC\uAE30\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ReadOnlyInPanel= "\uD328\uB110\uC5D0\uC11C \uC774 \uB9E4\uAC1C \uBCC0\uC218\uB97C \uD3B8\uC9D1\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uD574\uB2F9 \uAC12\uC744 \uC218\uC815\uD558\uB824\uBA74 \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB300\uD654 \uC0C1\uC790\uB97C \uC5EC\uC2ED\uC2DC\uC624.";

var L_bobj_crv_Tree_Drilldown_Node = "%1 \uB178\uB4DC \uB4DC\uB9B4\uB2E4\uC6B4";

var L_bobj_crv_ReportProcessingMessage = "\uBB38\uC11C\uB97C \uCC98\uB9AC\uD558\uB294 \uB3D9\uC548 \uC7A0\uC2DC \uAE30\uB2E4\uB824 \uC8FC\uC2ED\uC2DC\uC624";
var L_bobj_crv_PrintControlProcessingMessage = "Crystal Reports \uC778\uC1C4 \uCEE8\uD2B8\uB864\uC774 \uB85C\uB4DC\uB418\uB294 \uB3D9\uC548 \uAE30\uB2E4\uB824 \uC8FC\uC2ED\uC2DC\uC624.";

var L_bobj_crv_SundayShort = "S";
var L_bobj_crv_MondayShort = "M";
var L_bobj_crv_TuesdayShort = "T";
var L_bobj_crv_WednesdayShort = "W";
var L_bobj_crv_ThursdayShort = "T ";
var L_bobj_crv_FridayShort = "F";
var L_bobj_crv_SaturdayShort = "S";

var L_bobj_crv_Minimum = "\uCD5C\uC18C\uAC12";
var L_bobj_crv_Maximum = "\uCD5C\uB300\uAC12";

var L_bobj_crv_Date = "\uB0A0\uC9DC";
var L_bobj_crv_Time = "\uC2DC\uAC04";
var L_bobj_crv_DateTime = "\uB0A0\uC9DC \uC2DC\uAC04";
var L_bobj_crv_Boolean = "\uBD80\uC6B8";
var L_bobj_crv_Number = "\uC22B\uC790";
var L_bobj_crv_Text = "\uD14D\uC2A4\uD2B8";

var L_bobj_crv_InteractiveParam_NoAjax = "\uC0AC\uC6A9 \uC911\uC778 \uC6F9 \uBE0C\uB77C\uC6B0\uC800\uAC00 \uB9E4\uAC1C \uBCC0\uC218 \uCC3D\uC744 \uD45C\uC2DC\uD558\uC9C0 \uC54A\uB3C4\uB85D \uC124\uC815\uB418\uC5C8\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_AdvancedDialog_NoAjax= "\uBDF0\uC5B4\uC5D0\uC11C \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB300\uD654 \uC0C1\uC790\uB97C \uC5F4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";

var L_bobj_crv_EnableAjax= "\uBE44\uB3D9\uAE30 \uC694\uCCAD\uC744 \uC0AC\uC6A9\uD558\uB824\uBA74 \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC2ED\uC2DC\uC624.";

var L_bobj_crv_LastRefreshed = "\uB9C8\uC9C0\uB9C9 \uC0C8\uB85C \uACE0\uCE68";

var L_bobj_crv_Collapse = "\uCD95\uC18C";

var L_bobj_crv_CatalystTip = "\uC628\uB77C\uC778 \uB9AC\uC18C\uC2A4";
// <script>
/*
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: labels.js


=============================================================
*/

_default="기본값"
_black="검정"
_brown="밤색"
_oliveGreen="황록색"
_darkGreen="진한 녹색"
_darkTeal="진한 청록"
_navyBlue="짙은 파랑"
_indigo="남색"
_darkGray="진한 회색"
_darkRed="진한 빨강"
_orange="주황"
_darkYellow="진한 노랑"
_green="녹색"
_teal="청록"
_blue="파랑"
_blueGray="청회색"
_mediumGray="중간 회색"
_red="빨강"
_lightOrange="연한 주황"
_lime="라임색"
_seaGreen="해록색"
_aqua="바다색"
_lightBlue="연한 파랑"
_violet="보라"
_gray="회색"
_magenta="자홍"
_gold="황금색"
_yellow="노랑"
_brightGreen="밝은 녹색"
_cyan="녹청"
_skyBlue="하늘색"
_plum="진한 보라"
_lightGray="연한 회색"
_pink="분홍"
_tan="황갈색"
_lightYellow="연한 노랑"
_lightGreen="연한 녹색"
_lightTurquoise="연한 옥색"
_paleBlue="흐린 파랑"
_lavender="라벤더색"
_white="흰색"
_lastUsed="마지막 사용:"
_moreColors="다른 색..."

_month=new Array

_month[0]="１월"
_month[1]="２월"
_month[2]="３월"
_month[3]="４월"
_month[4]="５월"
_month[5]="６월"
_month[6]="７월"
_month[7]="８월"
_month[8]="９월"
_month[9]="10월"
_month[10]="11월"
_month[11]="12월"

_day=new Array
_day[0]="S"
_day[1]="M"
_day[2]="T"
_day[3]="W"
_day[4]="T"
_day[5]="F"
_day[6]="S"

_today="오늘"

_AM="오전"
_PM="오후"

_closeDialog="창 닫기"

_lstMoveUpLab="위로 이동"
_lstMoveDownLab="아래로 이동"
_lstMoveLeftLab="왼쪽으로 이동" 
_lstMoveRightLab="오른쪽으로 이동"
_lstNewNodeLab="중첩된 필터 추가"
_lstAndLabel="AND"
_lstOrLabel="OR"
_lstSelectedLabel="선택됨"
_lstQuickFilterLab="빠른 필터 추가"

_openMenu="{0} 옵션에 액세스하려면 여기를 클릭하십시오."
_openCalendarLab="달력 열기"

_scroll_first_tab="첫 번째 탭으로 스크롤"
_scroll_previous_tab="이전 탭으로 스크롤"
_scroll_next_tab="다음 탭으로 스크롤"
_scroll_last_tab="마지막 탭으로 스크롤"

_expandedLab="확장됨"
_collapsedLab="축소됨"
_selectedLab="선택됨"

_expandNode="%1 노드 확장"
_collapseNode="%1 노드 축소"

_checkedPromptLab="설정"
_nocheckedPromptLab="설정 안 함"
_selectionPromptLab="값이 다음과 같음"
_noselectionPromptLab="값 없음"

_lovTextFieldLab="여기에 값 입력"
_lovCalendarLab="여기에 날짜 입력"
_lovPrevChunkLab="이전 청크로 이동"
_lovNextChunkLab="다음 청크로 이동"
_lovComboChunkLab="청크"
_lovRefreshLab="새로 고침"
_lovSearchFieldLab="여기에 검색할 텍스트 입력"
_lovSearchLab="검색"
_lovNormalLab="보통"
_lovMatchCase="대/소문자 구분"
_lovRefreshValuesLab="값 새로 고침"

_calendarNextMonthLab="다음 달로 이동"
_calendarPrevMonthLab="이전 달로 이동"
_calendarNextYearLab="다음 연도로 이동"
_calendarPrevYearLab="이전 연도로 이동"
_calendarSelectionLab="선택한 날짜"

_menuCheckLab="선택됨"
_menuDisableLab="사용 안 함"
	
_level="수준"
_closeTab="탭 닫기"
_of=" /"

_RGBTxtBegin= "RGB("
_RGBTxtEnd= ")"

_helpLab="도움말"

_waitTitleLab="잠시 기다려 주십시오."
_cancelButtonLab="취소"

_modifiers= new Array
_modifiers[0]="Ctrl+"
_modifiers[1]="Shift+"
_modifiers[2]="Alt+"

_bordersMoreColorsLabel="기타 테두리..."
_bordersTooltip=new Array
_bordersTooltip[0]="테두리 없음"
_bordersTooltip[1]="왼쪽 테두리"
_bordersTooltip[2]="오른쪽 테두리"
_bordersTooltip[3]="아래쪽 테두리"
_bordersTooltip[4]="중간 아래쪽 테두리"
_bordersTooltip[5]="두꺼운 아래쪽 테두리"
_bordersTooltip[6]="위쪽 및 아래쪽 테두리"
_bordersTooltip[7]="위쪽 및 중간 아래쪽 테두리"
_bordersTooltip[8]="위쪽 및 두꺼운 아래쪽 테두리"
_bordersTooltip[9]="모든 테두리"
_bordersTooltip[10]="모든 중간 테두리"
_bordersTooltip[11]="모든 두꺼운 테두리"/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\uC624\uB298";
var L_January   = "1\uC6D4";
var L_February  = "2\uC6D4";
var L_March     = "3\uC6D4";
var L_April     = "4\uC6D4";
var L_May       = "5\uC6D4";
var L_June      = "6\uC6D4";
var L_July      = "7\uC6D4";
var L_August    = "8\uC6D4";
var L_September = "9\uC6D4";
var L_October   = "10\uC6D4";
var L_November  = "11\uC6D4";
var L_December  = "12\uC6D4";
var L_Su        = "\uC77C";
var L_Mo        = "\uC6D4";
var L_Tu        = "\uD654";
var L_We        = "\uC218";
var L_Th        = "\uBAA9";
var L_Fr        = "\uAE08";
var L_Sa        = "\uD1A0";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC22B\uC790\"\uC774\uACE0 \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC785\uB825\uD55C \uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC744 \uC218\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_BadCurrency   = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uD1B5\uD654\"\uC774\uACE0 \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4. \uC785\uB825\uD55C \uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC744 \uC218\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_BadDate       = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC\"\uC774\uACE0 \"%1\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_BadDateTime   = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC \uC2DC\uAC04\"\uC774\uACE0 \"%1 hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC, \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_BadTime       = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC2DC\uAC04\"\uC774\uACE0 \"hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_NoValue       = "\uAC12 \uC5C6\uC74C";
var L_BadValue      = "\"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uD558\uB824\uBA74 [\uC2DC\uC791] \uBC0F [\uB05D] \uAC12\uC744 \uBAA8\uB450 \"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uD574\uC57C \uD569\uB2C8\uB2E4.";
var L_BadBound      = "\"\uD558\uD55C \uC5C6\uC74C\"\uACFC \"\uC0C1\uD55C \uC5C6\uC74C\"\uC744 \uD568\uAED8 \uC124\uC815\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_NoValueAlready = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uB294 \uC774\uBBF8 \"\uAC12 \uC5C6\uC74C\"\uC73C\uB85C \uC124\uC815\uB418\uC5B4 \uC788\uC2B5\uB2C8\uB2E4. \uB2E4\uB978 \uAC12\uC744 \uCD94\uAC00\uD558\uB824\uBA74 \uBA3C\uC800 \"\uAC12 \uC5C6\uC74C\"\uC744 \uC81C\uAC70\uD558\uC2ED\uC2DC\uC624.";
var L_RangeError    = "\uBC94\uC704\uC758 \uC2DC\uC791 \uAC12\uC740 \uBC94\uC704\uC758 \uB05D \uAC12\uBCF4\uB2E4 \uC791\uC544\uC57C \uD569\uB2C8\uB2E4.";
var L_NoDateEntered = "\uB0A0\uC9DC\uB97C \uC785\uB825\uD558\uC2ED\uC2DC\uC624.";
var L_Empty         = "\uAC12\uC744 \uC785\uB825\uD558\uC2ED\uC2DC\uC624.";

// Strings for filter dialog
var L_closeDialog="\uCC3D \uB2EB\uAE30";

var L_SetFilter = "\uD544\uD130 \uC124\uC815";
var L_OK        = "\uD655\uC778";
var L_Cancel    = "\uCDE8\uC18C";

 /* Crystal Decisions Confidential Proprietary Information */
