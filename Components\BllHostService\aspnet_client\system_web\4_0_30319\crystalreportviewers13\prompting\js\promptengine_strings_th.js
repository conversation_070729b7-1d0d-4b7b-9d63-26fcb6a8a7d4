/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "\u0E27\u0E31\u0E19\u0E19\u0E35\u0E49";
var L_January   = "\u0E21\u0E01\u0E23\u0E32\u0E04\u0E21";
var L_February  = "\u0E01\u0E38\u0E21\u0E20\u0E32\u0E1E\u0E31\u0E19\u0E18\u0E4C";
var L_March     = "\u0E21\u0E35\u0E19\u0E32\u0E04\u0E21";
var L_April     = "\u0E40\u0E21\u0E29\u0E32\u0E22\u0E19";
var L_May       = "\u0E1E\u0E24\u0E29\u0E20\u0E32\u0E04\u0E21";
var L_June      = "\u0E21\u0E34\u0E16\u0E38\u0E19\u0E32\u0E22\u0E19";
var L_July      = "\u0E01\u0E23\u0E01\u0E0E\u0E32\u0E04\u0E21";
var L_August    = "\u0E2A\u0E34\u0E07\u0E2B\u0E32\u0E04\u0E21";
var L_September = "\u0E01\u0E31\u0E19\u0E22\u0E32\u0E22\u0E19";
var L_October   = "\u0E15\u0E38\u0E25\u0E32\u0E04\u0E21";
var L_November  = "\u0E1E\u0E24\u0E28\u0E08\u0E34\u0E01\u0E32\u0E22\u0E19";
var L_December  = "\u0E18\u0E31\u0E19\u0E27\u0E32\u0E04\u0E21";
var L_Su        = "\u0E2D\u0E32.";
var L_Mo        = "\u0E08.";
var L_Tu        = "\u0E2D.";
var L_We        = "\u0E1E.";
var L_Th        = "\u0E1E\u0E24.";
var L_Fr        = "\u0E28.";
var L_Sa        = "\u0E2A.";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17 \"\u0E2B\u0E21\u0E32\u0E22\u0E40\u0E25\u0E02\" \u0E41\u0E25\u0E30\u0E08\u0E30\u0E15\u0E49\u0E2D\u0E07\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E14\u0E49\u0E27\u0E22\u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E2B\u0E21\u0E32\u0E22\u0E25\u0E1A, \u0E15\u0E31\u0E27\u0E40\u0E25\u0E02 (\"0-9\"), \u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02 \u0E2B\u0E23\u0E37\u0E2D\u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E17\u0E28\u0E19\u0E34\u0E22\u0E21\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19 \u0E42\u0E1B\u0E23\u0E14\u0E41\u0E01\u0E49\u0E44\u0E02\u0E04\u0E48\u0E32\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E17\u0E35\u0E48\u0E1B\u0E49\u0E2D\u0E19";
var L_BadCurrency   = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17 \"\u0E2A\u0E01\u0E38\u0E25\u0E40\u0E07\u0E34\u0E19\" \u0E41\u0E25\u0E30\u0E08\u0E30\u0E15\u0E49\u0E2D\u0E07\u0E1B\u0E23\u0E30\u0E01\u0E2D\u0E1A\u0E14\u0E49\u0E27\u0E22\u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E40\u0E04\u0E23\u0E37\u0E48\u0E2D\u0E07\u0E2B\u0E21\u0E32\u0E22\u0E25\u0E1A, \u0E15\u0E31\u0E27\u0E40\u0E25\u0E02 (\"0-9\"), \u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E01\u0E32\u0E23\u0E08\u0E31\u0E14\u0E01\u0E25\u0E38\u0E48\u0E21\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02 \u0E2B\u0E23\u0E37\u0E2D\u0E2A\u0E31\u0E0D\u0E25\u0E31\u0E01\u0E29\u0E13\u0E4C\u0E17\u0E28\u0E19\u0E34\u0E22\u0E21\u0E40\u0E17\u0E48\u0E32\u0E19\u0E31\u0E49\u0E19 \u0E42\u0E1B\u0E23\u0E14\u0E41\u0E01\u0E49\u0E44\u0E02\u0E04\u0E48\u0E32\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E17\u0E35\u0E48\u0E1B\u0E49\u0E2D\u0E19";
var L_BadDate       = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17 \"\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\" \u0E41\u0E25\u0E30\u0E04\u0E27\u0E23\u0E2D\u0E22\u0E39\u0E48\u0E43\u0E19\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A \"%1\" \u0E42\u0E14\u0E22\u0E17\u0E35\u0E48 \"yyyy\" \u0E04\u0E37\u0E2D\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02\u0E1B\u0E35\u0E2A\u0E35\u0E48\u0E2B\u0E25\u0E31\u0E01 \"mm\" \u0E04\u0E37\u0E2D\u0E40\u0E14\u0E37\u0E2D\u0E19 (\u0E40\u0E0A\u0E48\u0E19 \u0E21\u0E01\u0E23\u0E32\u0E04\u0E21 = 1) \u0E41\u0E25\u0E30 \"dd\" \u0E04\u0E37\u0E2D\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E02\u0E2D\u0E07\u0E40\u0E14\u0E37\u0E2D\u0E19";
var L_BadDateTime   = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17 \"\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E41\u0E25\u0E30\u0E40\u0E27\u0E25\u0E32\" \u0E41\u0E25\u0E30\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A\u0E17\u0E35\u0E48\u0E16\u0E39\u0E01\u0E15\u0E49\u0E2D\u0E07\u0E04\u0E37\u0E2D \"%1 hh:mm:ss\" \"yyyy\" \u0E04\u0E37\u0E2D\u0E15\u0E31\u0E27\u0E40\u0E25\u0E02\u0E1B\u0E35\u0E2A\u0E35\u0E48\u0E2B\u0E25\u0E31\u0E01 \"mm\" \u0E04\u0E37\u0E2D\u0E40\u0E14\u0E37\u0E2D\u0E19 (\u0E40\u0E0A\u0E48\u0E19 \u0E21\u0E01\u0E23\u0E32\u0E04\u0E21 = 1) \"dd\" \u0E04\u0E37\u0E2D\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48\u0E02\u0E2D\u0E07\u0E40\u0E14\u0E37\u0E2D\u0E19 \"hh\" \u0E04\u0E37\u0E2D\u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07\u0E43\u0E19\u0E19\u0E32\u0E2C\u0E34\u0E01\u0E32\u0E41\u0E1A\u0E1A 24 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07 \"mm\" \u0E04\u0E37\u0E2D\u0E19\u0E32\u0E17\u0E35 \u0E41\u0E25\u0E30 \"ss\" \u0E04\u0E37\u0E2D\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35";
var L_BadTime       = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E40\u0E1B\u0E47\u0E19\u0E1B\u0E23\u0E30\u0E40\u0E20\u0E17 \"\u0E40\u0E27\u0E25\u0E32\" \u0E41\u0E25\u0E30\u0E04\u0E27\u0E23\u0E2D\u0E22\u0E39\u0E48\u0E43\u0E19\u0E23\u0E39\u0E1B\u0E41\u0E1A\u0E1A \"hh:mm:ss\" \u0E42\u0E14\u0E22\u0E17\u0E35\u0E48 \"hh\" \u0E04\u0E37\u0E2D\u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07\u0E43\u0E19\u0E19\u0E32\u0E2C\u0E34\u0E01\u0E32\u0E41\u0E1A\u0E1A 24 \u0E0A\u0E31\u0E48\u0E27\u0E42\u0E21\u0E07 \"mm\" \u0E04\u0E37\u0E2D\u0E19\u0E32\u0E17\u0E35 \u0E41\u0E25\u0E30 \"ss\" \u0E04\u0E37\u0E2D\u0E27\u0E34\u0E19\u0E32\u0E17\u0E35";
var L_NoValue       = "\u0E44\u0E21\u0E48\u0E21\u0E35\u0E04\u0E48\u0E32";
var L_BadValue      = "\u0E43\u0E19\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32 \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E04\u0E48\u0E32\" \u0E04\u0E38\u0E13\u0E15\u0E49\u0E2D\u0E07\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32\u0E17\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32 \u0E08\u0E32\u0E01 \u0E41\u0E25\u0E30 \u0E16\u0E36\u0E07 \u0E40\u0E1B\u0E47\u0E19 \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E04\u0E48\u0E32\"";
var L_BadBound      = "\u0E04\u0E38\u0E13\u0E08\u0E30\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32 \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E02\u0E2D\u0E1A\u0E40\u0E02\u0E15\u0E23\u0E30\u0E14\u0E31\u0E1A\u0E25\u0E48\u0E32\u0E07\" \u0E1E\u0E23\u0E49\u0E2D\u0E21\u0E01\u0E31\u0E1A \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E02\u0E2D\u0E1A\u0E40\u0E02\u0E15\u0E23\u0E30\u0E14\u0E31\u0E1A\u0E1A\u0E19\" \u0E44\u0E21\u0E48\u0E44\u0E14\u0E49";
var L_NoValueAlready = "\u0E1E\u0E32\u0E23\u0E32\u0E21\u0E34\u0E40\u0E15\u0E2D\u0E23\u0E4C\u0E19\u0E35\u0E49\u0E44\u0E14\u0E49\u0E23\u0E31\u0E1A\u0E01\u0E32\u0E23\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32\u0E40\u0E1B\u0E47\u0E19 \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E04\u0E48\u0E32\" \u0E41\u0E25\u0E49\u0E27 \u0E43\u0E2B\u0E49\u0E40\u0E2D\u0E32 \"\u0E44\u0E21\u0E48\u0E21\u0E35\u0E04\u0E48\u0E32\" \u0E2D\u0E2D\u0E01\u0E01\u0E48\u0E2D\u0E19\u0E40\u0E1E\u0E34\u0E48\u0E21\u0E04\u0E48\u0E32\u0E2D\u0E37\u0E48\u0E19";
var L_RangeError    = "\u0E08\u0E38\u0E14\u0E40\u0E23\u0E34\u0E48\u0E21\u0E15\u0E49\u0E19\u0E02\u0E2D\u0E07\u0E0A\u0E48\u0E27\u0E07\u0E08\u0E30\u0E21\u0E32\u0E01\u0E01\u0E27\u0E48\u0E32\u0E08\u0E38\u0E14\u0E2A\u0E34\u0E49\u0E19\u0E2A\u0E38\u0E14\u0E02\u0E2D\u0E07\u0E0A\u0E48\u0E27\u0E07\u0E44\u0E21\u0E48\u0E44\u0E14\u0E49";
var L_NoDateEntered = "\u0E04\u0E38\u0E13\u0E15\u0E49\u0E2D\u0E07\u0E1B\u0E49\u0E2D\u0E19\u0E27\u0E31\u0E19\u0E17\u0E35\u0E48";
var L_Empty         = "\u0E42\u0E1B\u0E23\u0E14\u0E1B\u0E49\u0E2D\u0E19\u0E04\u0E48\u0E32";

// Strings for filter dialog
var L_closeDialog="\u0E1B\u0E34\u0E14\u0E2B\u0E19\u0E49\u0E32\u0E15\u0E48\u0E32\u0E07";

var L_SetFilter = "\u0E15\u0E31\u0E49\u0E07\u0E04\u0E48\u0E32\u0E1F\u0E34\u0E25\u0E40\u0E15\u0E2D\u0E23\u0E4C";
var L_OK        = "\u0E15\u0E01\u0E25\u0E07";
var L_Cancel    = "\u0E22\u0E01\u0E40\u0E25\u0E34\u0E01";

 /* Crystal Decisions Confidential Proprietary Information */
