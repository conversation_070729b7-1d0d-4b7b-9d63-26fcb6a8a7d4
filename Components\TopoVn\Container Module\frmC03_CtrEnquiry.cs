using System.ComponentModel;
using System.Data.OracleClient;
using System.Diagnostics;
using System;
using System.Linq;
using Com.TCIS.TopoVn.Bll;
using Com.TCIS.TopoVn.Common;
using ExtensionMethods;
using System.Windows.Forms;
using System.Collections;
using System.Drawing;
using Microsoft.VisualBasic;
using System.Data.OleDb;
using System.Data;
using Com.TCIS.TopoVn.Controls;
using Microsoft.VisualBasic.Compatibility;
using Com.TCIS.TopoVn.ValueObjects;
using TopO_VN.Edi_Module;

namespace TopO_VN
{
    partial class frmC03_CtrEnquiry : System.Windows.Forms.Form
    {
        private const int SiteIdCol = 0;
        private const int ItemNoCol = 1;
        //private const int BBIdCol = 2;
        private const int LocCOL = 2;
        private const int FECol = 3;
        private const int CatCol = 4;
        private const int LOPCol = 5;
        private const int BillOfLadingCol = 6;
        private const int Seal = 7;
        private const int BookNoCol = 8;
        private const int OperationMethodCol = 9;
        private const int ISOCol = 10;
        //private const int LengthCol = 9;
        //private const int HeightCol = 10;
        //private const int TypeCol = 11;
        private const int GrossCol = 11;
        private const int RaNoCol = 12;
        private const int EcnCol = 13;
        private const int StopCOL = 14;
        private const int ArrByCol = 15;
        private const int ArrTsCol = 16;
        private const int DepByCol = 17;
        private const int DepTsCol = 18;
        private const int OriginalPort = 19;
        private const int POL = 20;
        private const int POD = 21;
        private const int LLPOD = 22;
        private const int FPOD = 23;
        private const int dest_stn = 24;
        private const int SRC_STN = 25;
        //private const int CPACol = 27;
        private const int ItemKeyCol = 26;
        private const int HistFlgCol = 27;
        //private const int IsAttCol = 30;
        //private const int BundleToCol = 31;
        //private const int UnBundleFrCol = 32;
        //private const int BundlekeyCol = 33;
        //private const int UnBundlekeyCol = 34;
        //private const int IsIPACol = 27;
        //private const int IsDisCol = 28;
        //private const int IsBapCol = 29;
        //private const int IsPrtCol = 30;
        //private const int C_NCCol = 28;
        private const int ImpCustomCheckCol = 28;
        private const int ExpCustomCheckCol = 29;
        private const int C80CustomCol = 30;
        private const int OwnerCol = 31;
        private const int RemarkCol = 32;

        int FirstSel;
        int LastSel;
        int pCnt;

        int lngItemKey;

        int ctrFound;
        bool IsStop;
        bool isAttach;
        int SlaveCnt;
        int SelCnt;

        DataTable gDt;
        DataTable aDt;
        DataTable stDt;
        int StCnt;

        //public frmCPP_SearchFilter frmCPP = new frmCPP_SearchFilter();
        public frmCPP_SearchFilter frmCPP;                                      //Hao, 10/06/2010

        private IContainerBE _containerBe = UnityInstanceProvider.CreateInstance<IContainerBE>();

        //------------------------------------------------------------------------------------------------------------------------------
        private void load_header()
        {
            DGV.Rows.Clear();
            if (DGV.Columns.Count > 0)
            {
                return;
            }
            DGV.Columns.Add("", "Site ID");
            DGV.Columns.Add("", "Ctnr No");
            //DGV.Columns.Add("", "BB ID");
            DGV.Columns.Add("", "Location");
            DGV.Columns.Add("", "F/E");
            DGV.Columns.Add("", "Category");
            DGV.Columns.Add("", "Liner");
            DGV.Columns.Add("", "Bill_Of_Lading");
            DGV.Columns.Add("", "Seal");
            DGV.Columns.Add("", "Book_No");
            DGV.Columns.Add("", "Operation Method");
            DGV.Columns.Add("", "ISO");
            //DGV.Columns.Add("", "Length");
            //DGV.Columns.Add("", "Height");
            //DGV.Columns.Add("", "Type");
            DGV.Columns.Add("", "Gross");
            DGV.Columns.Add("", "RA No");
            DGV.Columns.Add("", "ECN");
            DGV.Columns.Add("", "Stop");
            DGV.Columns.Add("", "Arr By");
            DGV.Columns.Add("", "Arr Time");
            DGV.Columns.Add("", "Dep By");
            DGV.Columns.Add("", "Dep Time");
            DGV.Columns.Add("", "Original Port");
            DGV.Columns.Add("", "POL");
            DGV.Columns.Add("", "POD");
            DGV.Columns.Add("", "LLPOD");
            DGV.Columns.Add("", "FPOD");
            DGV.Columns.Add("", "Delivery Place");
            DGV.Columns.Add("", "Receival Place");
            //DGV.Columns.Add("", "CPA");
            DGV.Columns.Add("", "itemkey");
            DGV.Columns.Add("", "histflg");
            //DGV.Columns.Add("", "isatt");
            //DGV.Columns.Add("", "Bundle To");
            //DGV.Columns.Add("", "UnBundle From");
            //DGV.Columns.Add("", "Bundlekey");
            //DGV.Columns.Add("", "UnBundle");
            //DGV.Columns.Add("", "isIPA");
            //DGV.Columns.Add("", "isDisch");
            //DGV.Columns.Add("", "isBaplie");
            //DGV.Columns.Add("", "isPrint");
            //DGV.Columns.Add("", "C/NC");
            DGV.Columns.Add("", "ImpCustomCheck");
            DGV.Columns.Add("", "ExpCustomCheck");
            DGV.Columns.Add("", "C80Custom");
            DGV.Columns.Add("", "Owner");
            DGV.Columns.Add("", "Remarks");

            DGV.Columns[ItemNoCol].Frozen = true;

            DGV.Columns[0].DefaultCellStyle.Font = new Font(DGV.Font, FontStyle.Bold);
            DGV.Columns[0].Width = System.Convert.ToInt32(DGV.Columns[0].Width * (DGV.Columns[0].Width / 2));

            ModuleMisc.DataGridView_Display(DGV, "N", "Y", "Y", "Y", "N", true);
            ModuleMultiLang.Multi_Language_Heading(this, DGV);

        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void MoveLabel(string Flg)
        {

        }


        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrISO_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.CboUpper(cboCtrISO);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrISO_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.load_Iso(ref cboCtrISO);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrISO_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(cboCtrISO.Text.MyTrim()))
            {
                cboCtrISO.Text = ModuleMisc.delSingleQuote(cboCtrISO.Text.MyTrim());
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrNonCtr_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.CboUpper(cboCtrNonCtr);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrNonCtr_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleSyscodes.Load_SyscodeView(ref cboCtrNonCtr, "CTRCLASS");
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrNonCtr_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(cboCtrNonCtr.Text.MyTrim()))
            {
                cboCtrNonCtr.Text = ModuleMisc.delSingleQuote(cboCtrNonCtr.Text.MyTrim());
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrType_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.CboUpper(cboCtrType);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrType_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleSyscodes.Load_SyscodeView(ref cboCtrType, "CTRTYPE");
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboCtrType_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(cboCtrType.Text.MyTrim()))
            {
                cboCtrType.Text = ModuleMisc.delSingleQuote(cboCtrType.Text.MyTrim());
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboItemClass_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.CboUpper(cboItemClass);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboItemClass_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleSyscodes.Load_SyscodeView(ref cboItemClass, "IMPEXP");
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboItemClass_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(cboItemClass.Text.MyTrim()))
            {
                cboItemClass.Text = ModuleMisc.delSingleQuote(cboItemClass.Text.MyTrim());
            }
        }



        //------------------------------------------------------------------------------------------------------------------------------
        public void cboLOP_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.CboUpper(cboLOP);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboLOP_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.Load_CBOView(ref cboLOP, "LINE_OPER, FULL_NAME", "line_oper", "", 0, 0, 0, 0);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboLOP_Enter(object sender, System.EventArgs e)
        {
            ModuleMisc.HighlightControl((Control)sender);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboLOP_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(cboLOP.Text.MyTrim()))
            {
                cboLOP.Text = ModuleMisc.delSingleQuote(cboLOP.Text.MyTrim());
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cboSiteID_DropDown(System.Object eventSender, System.EventArgs eventArgs)
        {
            Load_SiteID();
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private void Load_SiteID()
        {
            DataTable dt;
            string strSavedvalue;
            string[] ListHeader;
            string[] ListDetails;
            int i;
            int iZero = 0;

            strSavedvalue = cboSiteId.Text;
            cboSiteId.Items.Clear();
            cboSiteId.Text = strSavedvalue;

            try
            {

                dt = ModuleADODB.Ok_ADODB("*", "sys_codes", " WHERE code_tp = \'SITEID\' order by code_ref");
                if (dt.Rows.Count == 0)
                {
                    return;
                }


                ListHeader = new string[] { "Site Id", "Description" };

                frmListView.Default.ListViewHeader(ListHeader, ref cboSiteId, ref iZero);

                for (i = 0; i <= dt.Rows.Count - 1; i++)
                {
                    if (dt.Rows[i]["code_ref"].ToString().MyTrim() != mGlobal.gSiteId)
                    {


                        ListDetails = new string[] { dt.Rows[i]["code_ref"].ToString().MyTrim(), dt.Rows[i]["Descr"].ToString().MyTrim() };
                        frmListView.Default.ListViewAdd(ListDetails, 2);
                    }
                }
                frmListView.Default.ShowDialog(this); //Duc modified on 30/05/2008

            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "Load_SiteID");
            }
        }



        //------------------------------------------------------------------------------------------------------------------------------
        public void chkFull_CheckStateChanged(System.Object eventSender, System.EventArgs eventArgs)
        {

            if (chkFull.Checked)
            {
                chkMty.CheckState = System.Windows.Forms.CheckState.Unchecked;
            }
        }



        //------------------------------------------------------------------------------------------------------------------------------
        public void chkMty_CheckStateChanged(System.Object eventSender, System.EventArgs eventArgs)
        {

            if (chkMty.Checked)
            {
                chkFull.CheckState = System.Windows.Forms.CheckState.Unchecked;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdCancel_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            
            cmdEnquiry.Enabled = true;

            IsStop = true;
            txtCtrNo.Text = "";
            lblCtrFound.Text = "0";
            txtSel.Text = "0";
            txtSealNo.Text = "";
            txtBlock.Text = "";
            cboLOP.Text = "";
            cboSiteId.Text = "";
            SelCnt = 0;

            cboCtrType.Text = "";
            cboCtrISO.Text = "";
            this.cboItemClass.Text = "";
            chkAttach.CheckState = CheckState.Unchecked;
            chkCPA.CheckState = CheckState.Unchecked;
            chkBlankSeal.CheckState = CheckState.Unchecked;
            chkFull.CheckState = CheckState.Unchecked;
            chkMty.CheckState = CheckState.Unchecked;

            ctrFound = 0;
            cmdContinue.Enabled = false;
            cmdStop.Enabled = false;
            ProgBar.Value = 0;
            ProgBar.Visible = false;
            lblAttach.Visible = false;
            txtAttach.Visible = false;
            txtPreadvice.Text = "0";
            txtDischarge.Text = "0";
            txtBaplie.Text = "0";
            txtInYard.Text = "0";
            txtHist.Text = "0";
            txtReleaseNo.Text = "";

            txtReleaseNo.Text = "";
            cboCtrNonCtr.Text = "";

            load_header();

            ModuleMisc.Store_Tag(this);

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdClose_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (IsStop == false)
            {
                IsStop = true;
                cmdContinue.Enabled = true;
                cmdStop.Enabled = false;
                cmdEnquiry.Enabled = true;
                return;
            }

            this.Close();
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdContinue_Click(System.Object eventSender, System.EventArgs eventArgs)
        {

            load_ContainerList();
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdEnquiry_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            string sEmpty = "";
            int iZero = 0;

            try
            {

                cmdEnquiry.Enabled = false;
                cmdClose.Enabled = false;
                cmdCancel.Enabled = false;
                cmdGlobalSearch.Enabled = false;

                string strCtrNo;

                DataTable dt;
                bool tst;
                if (optOtherSites.Checked && cboSiteId.Text.MyTrim() == "")
                {
                    ModuleErrMsg.Ok_ErrMsg(ref cboSiteId, 0, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                    return;
                }
                else if (cboSiteId.Text.MyTrim() != "")
                {
                    if (!ModuleSyscodes.IsValidSyscode("SITEID", cboSiteId.Text.MyTrim()))
                    {
                        ModuleErrMsg.Ok_ErrMsg(ref cboSiteId, 1, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                        return;
                    }
                }

                DGV.Rows.Clear();
                lblCtrFound.Text = "0";
                txtPreadvice.Text = "0";
                txtDischarge.Text = "0";
                txtBaplie.Text = "0";
                txtHist.Text = "0";
                txtInYard.Text = "0";
                txtSel.Text = "0";
                txtPlan.Text = "0";
                SelCnt = 0;

                IsStop = false;
                cmdStop.Enabled = true;
                cmdStop.Focus();
                isAttach = false;
                lblAttach.Visible = false;
                txtAttach.Visible = false;
                if (optAllCtrs.Checked)
                {
                    if (cboCtrNonCtr.Text.MyTrim() == "")
                    {
                        cboCtrNonCtr.Text = "C";
                    }
                }
                load_header();
                if (optLocalSite.Checked)
                {
                    DGV.Columns[SiteIdCol].Visible = false;
                }

                //if (cboCtrNonCtr.Text.MyTrim() != "N")
                //{
                //    DGV.Columns[BBIdCol].Visible = false;
                //}
                StCnt = 0;
                this.Refresh();
                load_ContainerList();
                this.Cursor = System.Windows.Forms.Cursors.Default;
                DGV.Cursor = System.Windows.Forms.Cursors.Default;

                IsStop = true;

            }
            catch (Exception)
            {

            }
            finally
            {

                cmdEnquiry.Enabled = true;
                cmdClose.Enabled = true;
                cmdCancel.Enabled = true;
                cmdGlobalSearch.Enabled = true;

            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdGlobalSearch_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            frmCPP = new frmCPP_SearchFilter();             //Hao, 10/06/2010
            frmCPP.ShowDialog(this);
            frmCPP.Dispose();                               //Hao, 10/06/2010
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdStop_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            IsStop = true;
            cmdContinue.Enabled = true;
            cmdStop.Enabled = false;
            cmdEnquiry.Enabled = true;
            this.Cursor = System.Windows.Forms.Cursors.Default;

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void frmCtrEnquiry_KeyUp(System.Object eventSender, System.Windows.Forms.KeyEventArgs eventArgs)
        {
            int KeyCode = (int)eventArgs.KeyCode;
            int Shift = (int)eventArgs.KeyData / 0x10000;
            int i;
            switch (eventArgs.KeyCode)
            {

                case System.Windows.Forms.Keys.F2:

                    cmdEnquiry_Click(cmdEnquiry, new System.EventArgs());
                    break;
                case System.Windows.Forms.Keys.F4:

                    if (cmdStop.Enabled)
                    {
                        cmdStop_Click(cmdStop, new System.EventArgs());
                    }
                    break;
                case System.Windows.Forms.Keys.F5:

                    cmdGlobalSearch_Click(cmdGlobalSearch, new System.EventArgs());
                    break;
                case System.Windows.Forms.Keys.F6:

                    cmdCancel_Click(cmdCancel, new System.EventArgs());
                    break;
                case System.Windows.Forms.Keys.F7:

                    if (cmdContinue.Enabled)
                    {
                        cmdContinue_Click(cmdContinue, new System.EventArgs());
                    }
                    break;

                case System.Windows.Forms.Keys.F12:

                    cmdClose_Click(cmdClose, new System.EventArgs());
                    break;
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void frmCtrEnquiry_Load(System.Object eventSender, System.EventArgs eventArgs)
        {
            // Set up user's Id
            // ==================

            clearForm();


            load_header();
            MoveLabel("D");

            ToolTip1.SetToolTip(cboLOP, "Data from S13 - Line Oper Maintenance");
            ToolTip1.SetToolTip(cboSiteId, "Data from S52 - System Codes Maintenance, Code Type = SITEID");
            ToolTip1.SetToolTip(cboCtrISO, "Data from S12 - ISO Codes Maintenance");
            ToolTip1.SetToolTip(cboCtrType, "Data from S52 - System Codes Maintenance, Code Type = CTRTYPE");
            ToolTip1.SetToolTip(cboItemClass, "Data from S52 - System Codes Maintenance, Code Type = IMPEXP");
            ToolTip1.SetToolTip(cboCtrNonCtr, "Data from S52 - System Codes Maintenance, Code Type = CTRCLASS");
            IsStop = true;

            System.Windows.Forms.Form InForm = this;
            ModuleMultiLang.multi_language_captions(ref InForm, ref ToolTip1, "");
            ProgBar.Visible = false;

            EnquiryOnly = true;
        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void clearForm()
        {
            txtCtrNo.Text = "";
            lblCtrFound.Text = "";
            DGV.Rows.Clear();

        }

        //------------------------------------------------------------------------------------------------------------------------------
        private bool Get_RS(ref DataTable dt, string itemNo = "")
        {
            bool returnValue = false;
            string strSQL = "";
            string strWhereAtt;
            string strTable;
            string strSelect;
            string strwhere;
            string AndCl;
            string strStop;
            string strAtt;
            strAtt = "";
            strStop = "";
            dt = null;
            string sEmpty = "";
            int iZero = 0;

            try
            {

                SlaveCnt = 0;
                strSelect = "SELECT i.item_no,i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, "
                            +
                            "i.BILL_OF_LADING, i.SEAL_NO_CURRENT, i.BOOK_NO, GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS," //@TUANTLM 17/05/2018 - Yêu cầu của TTĐĐ TCHP
                            +
                            "i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by, i.item_key,  i.line_oper, "
                            +
                            "i.is_attach,i.bundle_to,i.UNBUNDLE_FROM, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, "
                            +
                            "i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,i.bb_id, i.RELEASE_NO, "
                            + "il.STK_REF,il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner,"
                            +
                            " NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <=1), ' ') AS CLR_BY,"
                            + " CASE WHEN i.EIR_ID=' ' THEN ' ' ELSE " +
                            "   NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <=1), ' ') " +
                            "   END OPERATION_METHOD, " +
                            "   NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check, " +
                            "   NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check," +
                            "   NVL((SELECT 'Y' AS Exp_Custom_Check FROM CUSTOM_CLEARANCE tq WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check";

                AndCl = " and ";
                if (optLocalSite.Checked)
                {
                    strwhere = " where i.site_id = \'" + mGlobal.gSiteId.MyTrim() + "\' ";
                }
                else if (optOtherSites.Checked)
                {
                    strwhere = " where i.site_id = \'" + cboSiteId.Text.MyTrim() + "\' ";
                }
                else
                {
                    strwhere = " where  ";
                    AndCl = " ";
                }
                strWhereAtt = strwhere + AndCl;
                strTable = " FROM item i, item_location il";

                strwhere = strwhere + AndCl + " i.item_key = il.item_key and il.stk_pch = \'C\'";
                AndCl = " and ";
                if (optAllCtrs.Checked == true)
                {
                    lblDGVHeading.Text = "Containers In Yard|";
                    if (chkAttach.Checked)
                    {
                        strSelect = strSelect + ",ia.item_id,ia.LINE_OPER as ia_LINE_OPER,ia.ISO as ia_ISO,ia.GROSS as ia_gross,";
                        strSelect = strSelect + "ia.LENGTH as ia_LENGTH,ia.ITEM_TYPE as ia_ITEM_TYPE,ATT_ITEM_KEY";
                        strTable = strTable + ",item_subs ia";
                        strwhere = strwhere + " and i.item_key = ia.item_key  and NEW_ATT_KEY = 0  ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Attached|";
                    }
                    strwhere = strwhere + AndCl + " (i.hist_flg = \' \' or i.hist_flg = \'N\') ";
                    if (txtBlock.Text.MyTrim() != "")
                    {
                        strwhere = strwhere + " AND il.stk_class = \'Y\' AND il.stack = \'" + txtBlock.Text.MyTrim() + "\'";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Block: " + txtBlock.Text.MyTrim() + "|";

                    }


                    if (cboLOP.Text.MyTrim() != "")
                    {
                        strwhere = strwhere + " AND i.line_oper = \'" + cboLOP.Text.MyTrim() + "\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Liner: " + cboLOP.Text.MyTrim() + "|";

                    }

                    if (cboCtrType.Text.MyTrim() != "")
                    {
                        strwhere = strwhere + " AND i.item_type = \'" + cboCtrType.Text.MyTrim() + "\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Ctnr Type: " + cboCtrType.Text.MyTrim() + "|";

                    }
                    if (cboCtrISO.Text.MyTrim() != "")
                    {
                        if (cboCtrISO.Text.MyTrim().Length == 4)
                        {
                            strwhere = strwhere + " AND i.iso = \'" + cboCtrISO.Text.MyTrim() + "\' ";
                        }
                        else
                        {
                            strwhere = strwhere + " AND i.iso LIKE \'" + cboCtrISO.Text.MyTrim() + "%\' ";
                        }
                        lblDGVHeading.Text = lblDGVHeading.Text + "ISO: " + cboCtrISO.Text.MyTrim() + "|";
                    }


                    if (chkFull.Checked)
                    {
                        strwhere = strwhere + " AND i.fel = \'F\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Full Ctnr|";

                    }

                    else if (chkMty.Checked)
                    {
                        strwhere = strwhere + " AND i.fel = \'E\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "Empty Ctnr|";

                    }

                    if (cboItemClass.Text.MyTrim() != "")
                    {
                        strwhere = strwhere + " AND i.CATEGORY = \'" + cboItemClass.Text.MyTrim() + "\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "CATEGORY: " + cboItemClass.Text.MyTrim() + "|";

                    }

                    if (cboCtrNonCtr.Text.MyTrim() != "")
                    {
                        if (cboCtrNonCtr.Text.MyTrim() == "N")
                        {
                            strwhere = strwhere + " AND i.ITEM_CLASS = \'N\' ";
                            lblDGVHeading.Text = lblDGVHeading.Text + "C/NC: " + cboCtrNonCtr.Text.MyTrim() + "|";

                        }
                        else
                        {
                            strwhere = strwhere + " AND (i.ITEM_CLASS <> \'N\') ";
                            lblDGVHeading.Text = lblDGVHeading.Text + "C/NC: " + cboCtrNonCtr.Text.MyTrim() + "|";

                        }
                    }


                    if (chkCPA.Checked)
                    {
                        strwhere = strwhere + " AND i.CPA_XRAY_REQUIRED = \'Y\' ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "CPA_XRAY_REQUIRED|";

                    }


                    if (chkBlankSeal.Checked)
                    {
                        strwhere = strwhere + " AND i.item_key not in (select item_key from item_seal) ";
                        lblDGVHeading.Text = lblDGVHeading.Text + "No Seal|";

                    }

                    strSQL = strSelect + strTable + strwhere;

                    strSQL = strSQL + " And i.ARR_TS > to_date(\'01/01/1901\'," + mGlobal.DBStrDMY + ") ";


                }
                else if (optOnrCtr.Checked == true)
                {

                    if (txtCtrNo.Text.MyTrim() == "")
                    {
                        ModuleErrMsg.Ok_ErrMsg(ref txtCtrNo, 293, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                        returnValue = false;
                        return returnValue;
                    }

                    if (txtCtrNo.Text.MyTrim().Length == 11)
                    {
                        strwhere = strwhere + AndCl + " i.item_no = " + "\'" + txtCtrNo.Text.MyTrim() + "\' ";
                    }
                    else
                    {
                        strwhere = strwhere + AndCl + " i.item_no LIKE " + "\'%" + txtCtrNo.Text.MyTrim() + "%" + "\' ";
                    }
                    lblDGVHeading.Text = "Ctnr no: " + txtCtrNo.Text.MyTrim() + "";


                    strSQL = strSelect + strTable + strwhere;

                }
                else if (optOnSealNo.Checked == true)
                {
                    if (txtSealNo.Text.MyTrim() == "")
                    {
                        ModuleErrMsg.Ok_ErrMsg(ref txtSealNo, 0, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                        returnValue = false;
                        return returnValue;
                    }

                    strwhere = strwhere + AndCl + " i.item_key = isl.item_key " + " AND isl.SEAL_NO LIKE " + "\'" + ModuleMisc.check_blank(txtSealNo.Text) + "%" + "\' ";


                    strTable = strTable + ",item_seal isl";

                    strSQL = strSelect + strTable + strwhere;
                    strSQL = strSQL + " And i.ARR_TS > to_date(\'01/01/1901\'," + mGlobal.DBStrDMY + ") ";
                    lblDGVHeading.Text = "Seal number: " + txtSealNo.Text.MyTrim() + "";


                }
                else if (optReleaseNo.Checked == true)
                {
                    if (txtReleaseNo.Text.MyTrim() == "")
                    {
                        ModuleErrMsg.Ok_ErrMsg(ref txtReleaseNo, 0, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                        returnValue = false;
                        return returnValue;
                    }

                    strwhere = strwhere + AndCl + " i.RELEASE_NO LIKE " + "\'" + txtReleaseNo.Text.MyTrim() + "%" + "\' ";

                    strSQL = strSelect + strTable + strwhere;

                    strSQL = strSQL + " And i.ARR_TS > to_date(\'01/01/1901\'," + mGlobal.DBStrDMY + ") ";
                    lblDGVHeading.Text = "Release number: " + txtReleaseNo.Text.MyTrim() + "";

                }
                else if (optOnListCont.Checked)
                {
                    strSQL = strSelect + strTable + strwhere;
                    strSQL = strSQL + string.Format(" And i.Item_No =  '{0}' And i.Arr_Ts >= SYSDATE - {1} ", itemNo, txtDayPeriod.Text.Trim());
                    
                }


                strSQL = strSQL + " and rownum < 105002 ORDER BY i.item_no,i.item_key ";

                strStop = "select ist.* from item i, item_stops ist where " + " i.site_id = \'" + mGlobal.gSiteId.MyTrim() + "\'  and (i.hist_flg = \' \' or i.hist_flg = \'N\') " + " and i.item_key = ist.item_key order by crt_ts desc";


                if (strSQL.MyTrim() != "")
                {
                    dt = ModuleADODB.T_ADODB(strSQL);
                    //stDt = ModuleADODB.T_ADODB(strStop);

                    returnValue = true;
                }
                else
                {
                    returnValue = false;
                }
            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "Get_RS");
            }

            return returnValue;
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private string getLocation(int i, DataTable dt, int ItemKey)
        {
            string returnValue;
            string conInfo;
            string strSQL;
            string strList;
            string Message;
            string FE;
            string strY;
            string strStkRef;
            string strX;
            string strZ;
            string strClass;

            try
            {

                returnValue = " ";

                strClass = gDt.Rows[i]["stk_class"].ToString();

                strStkRef = gDt.Rows[i]["stk_ref"].ToString();

                strX = gDt.Rows[i]["x"].ToString();

                strY = gDt.Rows[i]["y"].ToString();

                strZ = gDt.Rows[i]["z"].ToString();
                mGlobal.strStack = gDt.Rows[i]["stack"].ToString();

                if (strClass.MyTrim() == "V")
                {
                    returnValue = strStkRef.MyTrim() + " " + ModuleContainer.Set_XYZ(strClass, strX, strY, strZ);

                }
                else if (strClass.MyTrim() == "Y")
                {
                    if (mGlobal.strStack.MyTrim() != "")
                    {
                        returnValue = mGlobal.strStack.MyTrim() + " " + ModuleContainer.Set_XYZ(strClass, strX, strY, strZ);
                    }
                    else
                    {
                        returnValue = strStkRef.MyTrim();
                    }
                }
                else if (strClass.MyTrim() == "R")
                {

                    returnValue = "On wagon " + strStkRef.MyTrim();
                }
                else if (strClass.MyTrim() == "T")
                {

                    returnValue = "On truck " + strStkRef.MyTrim();
                }
                else if (strClass.MyTrim() == "C")
                {
                    returnValue = "Community";
                }

            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "item_location");
                returnValue = " ";
            }
            return returnValue;
        }
        //------------------------------------------------------------------------------------------------------------------------------

        /// <summary>
        ///There are a number of search options that can be used to quickly locate 
        ///containers as there are a vast number of containers within the System at 
        ///any one time. Select from the various options by clicking in the circles 
        ///or the checkboxes or select from dropdowns. 
        /// </summary>
        private void load_ContainerList()
        {
            object strList;
            object strSQL;
            object conInfo;
            string FE;
            int ItemKey;
            int HistCnt;
            int row;
            int i;
            int InYardCnt;
            int PlanCnt;
            string DepBy;
            string ArrBy;
            string gross;
            string Length;
            string Height;
            string ArrTs;
            string DepTs;
            string consignee;
            string Lop;
            string OperationMethod;
            string strLocation;
            string strHistFfg;
            string ItemNo;
            string BdlTo;
            string UnBdlFr;
            string ClearHeld;
            string CurAttID;
            string strStop;
            string EcnNo;
            string Owner;

            string impCustomCheck;
            string expCustomCheck;
            string c80CustomCheck;

            string sEmpty = "";
            int iZero = 0;
            
            try
            {
                
                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
                gDt = new DataTable();
                if (!IsStop)
                {
                    if (optOnListCont.Checked)
                    {
                        //var conts = txtListCont.Text.Trim();

                        string[] conts = txtListCont.Text.Split(',');

                        if (conts.Length == 1)
                        {
                            conts = txtListCont.Text.Split(';');
                        }

                        if (conts.Length == 1)
                        {
                            conts = txtListCont.Text.Split('\n');
                        }

                        gDt = null;
                        for (int j = 0; j < conts.Length; j++)
                        {
                            if (!conts[j].IsEmpty())
                            {
                                var itemNo = conts[j].Trim();

                                itemNo = itemNo.Replace(" ", "");
                                itemNo = itemNo.Replace("\r", "");
                                itemNo = itemNo.Replace("\t", "");

                                DataTable dt = null;

                                Get_RS(ref dt, itemNo);

                                if (gDt == null)
                                {
                                    if (dt != null)
                                    {
                                        gDt = dt.Clone();
                                        foreach (DataRow row1 in dt.Rows)
                                        {
                                            gDt.ImportRow(row1);    
                                        }
                                        
                                    }
                                }
                                else
                                {
                                    if (dt != null)
                                    {
                                        foreach (DataRow row1 in dt.Rows)
                                        {
                                            gDt.ImportRow(row1);
                                        }
                                    }
                                }
                                ///....
                            }
                        }
                    } 

                    if (gDt == null)
                    {
                        this.Cursor = System.Windows.Forms.Cursors.Default;

                        cmdStop.Enabled = false;
                        return;
                    }

                    if (!optOnListCont.Checked && !Get_RS(ref gDt))
                    {
                        this.Cursor = System.Windows.Forms.Cursors.Default;
                        return;
                    }
                    else
                    {
                        row = 0;
                        if (gDt.Rows.Count == 0)
                        {
                            if (optOnrCtr.Checked == true)
                            {
                                //Get_Item_Attach(ref row);
                                CheckOtherTable(ref row);
                                ModuleMisc.DataGridView_Display(DGV, "N", "N", "Y", "Y", "N", true);
                                DGV.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                                if (chkAttach.Checked)
                                {
                                    DGV.Columns[3].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                                }
                                lblCtrFound.Text = DGV.Rows.Count.ToString();
                                lblCtrFound.Refresh();
                                if (lblCtrFound.Text.MyTrim() == "0")
                                {
                                    ModuleErrMsg.ErrMsg(57, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                                }
                            }
                            else
                            {
                                ModuleErrMsg.ErrMsg(57, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);
                                lblCtrFound.Text = 0.ToString();
                            }

                            this.Cursor = System.Windows.Forms.Cursors.Default;

                            cmdStop.Enabled = false;


                            return;
                        }
                        else if (gDt.Rows.Count > 105000)
                        {
                            ModuleErrMsg.ErrMsg(412, ref sEmpty, ref sEmpty, ref sEmpty, ref iZero);

                            ProgBar.Maximum = 105000;
                        }
                        else
                        {
                            ProgBar.Maximum = gDt.Rows.Count;
                        }

                    }
                }

                if (gDt == null)
                {
                       this.Cursor = System.Windows.Forms.Cursors.Default;
                       cmdStop.Enabled = false;
                       return;
                }

                IsStop = false;
                cmdContinue.Enabled = false;
                cmdStop.Enabled = true;

                HistCnt = ModuleMisc.check_CInt(txtHist.Text);
                InYardCnt = ModuleMisc.check_CInt(txtInYard.Text);
                PlanCnt = ModuleMisc.check_CInt(txtPlan.Text);
                ProgBar.Visible = true;

                strStop = "";
                row = StCnt;
                cmdEnquiry.Enabled = false;
                int rowCnt = gDt.Rows.Count - 1;
                
                for (i = StCnt; i <= rowCnt ; i++)
                {
                    #region Fill data
                    System.Windows.Forms.Application.DoEvents();
                    if (IsStop)
                    {
                        ProgBar.Visible = false;
                        StCnt = row;

                        if (DGV.Columns.Count == 0)
                        {
                            cmdStop.Enabled = false;
                            return;
                        }

                        DGV.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                        this.Cursor = System.Windows.Forms.Cursors.Default;
                        cmdStop.Enabled = false;
                        return;
                    }

                    

                    CurAttID = "";

                    

                    ArrTs = gDt.Rows[i]["arr_ts"].ToString();

                    
                    if (((DateTime)gDt.Rows[i]["arr_ts"]).IsNullDateTime())
                    {

                        ArrTs = "";
                    }

                    if (gDt.Rows[i]["arr_car"].ToString().Trim() == "")
                    {

                        ArrBy = gDt.Rows[i]["arr_by"].ToString();
                    }
                    else
                    {

                        //ArrBy = System.Convert.ToString(gDt.Rows[i]["arr_car"]).MyTrim() + "/" + System.Convert.ToString(gDt.Rows[i]["arr_by"]).MyTrim();

                        ArrBy = gDt.Rows[i]["arr_car"].ToString().Trim() + "/" + gDt.Rows[i]["arr_by"].ToString();
                    }

                    DepTs = ModuleDateTime.FormatDate(gDt.Rows[i]["dep_ts"].ToString(), mGlobal.StrDMYHM);

                    if (DepTs == mGlobal.NullDateHM)
                    {
                        DepTs = "";
                    }

                    if (gDt.Rows[i]["dep_car"].ToString().Trim() == "")
                    {

                        DepBy = gDt.Rows[i]["dep_by"].ToString();
                    }
                    else
                    {

                        DepBy = gDt.Rows[i]["dep_car"].ToString().Trim() + "/" + gDt.Rows[i]["dep_by"].ToString();
                    }


                    if (ArrTs.Trim() == "")
                    {
                        strHistFfg = "P";
                    }
                    strStop = "";
                    //ItemKey = Convert.ToInt32(gDt.Rows[i]["item_key"].ToString());
                    //ClearHeld = CheckStop(ItemKey);
                    ClearHeld = gDt.Rows[i]["CLR_BY"].ToString();

                    if (ClearHeld.IsEmpty())
                    {
                        ClearHeld = "CLEAR";
                    }
                    else
                    {
                        ClearHeld = "HELD";
                    }
                    
                    if (ModuleMisc.check_Clng(System.Convert.ToString(gDt.Rows[i]["bundle_to"])) > 0)
                    {
                        
                        BdlTo = ModuleADODB.Get_String_Where("item_no", "item", "item_key", ModuleMisc.check_Clng(System.Convert.ToString(gDt.Rows[i]["bundle_to"])).ToString(), "N");
                    }
                    else
                    {
                        BdlTo = "";
                    }
                    if (ModuleMisc.check_Clng(System.Convert.ToString(gDt.Rows[i]["UNBUNDLE_FROM"])) > 0)
                    {
                        
                        UnBdlFr = ModuleADODB.Get_String_Where("item_no", "item", "item_key", ModuleMisc.check_Clng(System.Convert.ToString(gDt.Rows[i]["UNBUNDLE_FROM"])).ToString(), "N");
                    }
                    else
                    {
                        UnBdlFr = "";
                    }
                    
                    //strLocation = getLocation(i, gDt, ItemKey);
                    strLocation = getLocation(i, gDt, 0);
                    
                    
                    //var customClearance = _containerBe.GetCurrentCustomClearance(ItemKey);
                    
                    //if (customClearance != null)
                    //{
                    //    EcnNo = customClearance.EcnNo;
                    
                    //}
                    //else
                    //{
                    //    EcnNo = "";
                    //}

                    //FE = gDt.Rows[i]["fel"].ToString();
                    //ItemNo = gDt.Rows[i]["item_no"].ToString();

                    //Length = gDt.Rows[i]["length"].ToString();

                    //Height = gDt.Rows[i]["height"].ToString();

                    //gross = (gDt.Rows[i]["gross"].CheckDouble() * 1000).ToString();

                    //Lop = gDt.Rows[i]["line_oper"].ToString();

                    //OperationMethod = ModuleContainer.GetOperationMethod(gDt.Rows[i]["item_KEY"].MyTrim().CheckInt());
                    //OperationMethod = gDt.Rows[i]["Operation_Method"].ToString();
                    
                    //strHistFfg = gDt.Rows[i]["hist_flg"].ToString();

                    EcnNo = "";
                    DGV.Rows.Insert(row,
                        gDt.Rows[i]["site_id"],
                        gDt.Rows[i]["item_no"],
                        //gDt.Rows[i]["bb_id"],
                        strLocation, 
                        gDt.Rows[i]["fel"],
                        gDt.Rows[i]["category"],
                        gDt.Rows[i]["line_oper"],
                        gDt.Rows[i]["BILL_OF_LADING"],
                        gDt.Rows[i]["SEAL_NO_CURRENT"],
                        gDt.Rows[i]["BOOK_NO"],
                        gDt.Rows[i]["Operation_Method"],
                        gDt.Rows[i]["ISO"],
                        //gDt.Rows[i]["length"], 
                        //gDt.Rows[i]["height"],
                        //gDt.Rows[i]["item_type"],
                        gDt.Rows[i]["gross"], 
                        gDt.Rows[i]["RELEASE_NO"], 
                        EcnNo, //Ecn No
                        ClearHeld, 
                        ArrBy, ArrTs,
                        DepBy, DepTs,
                        gDt.Rows[i]["ORG_PORT"],
                        gDt.Rows[i]["LOAD_PORT"],
                        gDt.Rows[i]["DISCH_PORT"],
                        gDt.Rows[i]["LL_DISCH_PORT"],
                        gDt.Rows[i]["FDISCH_PORT"],
                        gDt.Rows[i]["PLACE_OF_DELIVERY"],
                        gDt.Rows[i]["PLACE_OF_RECEIPT"],
                        //gDt.Rows[i]["CPA_XRAY_REQUIRED"],
                        gDt.Rows[i]["item_key"], 
                        gDt.Rows[i]["hist_flg"], 
                        //"", //isatt
                        //BdlTo, 
                        //UnBdlFr,
                        //gDt.Rows[i]["bundle_to"], //Bundlekey
                        //gDt.Rows[i]["unbundle_from"], //UnBundle Key
                        ////"", //isIPA
                        ////"", //isDisch
                        ////"", //isBaplie
                        ////"", //isPrint
                        //(gDt.Rows[i]["ITEM_CLASS"].ToString().Trim() != "N") ? "C" : "N",
                        gDt.Rows[i]["imp_custom_check"].ToString(),
                        gDt.Rows[i]["exp_custom_check"].ToString(),
                        gDt.Rows[i]["c80_custom_check"].ToString(),
                        gDt.Rows[i]["Owner"].ToString(),
                        gDt.Rows[i]["REMARKS"].ToString()
                    );

                    if (gDt.Rows[i]["hist_flg"].ToString().Trim() == "Y")
                    {
                        HistCnt++;
                        txtHist.Text = HistCnt.ToString();
                        //txtHist.Refresh();
                    }
                    else if (ArrTs.Trim() == "")
                    {
                        PlanCnt = +1;
                        txtPlan.Text = PlanCnt.ToString();
                        //txtPlan.Refresh();
                    }
                    else
                    {
                        InYardCnt++;
                        txtInYard.Text = " " + InYardCnt;
                        //txtInYard.Refresh();

                        if (chkAttach.Checked)
                        {
                            loadAttachList(ref row, gDt.Rows[i]["item_no"].ToString().MyTrim(), ref i);
                        }

                    }

                    row++;

                    if (row%50 == 0)
                    {

                        lblCtrFound.Text = row.ToString();//DGV.Rows.Count.ToString());
                        //lblCtrFound.Refresh();
                        ProgBar.Value = row;
                    }
                    if (row == 105000)
                    {
                        break;
                    }
                    #endregion

                    if (row == 1)
                    {
                        ModuleMisc.DataGridView_Display(DGV, "N", "N", "Y", "Y", "N", true);
                    }
                }
                row--;
                
                lblCtrFound.Text = DGV.Rows.Count.ToString();
                ProgBar.Value = row;
                
                if (optOnrCtr.Checked == true)
                {

                    //Get_Item_Attach(ref row);
                    lblCtrFound.Text = row.ToString();
                    lblCtrFound.Refresh();
                    ProgBar.Value = row;
                    CheckOtherTable(ref row);
                }

                cmdContinue.Enabled = false;
                cmdStop.Enabled = false;
                ProgBar.Value = 0;
                ProgBar.Visible = false;
                lblCtrFound.Text = DGV.Rows.Count.ToString();
                lblCtrFound.Refresh();

                ctrFound = row;
                if (isAttach)
                {
                    lblAttach.Visible = true;
                    txtAttach.Visible = true;
                }

                cmdEnquiry.Enabled = true;

                //ModuleMisc.DataGridView_Display(DGV, "N", "N", "Y", "Y", "N", true);
                //DGV.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                //if (chkAttach.Checked)
                //{
                //    DGV.Columns[3].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                //}
                DGV.Columns[0].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                if (chkAttach.Checked)
                {
                    DGV.Columns[3].AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
                }
                this.Cursor = System.Windows.Forms.Cursors.Default;

            }
            catch (Exception ex)
            {
                this.Cursor = System.Windows.Forms.Cursors.Default;
                cmdEnquiry.Enabled = true;
                ModuleErrMsg.ErrorHandler(ex, "load_ContainerList");
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void loadAttachList(ref int MRow, string ItemNo, ref int idt)
        {
            bool NextItem;

            try
            {
                NextItem = false;
                while (!NextItem && idt < gDt.Rows.Count)
                {
                    if (ItemNo.MyTrim() == gDt.Rows[idt]["ITEM_NO"].ToString().MyTrim())
                    {
                        if (gDt.Rows[idt]["ITEM_ID"].ToString().MyTrim() != "")
                        {
                            MRow++;
                            DGV.Rows.Insert(MRow, gDt.Rows[idt]["ITEM_ID"].ToString().MyTrim(), "", "", chkAttach.Text + "-" + ItemNo, "", "", gDt.Rows[idt]["ia_LINE_OPER"].ToString().MyTrim(), "", System.Convert.ToString(gDt.Rows[idt]["ia_ISO"]).MyTrim(), ModuleMisc.FormatNum(System.Convert.ToString(gDt.Rows[idt]["ia_LENGTH"]), 0), "", gDt.Rows[idt]["ia_ITEM_TYPE"].ToString().MyTrim(), (System.Convert.ToDouble(gDt.Rows[idt]["ia_GROSS"])) * 1000, "", "", "", "", "", "", "", "", "", "", "", "", "", "", gDt.Rows[idt]["ITEM_KEY"].ToString().MyTrim(), "", "Y", "", "", "", "", "", "", "", "");

                            DGV.Rows[MRow].DefaultCellStyle.BackColor = txtAttach.BackColor;
                            SlaveCnt++;
                            txtAttach.Text = " " + SlaveCnt;
                            txtAttach.Refresh();
                            ProgBar.Maximum++;
                        }
                        idt++;
                        if (idt > 44)
                        {
                            idt = idt;
                        }
                    }
                    else
                    {
                        NextItem = true;
                        idt--;
                    }
                }

                isAttach = true;
            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "LoadAttachList");
            }
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private void Get_Item_Attach(ref int row)
        {
            string strSQL;
            DataTable dtAtt;
            int i;
            string ItemNo;

            strSQL = "select s.*, i.item_no from item_subs s,item i  where ITEM_ID LIKE \'" + txtCtrNo.Text.MyTrim() + "%\' ";
            strSQL = strSQL + " and att_item_key = 0 and new_att_key = 0 and s.item_key = i.item_key  order by s.upd_ts";
            dtAtt = ModuleADODB.T_ADODB(strSQL);
            try
            {

                if (dtAtt.Rows.Count == 0)
                {
                    return;
                }
                if (row == 0)
                {
                    load_header();
                }

                for (i = 0; i <= dtAtt.Rows.Count - 1; i++)
                {
                    if (row > 0)
                    {
                        row++;
                    }
                    ItemNo = System.Convert.ToString(dtAtt.Rows[i]["item_no"]);
                    DGV.Rows.Insert(row, dtAtt.Rows[i]["ITEM_ID"], "", "", chkAttach.Text + "-" + ItemNo, "", "", dtAtt.Rows[i]["LINE_OPER"], "", System.Convert.ToString(dtAtt.Rows[i]["ISO"]).MyTrim(), ModuleMisc.FormatNum(System.Convert.ToString(dtAtt.Rows[i]["LENGTH"]), 0), "", dtAtt.Rows[i]["ITEM_TYPE"], ModuleMisc.FormatNum(System.Convert.ToString((System.Convert.ToDouble(dtAtt.Rows[i]["GROSS"])) * 1000), 0), "", "", "", "", "", "", "", "", "", "", "", "", "", "", dtAtt.Rows[i]["ITEM_KEY"], "", "Y", "", "", "", "", "", "", "", "");


                    DGV.Rows[row].DefaultCellStyle.BackColor = txtAttach.BackColor;
                    SlaveCnt++;
                    txtAttach.Text = " " + SlaveCnt;
                    txtAttach.Refresh();
                    ProgBar.Maximum++;
                }
                isAttach = true;
            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "Get_Item_Attach");
            }
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private string CheckStop(int ItemKey)
        {
            string returnValue = "";
            DataRow[] dr;
            try
            {
                dr = ModuleADODB.FilterField(stDt, "item_key", ItemKey.ToString(), "=", "AND");
                if (dr.Length > 0)
                {

                    if (dr[0]["CLR_BY"].ToString().MyTrim() != "")
                    {
                        returnValue = "CLEAR";
                    }
                    else
                    {
                        returnValue = "HELD";
                    }
                }
                else
                {
                    returnValue = "";
                }

            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "CheckStop");
            }
            return returnValue;
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private void CheckOtherTable(ref int row)
        {
            string strSQL;
            DataTable dt;

            strSQL = "select * from edi_item_pre_advice where ITEM_no LIKE \'%" + txtCtrNo.Text.MyTrim() + "%\' ";
            strSQL = strSQL + " and site_id=\'" + mGlobal.gSiteId + "\' and ig_flg <> \'Y\'";
            dt = ModuleADODB.T_ADODB(strSQL);
            LoadMoreData(ref row, dt, "E");
            if (IsStop)
            {
                ProgBar.Visible = false;
                this.Cursor = System.Windows.Forms.Cursors.Default;
                return;
            }
            strSQL = "select * from discharge_list where ITEM_no LIKE \'%" + txtCtrNo.Text.MyTrim() + "%\' ";
            strSQL = strSQL + " and site_id=\'" + mGlobal.gSiteId + "\' and hist_flg <> \'Y\'";
            dt = ModuleADODB.T_ADODB(strSQL);
            LoadMoreData(ref row, dt, "D");
            if (IsStop)
            {
                ProgBar.Visible = false;
                this.Cursor = System.Windows.Forms.Cursors.Default;
                return;
            }
            strSQL = "select * from baplie where ctr_no LIKE \'%" + txtCtrNo.Text.MyTrim() + "%\' ";
            strSQL = strSQL + " and site_id=\'" + mGlobal.gSiteId + "\' " +
                              "  AND HIST_FLG <> 'Y' ";







            dt = ModuleADODB.T_ADODB(strSQL);

            LoadMoreData(ref row, dt, "B");
            if (IsStop)
            {
                ProgBar.Visible = false;
                this.Cursor = System.Windows.Forms.Cursors.Default;
                return;
            }
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private void LoadMoreData(ref int row, DataTable dt, string DataFr)
        {
            string strList;
            string strSQL;
            string conInfo;
            string FE = "";
            int ItemKey = 0;
            int bCnt = 0;
            int eCnt = 0;
            int dCnt = 0;
            int j = 0;
            int i = 0;
            string Height = "";
            string Length = "";
            string DepBy = "";
            string ArrBy = "";
            string gross = "";
            string ArrTs = "";
            string DepTs = "";
            string Lop = "";
            string CtType = "";
            string FPOD = "";
            string POL = "";
            string strLocation = "";
            string POD = "";
            string LLPOD = "";
            string CNC = "";
            string strHistFfg = "";
            string ISO = "";
            string strStop = "";
            string Category = "";
            DataTable rsAtt;
            string frP = "";
            string frD = "";
            string frB = "";
            string ItemNo = "";

            dCnt = 0;

            eCnt = 0;
            bCnt = 0;
            for (i = 0; i <= dt.Rows.Count - 1; i++)
            {
                System.Windows.Forms.Application.DoEvents();
                if (IsStop)
                {
                    return;
                }

                ArrBy = "";
                frP = "";

                frD = "";
                frB = "";
                switch (DataFr)
                {
                    case "E":

                        ItemNo = System.Convert.ToString(dt.Rows[i]["item_no"]);
                        strLocation = "";
                        FE = System.Convert.ToString(dt.Rows[i]["fel"]);
                        Category = System.Convert.ToString(dt.Rows[i]["Category"]);

                        Lop = System.Convert.ToString(dt.Rows[i]["liner_code"]);
                        CNC = ((System.Convert.ToString(dt.Rows[i]["IS_CTR"]).MyTrim() == "N") ? "N" : "C").ToString();
                        ISO = System.Convert.ToString(dt.Rows[i]["iso"]);
                        Length = ModuleMisc.FormatNum(System.Convert.ToString(dt.Rows[i]["length"]), 0);
                        Height = ModuleMisc.FormatNum(System.Convert.ToString(dt.Rows[i]["height"]), 1);
                        CtType = System.Convert.ToString(dt.Rows[i]["ctr_type"]);

                        gross = ModuleMisc.FormatNum(System.Convert.ToString((System.Convert.ToDouble(dt.Rows[i]["gross"])) * 1000), 0);
                        POL = System.Convert.ToString(dt.Rows[i]["pol"]);
                        POD = System.Convert.ToString(dt.Rows[i]["pod"]);
                        FPOD = System.Convert.ToString(dt.Rows[i]["fin_disch_port"]);
                        ItemKey = System.Convert.ToInt32(dt.Rows[i]["item_key"]);
                        LLPOD = "";
                        frP = "Y";
                        break;
                    case "D":

                        ItemNo = System.Convert.ToString(dt.Rows[i]["item_no"]);
                        strLocation = System.Convert.ToString(dt.Rows[i]["stowloc"]);
                        FE = System.Convert.ToString(dt.Rows[i]["fel"]);
                        //Category = "I"
                        Category = System.Convert.ToString(dt.Rows[i]["Category"]);

                        Lop = System.Convert.ToString(dt.Rows[i]["LINE_OPER"]);

                        ArrBy = System.Convert.ToString(dt.Rows[i]["TFC_CODE"]);
                        CNC = ((System.Convert.ToString(dt.Rows[i]["BB_FLG"]).MyTrim() == "Y") ? "N" : "C").ToString();
                        ISO = System.Convert.ToString(dt.Rows[i]["iso"]);
                        Length = ModuleMisc.FormatNum(System.Convert.ToString(dt.Rows[i]["item_size"]), 0);
                        Height = ModuleMisc.FormatNum(System.Convert.ToString(dt.Rows[i]["height"]), 1);
                        CtType = System.Convert.ToString(dt.Rows[i]["item_type"]);

                        gross = ModuleMisc.FormatNum(System.Convert.ToString((System.Convert.ToDouble(dt.Rows[i]["weight"])) * 1000), 0);
                        POL = System.Convert.ToString(dt.Rows[i]["load_port"]);
                        POD = System.Convert.ToString(dt.Rows[i]["disch_port"]);
                        FPOD = System.Convert.ToString(dt.Rows[i]["fdisch_port"]);
                        ItemKey = System.Convert.ToInt32(dt.Rows[i]["item_key"]);
                        LLPOD = System.Convert.ToString(dt.Rows[i]["LL_POD"]);
                        frD = "Y";
                        break;
                    case "B":

                        ItemNo = System.Convert.ToString(dt.Rows[i]["ctr_no"]);
                        strLocation = System.Convert.ToString(dt.Rows[i]["stowloc"]);
                        FE = System.Convert.ToString(dt.Rows[i]["full_empty"]);
                        //Category = "I"

                        Lop = System.Convert.ToString(dt.Rows[i]["shipping_line_code"]);
                        CNC = ((System.Convert.ToString(dt.Rows[i]["BB_FLAG"]).MyTrim() == "Y") ? "N" : "C").ToString();
                        ISO = System.Convert.ToString(dt.Rows[i]["iso_code"]);
                        ModuleMisc.getISOvalues(ISO, ref Length, ref Height, ref CtType);

                        gross = ModuleMisc.FormatNum((dt.Rows[i]["weight_gross"].CheckDouble() * 1000).MyTrim(), 0);
                        POL = System.Convert.ToString(dt.Rows[i]["pol_code"]);
                        POD = System.Convert.ToString(dt.Rows[i]["pod_code"]);
                        FPOD = System.Convert.ToString(dt.Rows[i]["fpd_code"]);
                        //if (POD.MyTrim() != mGlobal.gHomePort.MyTrim())
                        //{
                        //    Category = "S";
                        //}



                        //else if (POD.MyTrim() == mGlobal.gHomePort.MyTrim() && FPOD.MyTrim() != "" && FPOD.MyTrim() != mGlobal.gHomePort.MyTrim())
                        //{
                        //    Category = "T";
                        //}



                        //else if (POD.MyTrim() == mGlobal.gHomePort.MyTrim() && (FPOD.MyTrim() == "" || FPOD.MyTrim() == mGlobal.gHomePort.MyTrim()))
                        //{
                        //    Category = "I";
                        //}
                        Category = dt.Rows[i]["CATEGORY"].MyTrim();



                        ItemKey = System.Convert.ToInt32(dt.Rows[i]["item_key"]);

                        DataRow dr = ModuleVessel.GetVesDetailsFromVesID(dt.Rows[i]["VES_ID"].ToString());
                        if (dr != null)
                            ArrBy = dr["TFC_CODE_I"].MyTrim();
                        LLPOD = "";
                        frB = "Y";
                        break;
                }



                if (DGV.Rows.Count != 0)
                {
                    row++;
                }

                DGV.Rows.Insert(row, ItemNo.MyTrim(), System.Convert.ToString(dt.Rows[i]["site_id"]).MyTrim(), "",
                    strLocation.MyTrim(), FE.MyTrim(),
                    Category, Lop.MyTrim(), "",
                    ISO, Length.MyTrim(),
                    Height.MyTrim(), CtType,
                    gross.MyTrim(), dt.Rows[i]["RELEASE_NO"].MyTrim(),
                    "", ArrBy.MyTrim(), "", "", "", "", POL, POD,
                    LLPOD, FPOD, "", "", "",
                    ItemKey.ToString().MyTrim(), "", "", "", "", "", "",
                    frP, frD, frB, CNC);

                switch (DataFr)
                {
                    case "E":
                        DGV.Rows[row].DefaultCellStyle.BackColor = txtPreadvice.BackColor;
                        eCnt++;
                        txtPreadvice.Text = " " + eCnt;
                        txtPreadvice.Refresh();
                        break;
                    case "D":
                        DGV.Rows[row].DefaultCellStyle.BackColor = txtDischarge.BackColor;
                        dCnt++;
                        txtDischarge.Text = " " + dCnt;
                        txtDischarge.Refresh();
                        break;
                    case "B":
                        DGV.Rows[row].DefaultCellStyle.BackColor = txtBaplie.BackColor;
                        bCnt++;
                        txtBaplie.Text = " " + bCnt;
                        txtBaplie.Refresh();
                        break;
                }


                lblCtrFound.Text = DGV.Rows.Count.ToString();
                lblCtrFound.Refresh();


            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void frmCtrEnquiry_FormClosed(System.Object eventSender, System.Windows.Forms.FormClosedEventArgs eventArgs)
        {
            if (IsStop == false)
            {
                IsStop = true;
                cmdContinue.Enabled = true;
                cmdStop.Enabled = false;
                cmdEnquiry.Enabled = true;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void optAllCtrs_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optAllCtrs.Checked)
            {

                txtReleaseNo.Text = "";
                txtCtrNo.Text = "";
                txtSealNo.Text = "";
                txtBlock.Enabled = true;
                cboLOP.Enabled = true;
                cboCtrType.Enabled = true;
                cboCtrISO.Enabled = true;
                cboItemClass.Enabled = true;
                cboCtrNonCtr.Enabled = true;
                cboCtrNonCtr.Text = "C";
                chkFull.Enabled = true;
                chkMty.Enabled = true;
                chkCPA.Enabled = true;
                chkBlankSeal.Enabled = true;
                chkAttach.Enabled = true;
                txtSealNo.Enabled = false;
                txtCtrNo.Enabled = false;
                txtReleaseNo.Enabled = false;
                lblAttach.Visible = false;
                txtAttach.Visible = false;
                Label4.Visible = false;

                txtPreadvice.Visible = false;
                Label5.Visible = false;

                txtDischarge.Visible = false;
                Label8.Visible = false;

                txtBaplie.Visible = false;
                Label10.Visible = false;

                txtPlan.Visible = false;

                MoveLabel("D");
                txtCtrNo.BackColor = System.Drawing.SystemColors.Control;
                txtSealNo.BackColor = System.Drawing.SystemColors.Control;
                txtReleaseNo.BackColor = System.Drawing.SystemColors.Control;
                txtBlock.BackColor = System.Drawing.SystemColors.Window;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void optAllSites_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optAllSites.Checked)
            {
                txtBlock.Enabled = false;
                txtBlock.BackColor = System.Drawing.ColorTranslator.FromOle(0xE0E0E0);
                cboSiteId.Enabled = false;
                cboSiteId.Text = "";
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        private void optLocalSite_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optLocalSite.Checked)
            {
                txtBlock.Enabled = true;
                txtBlock.BackColor = System.Drawing.Color.White;
                cboSiteId.Enabled = false;
                cboSiteId.Text = "";

            }
        }


        //------------------------------------------------------------------------------------------------------------------------------
        public void optOnrCtr_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optOnrCtr.Checked)
            {

                txtReleaseNo.Text = "";
                txtBlock.Text = "";
                cboLOP.Text = "";
                txtSealNo.Text = "";
                chkMty.CheckState = CheckState.Unchecked;
                chkFull.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkCPA.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkBlankSeal.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkAttach.CheckState = System.Windows.Forms.CheckState.Unchecked;
                cboLOP.Text = "";
                cboCtrType.Text = "";
                cboCtrISO.Text = "";
                cboItemClass.Text = "";
                cboCtrNonCtr.Text = "";
                txtBlock.Enabled = false;
                cboLOP.Enabled = false;
                cboCtrType.Enabled = false;
                cboCtrISO.Enabled = false;
                cboItemClass.Enabled = false;
                cboCtrNonCtr.Enabled = false;
                chkFull.Enabled = false;
                chkMty.Enabled = false;
                chkCPA.Enabled = false;
                chkBlankSeal.Enabled = false;
                chkAttach.Enabled = false;
                txtSealNo.Enabled = false;
                txtCtrNo.Enabled = true;
                txtCtrNo.ReadOnly = false;
                txtReleaseNo.Enabled = false;
                lblAttach.Visible = false;
                txtAttach.Visible = false;

                lblHist.Visible = true;

                txtHist.Visible = true;
                lblInYard.Visible = true;

                txtInYard.Visible = true;
                Label4.Visible = true;

                txtPreadvice.Visible = true;
                Label5.Visible = true;

                txtDischarge.Visible = true;
                Label8.Visible = true;

                txtBaplie.Visible = true;
                Label10.Visible = true;

                txtPlan.Visible = true;
                MoveLabel("U");
                txtBlock.BackColor = System.Drawing.SystemColors.Control;
                txtSealNo.BackColor = System.Drawing.SystemColors.Control;
                txtReleaseNo.BackColor = System.Drawing.SystemColors.Control;
                txtCtrNo.BackColor = System.Drawing.SystemColors.Window;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void optOnSealNo_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optOnSealNo.Checked)
            {
                txtReleaseNo.Text = "";
                txtBlock.Text = "";
                cboLOP.Text = "";
                chkMty.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkFull.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkCPA.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkBlankSeal.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkAttach.CheckState = System.Windows.Forms.CheckState.Unchecked;
                cboLOP.Text = "";
                cboCtrType.Text = "";
                cboCtrISO.Text = "";
                cboItemClass.Text = "";
                cboCtrNonCtr.Text = "";
                txtCtrNo.Text = "";
                ctrFound = 0;
                txtBlock.Enabled = false;
                cboLOP.Enabled = false;
                cboCtrType.Enabled = false;
                cboCtrISO.Enabled = false;
                cboItemClass.Enabled = false;
                cboCtrNonCtr.Enabled = false;
                chkFull.Enabled = false;
                chkMty.Enabled = false;
                chkCPA.Enabled = false;
                chkBlankSeal.Enabled = false;
                chkAttach.Enabled = false;
                txtSealNo.Enabled = true;
                txtSealNo.ReadOnly = false;
                txtCtrNo.Enabled = false;
                txtReleaseNo.Enabled = false;
                lblAttach.Visible = false;
                txtAttach.Visible = false;

                lblHist.Visible = true;

                txtHist.Visible = true;
                lblInYard.Visible = true;

                txtInYard.Visible = true;
                Label4.Visible = false;

                txtPreadvice.Visible = false;
                Label5.Visible = false;

                txtDischarge.Visible = false;
                Label8.Visible = false;

                txtBaplie.Visible = false;
                Label10.Visible = false;

                txtPlan.Visible = false;

                MoveLabel("U");
                txtBlock.BackColor = System.Drawing.SystemColors.Control;
                txtCtrNo.BackColor = System.Drawing.SystemColors.Control;
                txtReleaseNo.BackColor = System.Drawing.SystemColors.Control;
                txtSealNo.BackColor = System.Drawing.SystemColors.Window;
            }
        }


        //------------------------------------------------------------------------------------------------------------------------------
        private void optOtherSites_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optOtherSites.Checked)
            {
                cboSiteId.Enabled = true;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void optReleaseNo_CheckedChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (optReleaseNo.Checked)
            {
                txtReleaseNo.Text = "";
                txtBlock.Text = "";
                cboLOP.Text = "";
                chkMty.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkFull.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkCPA.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkBlankSeal.CheckState = System.Windows.Forms.CheckState.Unchecked;
                chkAttach.CheckState = System.Windows.Forms.CheckState.Unchecked;
                cboLOP.Text = "";
                cboCtrType.Text = "";
                cboCtrISO.Text = "";
                cboItemClass.Text = "";
                cboCtrNonCtr.Text = "";
                txtCtrNo.Text = "";
                txtSealNo.Text = "";
                lblAttach.Visible = false;
                txtAttach.Visible = false;

                ctrFound = 0;
                txtBlock.Enabled = false;
                cboLOP.Enabled = false;
                cboCtrType.Enabled = false;
                cboCtrISO.Enabled = false;
                cboItemClass.Enabled = false;
                cboCtrNonCtr.Enabled = false;
                chkFull.Enabled = false;
                chkMty.Enabled = false;
                chkCPA.Enabled = false;
                chkBlankSeal.Enabled = false;
                chkAttach.Enabled = false;
                txtSealNo.Enabled = false;
                txtCtrNo.Enabled = false;
                txtReleaseNo.Enabled = true;
                txtReleaseNo.ReadOnly = false;
                lblHist.Visible = true;

                txtHist.Visible = true;
                lblInYard.Visible = true;

                txtInYard.Visible = true;
                Label4.Visible = false;

                txtPreadvice.Visible = false;
                Label5.Visible = false;

                txtDischarge.Visible = false;
                Label8.Visible = false;

                txtBaplie.Visible = false;
                Label10.Visible = false;

                txtPlan.Visible = false;

                MoveLabel("U");

                txtBlock.BackColor = System.Drawing.SystemColors.Control;
                txtCtrNo.BackColor = System.Drawing.SystemColors.Control;
                txtSealNo.BackColor = System.Drawing.SystemColors.Control;
                txtReleaseNo.BackColor = System.Drawing.SystemColors.Window;

            }
        }


        //------------------------------------------------------------------------------------------------------------------------------
        public void txtBlock_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.txtUpper(txtBlock);
            optAllCtrs.Checked = true;
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtBlock_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(txtBlock.Text.MyTrim()))
            {
                txtBlock.Text = ModuleMisc.delSingleQuote(txtBlock.Text.MyTrim());
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtCtrNo_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.txtUpper(txtCtrNo);

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtCtrNo_KeyPress(System.Object eventSender, System.Windows.Forms.KeyPressEventArgs eventArgs)
        {
            int KeyAscii = Strings.Asc(eventArgs.KeyChar);
            string strCtrNo;

            if (KeyAscii == (int)System.Windows.Forms.Keys.Return)
            {
                IsStop = false;
            }

            eventArgs.KeyChar = Strings.Chr(KeyAscii);
            if (KeyAscii == 0)
            {
                eventArgs.Handled = true;
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtCtrNo_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(txtCtrNo.Text.MyTrim()))
            {
                txtCtrNo.Text = ModuleMisc.delSingleQuote(txtCtrNo.Text.MyTrim());
            }


        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtSealNo_TextChanged(System.Object eventSender, System.EventArgs eventArgs)
        {
            ModuleMisc.txtUpper(txtSealNo);
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void cmdPrintSelCTNR_Click(System.Object eventSender, System.EventArgs eventArgs)
        {
            int i;
            int j;
            string strSQL;
            string stritem;
            string ReportName;
            frmRpt Rpt = new frmRpt();

            try
            {

                this.Cursor = System.Windows.Forms.Cursors.WaitCursor;

                InsertContainerEnquiryList();

                if (pCnt == 0)
                {
                    return;
                }

                ReportName = Application.StartupPath + "\\report\\C03.rpt";

                Rpt.ReportName = ReportName;


                Rpt.ParamName = new string[] { "pIP_key" };


                Rpt.ParamValue = new string[] { mGlobal.g_ipAddress };
                Rpt.ShowDialog(this);

                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "cmdPrintSelCTNR_Click");
                this.Cursor = System.Windows.Forms.Cursors.Default;
            }
        }
        //------------------------------------------------------------------------------------------------------------------------------
        private void InsertContainerEnquiryList()
        {

            string g_IP_KEY;
            string g_ITEM_KEY;
            string g_ITEM_NO;
            string g_LOCATION;
            string g_X;
            string g_Y;
            string g_Z;
            string g_ISO;
            string g_CATEGORY;
            string g_F_E;
            string g_ITEM_STATUS;
            string g_LINE_OPER;
            string g_OPER_METHOD;
            string g_OWNER;
            string g_SEAL_NO_CURRENT;
            string g_ARR_BY = "";
            string g_ARR_CAR;
            string g_ARR_TS;
            string g_DEP_CAR;
            string g_DEP_TS;
            string g_DATA_SOURCE;
            string g_UPD_TS;
            string[] SQL = new string[6];
            int i;


            string strSQLDelete;
            try
            {

                strSQLDelete = "DELETE FROM CONTAINER_ENQUIRY_LIST_TEMP NOLOGGING " + "WHERE ip_key =  \'"
                    + mGlobal.g_ipAddress.MyTrim() + "\' ";

                mGlobal.G_MSTClass.BeginMST();
                ModuleADODB.ExecuteSQL(strSQLDelete, ref mGlobal.MINUS_ONE, -1);
                pCnt = 0;

                for (i = 0; i <= DGV.Rows.Count - 1; i++)
                {
                    if (chkAll.CheckState == CheckState.Checked)
                    {

                        SQL[0] = "";
                        SQL[1] = "";
                        SQL[2] = "";
                        SQL[3] = "";
                        SQL[4] = "";


                        g_IP_KEY = mGlobal.g_ipAddress.MyTrim();
                        g_ITEM_NO = DGV.Rows[i].Cells[ItemNoCol].Value.ToString().MyTrim();
                        g_ITEM_KEY = DGV.Rows[i].Cells[ItemKeyCol].Value.ToString().MyTrim();
                        g_LOCATION = DGV.Rows[i].Cells[LocCOL].Value.ToString().MyTrim();
                        g_ISO = DGV.Rows[i].Cells[ISOCol].Value.ToString().MyTrim();
                        g_F_E = DGV.Rows[i].Cells[FECol].Value.ToString().MyTrim();
                        g_CATEGORY = DGV.Rows[i].Cells[CatCol].Value.ToString().MyTrim();
                        g_LINE_OPER = DGV.Rows[i].Cells[LOPCol].Value.ToString().MyTrim();
                        g_OPER_METHOD = DGV.Rows[i].Cells[OperationMethodCol].Value.ToString().MyTrim();
                        g_ARR_CAR = DGV.Rows[i].Cells[ArrByCol].Value.ToString().MyTrim();
                        g_ARR_TS = DGV.Rows[i].Cells[ArrTsCol].Value.ToString().MyTrim();
                        g_DEP_CAR = DGV.Rows[i].Cells[DepByCol].Value.ToString().MyTrim();
                        g_DEP_TS = DGV.Rows[i].Cells[DepTsCol].Value.ToString().MyTrim();

                        g_DATA_SOURCE = " ";
                       
                        SQL[1] = "INSERT into CONTAINER_ENQUIRY_LIST_TEMP NOLOGGING (" + "IP_KEY," + "ITEM_KEY," + "ITEM_NO," + "LOCATION," + "ISO," + "CATEGORY," + "F_E," + "LINE_OPER," + "ARR_BY," + "ARR_CAR ,";

                        SQL[2] = "ARR_TS," + "DEP_CAR," + "DEP_TS," + "DATA_SOURCE," + "UPD_TS )";

                        SQL[3] = " VALUES (";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_IP_KEY) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_Clng(g_ITEM_KEY) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_ITEM_NO) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_LOCATION) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_ISO) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_CATEGORY) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_F_E) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_LINE_OPER) + "\', ";
                        SQL[3] = SQL[3] + " \'" + ModuleMisc.check_blank(g_ARR_BY) + "\', ";

                        SQL[4] = SQL[4] + " \'" + ModuleMisc.check_blank(g_ARR_CAR) + "\', ";
                        SQL[4] = SQL[4] + " TO_DATE(\'" + ModuleDateTime.check_date(g_ARR_TS) + "\'," + mGlobal.DBStrDMYH24MS + "), ";
                        SQL[4] = SQL[4] + " \'" + ModuleMisc.check_blank(g_DEP_CAR) + "\', ";
                        SQL[4] = SQL[4] + " TO_DATE(\'" + ModuleDateTime.check_date(g_DEP_TS) + "\'," + mGlobal.DBStrDMYH24MS + "), ";
                        SQL[4] = SQL[4] + " \'" + ModuleMisc.check_blank(g_DATA_SOURCE) + "\', ";
                        SQL[4] = SQL[4] + ModuleDateTime.Now_Ts();
                        SQL[4] = SQL[4] + ") ";


                        SQL[0] = SQL[1] + SQL[2] + SQL[3] + SQL[4];

                        ModuleADODB.ExecuteSQL(SQL[0], ref mGlobal.MINUS_ONE, -1);
                        pCnt++;
                    }

                }

                mGlobal.G_MSTClass.EndMST();

            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "InsertContainerEnquiryList");
            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void txtSealNo_Leave(System.Object eventSender, System.EventArgs eventArgs)
        {
            if (ModuleMisc.checkSingleQuote(txtSealNo.Text.MyTrim()))
            {
                txtSealNo.Text = ModuleMisc.delSingleQuote(txtSealNo.Text.MyTrim());
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void DGV_CellClick(object sender, System.Windows.Forms.DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {

            }
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void DGV_CellDoubleClick(object sender, System.Windows.Forms.DataGridViewCellEventArgs e)
        {
            string strSQL;
            DataTable dt;
            this.Cursor = System.Windows.Forms.Cursors.WaitCursor;
            string sEmpty = "";
            int iZero = 0;

            try
            {
                if (e.RowIndex >= 0)
                {
                    if (DGV.Rows[e.RowIndex].Cells[HistFlgCol].Value.ToString().MyTrim() != "Y")
                    {
                        frmC01_ContainerMaint frm = new frmC01_ContainerMaint();

                        frm.EnquiryOnly = EnquiryOnly;

                        frm.txtItemNo.Text = System.Convert.ToString(DGV.Rows[e.RowIndex].Cells[ItemNoCol].Value);
                        frm.LoadItem(" ", System.Convert.ToInt32(DGV.Rows[e.RowIndex].Cells[ItemKeyCol].Value), false);
                        frm.ShowDialog(this);
                        frm.Dispose();

                    }
                    else if (DGV.Rows[e.RowIndex].Cells[HistFlgCol].Value.ToString().MyTrim() == "Y")
                    {
                        frmC01_ContainerMaint frm = new frmC01_ContainerMaint();
                        frm.EnquiryOnly = EnquiryOnly;
                        frm.txtItemNo.Text = System.Convert.ToString(DGV.Rows[e.RowIndex].Cells[ItemNoCol].Value);
                        frm.LoadItem(" ", System.Convert.ToInt32(DGV.Rows[e.RowIndex].Cells[ItemKeyCol].Value), null);
                        frm.ShowDialog(this);
                        frm.Dispose();
                    }
                }

            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "DGV_CellDoubleClick");
            }
            this.Cursor = System.Windows.Forms.Cursors.Default;
        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void DGV_CellFormatting(object sender, System.Windows.Forms.DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                if (e.RowIndex >= 0)
                {
                    if (e.ColumnIndex > 0)
                    {
                        if (Strings.Trim(DGV.Rows[e.RowIndex].Cells[HistFlgCol].Value.ToString()) == "Y")
                        {
                            e.CellStyle.BackColor = txtHist.BackColor;
                        }
                        //else if (Strings.Trim(DGV.Rows[e.RowIndex].Cells[HistFlgCol].Value.ToString()) == "P")
                        //{
                        //    e.CellStyle.BackColor = txtPlan.BackColor;
                        //}
                        //else if (Strings.Trim(DGV.Rows[e.RowIndex].Cells[IsIPACol].Value.ToString()) == "Y")
                        //{
                        //    e.CellStyle.BackColor = txtPreadvice.BackColor;
                        //}
                        //else if (Strings.Trim(DGV.Rows[e.RowIndex].Cells[IsDisCol].Value.ToString()) == "Y")
                        //{
                        //    e.CellStyle.BackColor = txtDischarge.BackColor;
                        //}
                        //else if (Strings.Trim(DGV.Rows[e.RowIndex].Cells[IsBapCol].Value.ToString()) == "Y")
                        //{
                        //    e.CellStyle.BackColor = txtBaplie.BackColor;
                        //}
                        else
                        {
                            e.CellStyle.BackColor = txtInYard.BackColor;

                        }
                    }
                    else
                    {
                        e.CellStyle.BackColor = this.BackColor;
                    }

                }
            }
            catch (Exception ex)
            {
                ModuleErrMsg.ErrorHandler(ex, "DGV_CellFormatting");
            }

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void lblDGVHeading_Click(System.Object sender, System.EventArgs e)
        {

        }

        //------------------------------------------------------------------------------------------------------------------------------
        public void Frame2_Enter(System.Object sender, System.EventArgs e)
        {

        }



        //------------------------------------------------------------------------------------------------------------------------------
        public void frmC03_CtrEnquiry_FormClosing(System.Object sender, System.Windows.Forms.FormClosingEventArgs e)
        {

            if (cmdEnquiry.Enabled == false)
            {
                e.Cancel = true;
            }

        }
        //------------------------------------------------------------------------------------------------------------------------------

        public bool EnquiryOnly { get; set; }

        private void optOnListCont_CheckedChanged(object sender, EventArgs e)
        {
            if (optOnListCont.Checked)
            {
                txtListCont.Enabled = true;
                txtDayPeriod.Enabled = true;
            }
            else
            {
                txtListCont.Enabled = false;
                txtDayPeriod.Enabled = false;
            }
        }

        private void btnSearchVgm_Click(object sender, EventArgs e)
        {
            if (txtListCont.Text.IsEmpty())
            {
                MessageBox.Show("Vui lòng nhập danh sách số container vào ô bên cạnh");
                return;
            }

            if (optOnListCont.Checked)
            {
                //var conts = txtListCont.Text.Trim();

                string[] conts = txtListCont.Text.Split(',');

                if (conts.Length == 1)
                {
                    conts = txtListCont.Text.Split(';');
                }

                if (conts.Length == 1)
                {
                    conts = txtListCont.Text.Split('\n');
                }

                var msgVgm = string.Format("ItemNo\tCustomerVgm\tLinerOperVgm\tLoadListVgm\tMaxGross\r\n") ;
                for (int j = 0; j < conts.Length; j++)
                {
                    if (j > 200)
                    {
                        MessageBox.Show("Chỉ cho phép tra Vgm tối đa 200 cont");
                        break;
                    }

                    if (!conts[j].IsEmpty())
                    {
                        var itemNo = conts[j].Trim();

                        itemNo = itemNo.Replace(" ", "");
                        itemNo = itemNo.Replace("\r", "");
                        itemNo = itemNo.Replace("\t", "");

                        //Tra Vgm
                        var vgms = ItemBundle.ContainerBe.GetItemVgm(itemNo).FindAll(c=>c.DummyFlg != BooleanType.Yes && c.HistFlg != BooleanType.Yes);

                        var custVgm = vgms.Find(c => c.ReceiveFrom == VgmType.CUST);
                        var lopVgm = vgms.Find(c => c.ReceiveFrom == VgmType.LOP);
                        var lopLlVgm = vgms.Find(c => c.ReceiveFrom == VgmType.LOP && c.LoadListFlg == BooleanType.Yes);

                        var maxGross = 0.0;

                        if (custVgm != null && custVgm.MaxGrossWt > 0)
                        {
                            maxGross = custVgm.MaxGrossWt;
                        }
                        else if (lopVgm != null && lopVgm.MaxGrossWt > 0)
                        {
                            maxGross = lopVgm.MaxGrossWt;
                        }
                        else if (lopLlVgm != null && lopLlVgm.MaxGrossWt > 0)
                        {
                            maxGross = lopLlVgm.MaxGrossWt;
                        }

                        msgVgm += string.Format("{0}\t{1}\t{2}\t{3}\t{4}\r\n", 
                            itemNo, 
                            custVgm!=null?custVgm.CertifiedWeight:0,
                            lopVgm != null ? lopVgm.CertifiedWeight : 0,
                            lopLlVgm != null ? lopLlVgm.CertifiedWeight : 0,
                            maxGross);
                    }

                    
                }

                var cd = new ConfirmDialog(msgVgm, false);
                cd.ShowDialog();
            } 
        }

        private void btnReport_Click(object sender, EventArgs e)
        {
            var dateTime = new ChooseDateTimeRange();

            dateTime.FromDate = DateTime.Now.AddDays(-1);
            dateTime.ToDate = DateTime.Now;

            dateTime.ShowDialog();

            if (dateTime.DialogResult != DialogResult.OK)
            {
                return;
            }

            var vgms = ItemBundle.ContainerBe.GetVgmReportByTruckIn(dateTime.FromDate, dateTime.ToDate);

            var cd = new Browser(vgms.ToList<Object>());

            cd.ShowDialog();
        }
    }
}
