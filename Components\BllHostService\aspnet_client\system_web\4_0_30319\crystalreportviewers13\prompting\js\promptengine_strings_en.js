/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Today";
var L_January   = "January";
var L_February  = "February";
var L_March     = "March";
var L_April     = "April";
var L_May       = "May";
var L_June      = "June";
var L_July      = "July";
var L_August    = "August";
var L_September = "September";
var L_October   = "October";
var L_November  = "November";
var L_December  = "December";
var L_Su        = "Su";
var L_Mo        = "Mo";
var L_Tu        = "Tu";
var L_We        = "We";
var L_Th        = "Th";
var L_Fr        = "Fr";
var L_Sa        = "Sa";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "yyyy";
var L_MM            = "mm";
var L_DD            = "dd";
var L_BadNumber     = "This parameter is of type \"Number\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadCurrency   = "This parameter is of type \"Currency\" and can only contain a negative sign symbol, digits (\"0-9\"), digit grouping symbols or a decimal symbol. Please correct the entered parameter value.";
var L_BadDate       = "This parameter is of type \"Date\" and should be in the format \"%1\" where \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), and \"dd\" is the day of the month.";
var L_BadDateTime   = "This parameter is of type \"DateTime\" and the correct format is \"%1 hh:mm:ss\". \"yyyy\" is the four digit year, \"mm\" is the month (e.g. January = 1), \"dd\" is the day of the month, \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_BadTime       = "This parameter is of type \"Time\" and should be in the format \"hh:mm:ss\" where \"hh\" is hours in a 24 hour clock, \"mm\" is minutes, and \"ss\" is seconds.";
var L_NoValue       = "No Value";
var L_BadValue      = "To set \"No Value\", you must set both From and To values to \"No Value\".";
var L_BadBound      = "You cannot set \"No Lower Bound\" together with \"No Upper Bound\".";
var L_NoValueAlready = "This parameter is already set to \"No Value\". Remove \"No Value\" before adding other values.";
var L_RangeError    = "The start of range cannot be greater than the end of range.";
var L_NoDateEntered = "You must enter a date.";
var L_Empty         = "Please enter a value.";

// Strings for filter dialog
var L_closeDialog="Close window";

var L_SetFilter = "Set Filter";
var L_OK        = "OK";
var L_Cancel    = "Cancel";

 /* Crystal Decisions Confidential Proprietary Information */
