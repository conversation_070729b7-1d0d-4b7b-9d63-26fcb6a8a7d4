<!--
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: waitDialog.html
=============================================================
-->

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>Waiting dialog window</title>
			
		<script language="javascript" src="../dom.js"></script>
		<script language="javascript" src="../dialog.js"></script>
		<script language="javascript" src="../palette.js"></script>
		<script language="javascript" src="../waitdialog.js"></script>
		
		<script language="javascript">
			var skin=parent._skin?parent._skin:"skin_standard";
			var lang=parent._lang?parent._lang:"en";
			
			initDom("../images/"+skin+"/",lang)
			styleSheet()
		</script>
		
		<script language="javascript">
						
			// 
			// GLOBALS
			//		
			
			// Widgets
			var waitDlg = null;
						
			// Used in resizeCB			
			var initialized	= false;
		</script>					
		

		<script language="javascript">
			
			function clickCB()
			{
				var id=this.id
				switch(id)
				{
					case "button1":
						waitDlg1.show(true)
						waitDlg1.startProgress()
						break;
					case "button2":
						waitDlg2.show(true)
						break;
					case "button3":
						waitDlg3.show(true)
						break;
					case "button4":
						waitDlg4.show(true)
						break;
				}
			}
																		        
			// function cancelCB
			// called when click on the CANCEL button
			function cancelCB()
			{	
				waitDlg1.show(false);
				waitDlg2.show(false)
				waitDlg3.show(false);
				waitDlg4.show(false)			
			}
						
			// Load widgets
			function loadCB()
			{	
				if (!initialized)
				{	
					initialized=true;
					
					// --------------------------------------------------------
					// Create widgets
					// --------------------------------------------------------
					button1=newButtonWidget("button1","Wait dialog with Undeterminate bar",clickCB)
					button2=newButtonWidget("button2","Wait dialog with Determinate bar",clickCB)					
					button3=newButtonWidget("button3","No progress bar - No label - No cancel",clickCB)
					button4=newButtonWidget("button4","No progress bar - No label - Cancel",clickCB)
						
					waitDlg1 = newWaitDialogBoxWidget("waitDlg1",250,170,"Refresh",true,cancelCB,true,0,true,"Explication \n Suite sur autre ligne");
					waitDlg2 = newWaitDialogBoxWidget("waitDlg2",250,170,"Refresh",true,cancelCB,false,null,true,"Explication \n Suite sur autre ligne",true);					
					waitDlg3 = newWaitDialogBoxWidget("waitDlg3",250,150,"Refresh",false,null,false,null,false,null);
					waitDlg4 = newWaitDialogBoxWidget("waitDlg4",250,150,"Refresh",true,cancelCB,false,null,false,null);
				}
				
				if ( !_curWin.basicDlgInitialized )
				{
					_curWin.basicDlgInitialized = true;
					
					// --------------------------------------------------------
					// Write the widgets
					// --------------------------------------------------------			

					// Build the dialog box
					targetApp(
						'<table cellspacing="0" cellpadding="5" width="50%" border="1" class="dialogzone"><tbody>'+
						'<tr>'+
							'<td align="center">'+button1.getHTML()+'</td>'+
							'<td align="center">'+button2.getHTML()+'</td>'+
						'<tr>'+
						'<tr>'+
							'<td align="center">'+button3.getHTML()+'</td>'+
							'<td align="center">'+button4.getHTML()+'</td>'+
						'<tr>'+
						'</tbody></table>'+
						waitDlg1.getHTML()+
						waitDlg2.getHTML()+
						waitDlg3.getHTML()+
						waitDlg4.getHTML()
					);					
				}
				
				// --------------------------------------------------------
				// Init widgets
				// --------------------------------------------------------				
				waitDlg1.init();
				waitDlg2.init();				
				waitDlg3.init();
				waitDlg4.init();
			}			
					
			// 
			// END OF FUNCTIONS
			//		

		</script>

	</head>

	<body class="dialogzone" onload="setTimeout('loadCB()',1)">	
	</body>
	
</html>