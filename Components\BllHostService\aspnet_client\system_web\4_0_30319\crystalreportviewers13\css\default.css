.crpage
{
    background-color:#FFFFFF;
    color:#000000;
    font-family:verdana,<PERSON>l;
}

.crheader
{
    background-color: #E4E4EC;
    color: #000000;
    font-family:verdana,Arial;
    border-bottom:1px solid #BEBED1;
}

.crheader A
{
    color:#000000;
    cursor:hand;
    text-decoration:none;
}

.crheader A:hover
{
    color:#FF0000;
    cursor:hand;
    text-decoration:none;
}

.crtitle
{
    color:#52526F;
    font-size:11pt;
    font-family:Arial;
}

.crpagepath
{
    background-color:#006699;
    color:#ffffff;
    padding-left:10;
}

.crpagepath A
{
    color:#FFFFFF;
    cursor:hand;
    text-decoration:none;
}

.crpagepath A:hover
{
    color:#FF0000;
    cursor:hand;
    text-decoration:none;
}

.crwizard
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:verdana,Arial;
    border-top:1px solid #FFFFFF;
}

.crwizardtitle
{
    color:#52526F;
    font-size:11pt;
    font-family:Arial;
}

.crwizardtabbackground
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:Arial;
    font-size:10pt;
}

.crwizardtabbackground A
{
    text-decoration:none;
    color:#000000;
}

.crwizardtabborder
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:Arial;
    font-size:11pt;
    border-left:1px solid #FFFFFF;
    border-bottom:1px solid #FFFFFF;
    border-right:1px solid #FFFFFF;
    padding-bottom:7px;
    padding-top:10px;
}

.crwizardtabborder2
{
    border-bottom:1px solid #FFFFFF;
}

.crwizardtoolbar
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:Arial;
    font-size:11pt;
}

.crwizardtoolbar A
{
    color:#000000;
    cursor:hand;
    text-decoration:none;
}

.crwizardtoolbar A.hover
{
    color:#FF0000;
    cursor:hand;
    text-decoration:none;
}

.crwizardinstructions
{
    color:#58587b;
    font-family: Arial;
    font-size:11pt;
}

.crwizardformula
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:Arial;
    font-size:11pt;
}

.crwizardformulatextbox
{
    font-family:Arial;
    font-size:11pt;
}
.crwizardformulabutton
{
    font-family:Arial;
    font-size:9pt;
    font-weight:bold;
}

.crwizardfields
{
    background-color:#CAC9D9;
    color:#000000;
    font-family:Arial;
    font-size:11pt;
}

.crwizardfieldsselect
{
    font-family:Arial;
    font-size:9pt;
}

.crsearchresults
{
    background-color:#FFFFFF;
    color:#636384;
    font-family:Arial;
    font-size:10pt;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.crsearchresults A
{
    color:#636384;
    font-family:Arial;
    font-size:10pt;
}

.crsearchresults A:hover
{
    color:#636384;
    font-family:Arial;
    font-size:10pt;
}

.crsearchresultsalt
{
    background-color:#ECECF2;
    color:#636384;
    font-family:Arial;
    font-size:10pt;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.crsearchresultsalt A
{
    color:#636384;
    font-family:Arial;
    font-size:10pt;
}

.crsearchresultsalt A:hover
{
    color:#636384;
    font-family:Arial;
    font-size:10pt;
}

.crsearchresultstitle
{
    background-color:#DFDEEA;
    color:#000000;
    font-size:10pt;
    font-family:Arial;
    font-weight:normal;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.crsearchresultstoolbar
{
    background-color:#FFFFFF;
    color:#000000;
}

.crsearchresultstoolbar A
{
    color:#0000FF;
    cursor:hand;
    text-decoration:none;
}

.crsearchresultstoolbar A:hover
{
    color:#FF00FF;
    cursor:hand;
    text-decoration:none;
}

.crsearchresultsbutton
{
    font-size:9pt;
    font-family:Arial;
    font-weight:bold;
}

.crcaption
{
    color:#000000;
    font-size:14pt;
    font-family:verdana,Arial;
}


.crtoolbar
{
    background-color:#E4E4EC;
    color:#000000;
    font-size:8pt;
    font-family:verdana,Arial;
    border-bottom:1px solid #BEBED1;
    border-top:1px solid #FFFFFF;
}

.crtoolbar A
{
    color:#000000;
    cursor:hand;
    text-decoration:none;
}

.crtoolbar A:hover
{
    color:#FF0000;
    cursor:hand;
    text-decoration:none;
}

.crtoolbar A IMG
{
    border-style:none;
    border-width:0
}

.crtoolbarlist
{
    font-size:8pt;
    font-family:verdana,Arial;
}

.crtoolbartextbox
{
    font-size:8pt;
    font-family:verdana,Arial;
}

.crtoolbartextboxgoto
{
	font-size:8pt;
	font-family:verdana,Arial;
	width: 40px;
	height: 16px;
}

.crtoolbartextboxsearch
{
	font-size:8pt;
	font-family:verdana,Arial;
	width: 90px;
	height: 16px;
}

.crtoptoolbar
{
    background-color:#0073AA;
    color:#FFFFFF;
}

.crtoptoolbar A
{
    color:#FFFFFF;
    cursor:hand;
    text-decoration:none;
}

.crtoptoolbar A:hover
{
    color:#FF0000;
    cursor:hand;
    text-decoration:none;
}

.crcontent
{
    font-size:12pt;
}


.promptBorder
{
    background-color: #A3A3BC;
}

.promptHeader
{
      background-color: #E4E4EC;
      color: black;
      font-family: Arial;
      font-weight: bold;
      font-size: 11pt;
}

.promptElement
{
      background-color: #E4E4EC;
      border-top:1px solid #FFFFFF;
}

.promptElementText
{
      color: black;
      font-family: Arial;
      font-size: 11pt;
}

.promptingText
{
      color: black;
      font-family: Arial;
      font-size: 11pt;
}

.promptRuler
{
      color: #A3A3BC;
      height: 1px;
}

.promptMessage
{
      color: black;
      font-family: Arial;
      font-weight: bold;
      font-size: 11pt;
}

.promptTextBox
{
      background-color: #FFFFFF;
      border-top: 1px solid;
      border-bottom: 1px solid;
      border-left: 1px solid;
      border-right: 1px solid;
      border-color: #A3A3BC;
      font-family: Arial;
      font-size: 11pt;
}

.promptButton
{
    border-right: 1px solid;
    border-top: 1px solid;
    border-left: 1px solid;
    border-bottom: 1px solid;
    background-color: #EFEFEC;
    border-color: #A3A3BC;
    font-family: Arial;
    font-size: 11pt;
    cursor: hand;

}

.promptDropDown
{
      background-color: #FFFFFF;
      border-top: 1px solid;
      border-bottom: 1px solid;
      border-left: 1px solid;
      border-right: 1px solid;
      border-color: #A3A3BC;
      font-family: Arial;
      font-size: 11pt;
}

.promptListBox
{
      background-color: #FFFFFF;
      border-top: 1px solid;
      border-bottom: 1px solid;
      border-left: 1px solid;
      border-right: 1px solid;
      border-color: #A3A3BC;
      font-family: Arial;
      font-size: 11pt;
}

.CRGridViewerToolbar
{
    background-color:#E4E4EC;
    color:#000000;
    font-family:verdana,Arial;
    font-size:8pt
}

.CRGridViewerNavigationBar
{
    background-color:#FFFFFF;
    color:#000000;
    font-family:verdana,Arial;
    font-size:8pt;
}

.CRGridViewerHeading
{
    background-color:#CAC9D9;
    color:#000000;
    font-size:10pt;
    font-family:Arial;
    font-weight:normal;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}


.CRGridViewerCell
{
    background-color:#FFFFFF;
    color:#636384;
    font-size:10pt;
    font-family:Arial;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.CRGridViewerCellAlt
{
    background-color:#ECECF2;
    color:#636384;
    font-size:10pt;
    font-family:Arial;
    border-width:0px;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.CRGridViewerRowNumberColumn
{
    background-color:#FFFFFF;
    color:#636384;
    font-size:10pt;
    font-family:Arial;
    border-width:0;
    padding-top:1px;
    padding-bottom:1px;
    padding-left:3px;
    padding-right:3px;
}

.CRGridViewerTable
{
    font-family:Arial;
}

.crgrptr a
{
      color: #000000;
      font-family: Arial;
      font-size: 10pt;
}

.crgrptr a:hover
{
     color: #ff0000;
     cursor: hand;
     text-decoration:underline;
}

.crgrptrrb
{
    cursor: w-resize;
    height: 100%;
    width: 4px;
    background-repeat: repeat-y;
}

.crgrplvl1
{
}

.crgrplvl2
{
}

.crexportmessage
{
    font-size: 8pt;
    font-family: Arial, verdana;
}

.crexportselect
{
    font-size: 8pt;
    font-family: Arial, verdana;
    width:100%;
}

.crexportpage
{
    background-color:#E4E4EC;
}

.crexportruler
{
    color: #A3A3BC;
    height: 1px;
}

.crexporttextbox
{
    font-size: 8pt;
    font-family: Arial, verdana;
}

.crexportbutton
{
    border-right: 1px solid;
    border-top: 1px solid;
    border-left: 1px solid;
    border-bottom: 1px solid;
    background-color: #EFEFEC;
    border-color: #A3A3BC;
    font-family: Arial;
    font-size: 8pt;
    cursor: hand;
    width:20%;
}

.crleftpaneruler
{
    color: #A3A3BC;
    height: 1px;
}

.crleftpanemessage
{
    color: #000000;
    font-size: 8pt;
    font-family: Arial, verdana;
}

.crleftpanemessage a
{
    color: #000000;
    font-size: 8pt;
    font-family: Arial, verdana;
    text-decoration:none;
}

.crleftpanemessage a:hover
{
    cursor: hand;
    text-decoration:underline;
}
.crleftpanetextbox
{
    font-size: 8pt;
    font-family: Arial, verdana;
}

.crleftpaneselect
{
    font-size: 8pt;
    font-family: Arial, verdana;
    width:100%;      
}

.crleftpanefieldset
{
    border: 1px solid;
    border-color: #A3A3BC;
    padding: 0;
}

.crCurrMatchItem
{
    border: 2px solid;
    border-color: #FF0000;
}