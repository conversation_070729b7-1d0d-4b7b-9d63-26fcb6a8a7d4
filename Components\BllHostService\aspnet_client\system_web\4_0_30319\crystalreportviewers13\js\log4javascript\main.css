body {
	font-family: Verdana, Arial, Helvetica, Sans-serif;
	font-size: 75%;
	color: black;
	background-color: #eeeeee;
	text-align: center;
	padding: 0px;
	margin: 0px;
}

div#container {
	width: 770px;
	text-align: left;
	line-height: 150%;
	border-width: 0px 1px 1px 1px;
	border-color: #cccccc;
	border-style: solid;
	background-color: white;
	color: black;
	padding: 10px;
	margin: 0px auto 10px auto;
}

div#header {
	margin: 0px;
	border-bottom: solid #cccccc 1px;
}

div#header h1 {
	font-family: Courier New, Courier, Monospace, Serif;
	padding: 10px 0px;
	margin: 0px;
	font-size: 200%;
	font-weight: bold;
	text-align: right;
}

div#header h1 a {
	color: black;
}

div#nav {
	float: right;
	font-size: 91.66%;
	font-weight: bold;
	padding-top: 5px;
}

div#container.nonav div#content {
	float: none;
	width: auto;
}

*.externallinkinfo {
	float: right;
	font-style: italic;
}

div#content h1 {
	padding: 5px 3px;
	margin: 5px 0px;
	font-size: 175%;
	font-weight: normal;
}

div#content h2 {
	background-color: darkgreen;
	color: white;
	padding: 0px 3px;
	font-size: 116.66%;
	font-weight: bold;
}

div#content h2 a {
	color: white;
}

div#content h3 {
	padding: 0px 3px;
	font-size: 116.66%;
	font-weight: bold;
	border-style: solid;
	border-color: #003399;
	border-width: 1px 0px;
}

div#content h4 {
	padding: 0px 3px;
	font-size: 100%;
	font-weight: bold;
	border-top: solid #eeeeee 1px;
}

div#content h5 {
	padding: 0px;
	margin: 3px 0px;
}

div#footer {
	margin-top: 20px;
	padding: 2px;
	border-top: solid #cccccc 1px;
	font-size: 91.66%;
}

a {
	color: #003399;
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a.bold {
	font-weight: bold;
}

a img {
	border-width: 0px;
}

br.clear {
	clear: both;
}

table {
	font-size: 100%;
}

/* Code */
pre, code {
	font-family: Courier New, Courier;
	font-size: 108.33%;
}

pre.code {
	border: solid 1px #cccccc;
	background-color: #eeeeee;
	padding: 3px;
}

div.example, div.panel {
	border: solid 1px #cccccc;
	background-color: #f5f5f5;
	padding: 3px;
}

div.panel h2 {
	margin: 5px 0px;
}

div.padded {
	padding: 10px;
}

div.hidden {
	display: none;
}

div.active {
	background-color: #fcfffc;
	border-color: green;
}

label.rightofinput, input.rightoflabel {
	margin-right: 20px;
}

/* 'Back to top' link */
p.linktotop {
	text-align: right;
}

ul.propertieslist li.method, ul.propertieslist li.property {
	margin: 0;
	padding: 0px 0px 15px 0px;
}

ul.propertieslist li *.name {
	font-size: 116.66%;
	font-weight: bold;
}

ul.propertieslist li.method div.methodsignature {
	margin: 10px 0px;
	font-size: 116.66%;
	background-color: #eeeeee;
}

ul.propertieslist li.method *.paramsheading {
	font-weight: bold;
}

ul.propertieslist li.method *.params {
	padding-top: 5px;
	padding-bottom: 5px;
}

ul.propertieslist li.method *.params li.param {
	padding-bottom: 10px;
}

ul.propertieslist li.method *.params li.param *.paramname {
	font-style: italic;
}

div.serverlog {
	height: 200px;
	/*border: solid 1px #cccccc;*/
}

div#inPageConsole {
	margin-top: 10px;
}

div.iframecontainer {
	background-color: white;
	border: solid #cccccc 1px;
	width: 100%;
}