if(typeof (dojo)!="undefined"){dojo.provide("MochiKit.Base")}if(typeof (MochiKit)=="undefined"){MochiKit={}}if(typeof (MochiKit.Base)=="undefined"){MochiKit.Base={}}MochiKit.Base.VERSION="1.4";MochiKit.Base.NAME="MochiKit.Base";MochiKit.Base.update=function(B,D){if(B===null){B={}}for(var C=1;C<arguments.length;C++){var E=arguments[C];if(typeof (E)!="undefined"&&E!==null){for(var A in E){B[A]=E[A]}}}return B};MochiKit.Base.update(MochiKit.Base,{__repr__:function(){return"["+this.NAME+" "+this.VERSION+"]"},toString:function(){return this.__repr__()},camelize:function(B){var A=B.split("-");var D=A[0];for(var C=1;C<A.length;C++){D+=A[C].charAt(0).toUpperCase()+A[C].substring(1)}return D},counter:function(A){if(arguments.length===0){A=1}return function(){return A++}},clone:function(B){var A=arguments.callee;if(arguments.length==1){A.prototype=B;return new A()}},extend:function(B,E,D){if(!D){D=0}if(E){var A=E.length;if(typeof (A)!="number"){if(typeof (MochiKit.Iter)!="undefined"){E=MochiKit.Iter.list(E);A=E.length}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(!B){B=[]}for(var C=D;C<A;C++){B.push(E[C])}}return B},updatetree:function(C,E){if(C===null){C={}}for(var D=1;D<arguments.length;D++){var F=arguments[D];if(typeof (F)!="undefined"&&F!==null){for(var B in F){var A=F[B];if(typeof (C[B])=="object"&&typeof (A)=="object"){arguments.callee(C[B],A)}else{C[B]=A}}}}return C},setdefault:function(B,D){if(B===null){B={}}for(var C=1;C<arguments.length;C++){var E=arguments[C];for(var A in E){if(!(A in B)){B[A]=E[A]}}}return B},_newNamedError:function(B,A,C){C.prototype=new MochiKit.Base.NamedError(B.NAME+"."+A);B[A]=C},operator:{identity:function(A){return A}},forwardCall:function(A){return function(){return this[A].apply(this,arguments)}},typeMatcher:function(){var B={};for(var A=0;A<arguments.length;A++){var C=arguments[A];B[C]=C}return function(){for(var D=0;D<arguments.length;D++){if(!(typeof (arguments[D]) in B)){return false}}return true}},isNull:function(){for(var A=0;A<arguments.length;A++){if(arguments[A]!==null){return false}}return true},isUndefinedOrNull:function(){for(var A=0;A<arguments.length;A++){var B=arguments[A];if(!(typeof (B)=="undefined"||B===null)){return false}}return true},isEmpty:function(A){return !MochiKit.Base.isNotEmpty.apply(this,arguments)},isNotEmpty:function(B){for(var A=0;A<arguments.length;A++){var C=arguments[A];if(!(C&&C.length)){return false}}return true},isArrayLike:function(){for(var A=0;A<arguments.length;A++){var C=arguments[A];var B=typeof (C);if((B!="object"&&!(B=="function"&&typeof (C.item)=="function"))||C===null||typeof (C.length)!="number"||C.nodeType===3){return false}}return true},isDateLike:function(){for(var A=0;A<arguments.length;A++){var B=arguments[A];if(typeof (B)!="object"||B===null||typeof (B.getTime)!="function"){return false}}return true},xmap:function(B){if(B===null){return MochiKit.Base.extend(null,arguments,1)}var C=[];for(var A=1;A<arguments.length;A++){C.push(B(arguments[A]))}return C},map:function(J,F){var B=MochiKit.Base;var H=MochiKit.Iter;var K=B.isArrayLike;if(arguments.length<=2){if(!K(F)){if(H){F=H.list(F);if(J===null){return F}}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(J===null){return B.extend(null,F)}var G=[];for(var E=0;E<F.length;E++){G.push(J(F[E]))}return G}else{if(J===null){J=Array}var A=null;for(E=1;E<arguments.length;E++){if(!K(arguments[E])){if(H){return H.list(H.imap.apply(null,arguments))}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}var C=arguments[E].length;if(A===null||A>C){A=C}}G=[];for(E=0;E<A;E++){var I=[];for(var D=1;D<arguments.length;D++){I.push(arguments[D][E])}G.push(J.apply(this,I))}return G}},xfilter:function(B){var C=[];if(B===null){B=MochiKit.Base.operator.truth}for(var A=1;A<arguments.length;A++){var D=arguments[A];if(B(D)){C.push(D)}}return C},filter:function(E,B,C){var F=[];var A=MochiKit.Base;if(!A.isArrayLike(B)){if(MochiKit.Iter){B=MochiKit.Iter.list(B)}else{throw new TypeError("Argument not an array-like and MochiKit.Iter not present")}}if(E===null){E=A.operator.truth}if(typeof (Array.prototype.filter)=="function"){return Array.prototype.filter.call(B,E,C)}else{if(typeof (C)=="undefined"||C===null){for(var D=0;D<B.length;D++){var G=B[D];if(E(G)){F.push(G)}}}else{for(D=0;D<B.length;D++){G=B[D];if(E.call(C,G)){F.push(G)}}}}return F},_wrapDumbFunction:function(func){return function(){switch(arguments.length){case 0:return func();case 1:return func(arguments[0]);case 2:return func(arguments[0],arguments[1]);case 3:return func(arguments[0],arguments[1],arguments[2])}var args=[];for(var i=0;i<arguments.length;i++){args.push("arguments["+i+"]")}return eval("(func("+args.join(",")+"))")}},methodcaller:function(B){var A=MochiKit.Base.extend(null,arguments,1);if(typeof (B)=="function"){return function(C){return B.apply(C,A)}}else{return function(C){return C[B].apply(C,A)}}},method:function(B,C){var A=MochiKit.Base;return A.bind.apply(this,A.extend([C,B],arguments,2))},compose:function(B,F){var E=[];var A=MochiKit.Base;if(arguments.length===0){throw new TypeError("compose() requires at least one argument")}for(var C=0;C<arguments.length;C++){var D=arguments[C];if(typeof (D)!="function"){throw new TypeError(repr(D)+" is not a function")}E.push(D)}return function(){var G=arguments;for(var H=E.length-1;H>=0;H--){G=[E[H].apply(this,G)]}return G[0]}},bind:function(E,C){if(typeof (E)=="string"){E=C[E]}var D=E.im_func;var G=E.im_preargs;var B=E.im_self;var A=MochiKit.Base;if(typeof (E)=="function"&&typeof (E.apply)=="undefined"){E=A._wrapDumbFunction(E)}if(typeof (D)!="function"){D=E}if(typeof (C)!="undefined"){B=C}if(typeof (G)=="undefined"){G=[]}else{G=G.slice()}A.extend(G,arguments,2);var F=function(){var I=arguments;var J=arguments.callee;if(J.im_preargs.length>0){I=A.concat(J.im_preargs,I)}var H=J.im_self;if(!H){H=this}return J.im_func.apply(H,I)};F.im_self=B;F.im_func=D;F.im_preargs=G;return F},bindMethods:function(B){var D=MochiKit.Base.bind;for(var A in B){var C=B[A];if(typeof (C)=="function"){B[A]=D(C,B)}}},registerComparator:function(C,B,A,D){MochiKit.Base.comparatorRegistry.register(C,B,A,D)},_primitives:{"boolean":true,string:true,number:true},compare:function(F,B){if(F==B){return 0}var E=(typeof (F)=="undefined"||F===null);var G=(typeof (B)=="undefined"||B===null);if(E&&G){return 0}else{if(E){return -1}else{if(G){return 1}}}var A=MochiKit.Base;var D=A._primitives;if(!(typeof (F) in D&&typeof (B) in D)){try{return A.comparatorRegistry.match(F,B)}catch(H){if(H!=A.NotFound){throw H}}}if(F<B){return -1}else{if(F>B){return 1}}var C=A.repr;throw new TypeError(C(F)+" and "+C(B)+" can not be compared")},compareDateLike:function(B,A){return MochiKit.Base.compare(B.getTime(),A.getTime())},compareArrayLike:function(B,A){var F=MochiKit.Base.compare;var E=B.length;var G=0;if(E>A.length){G=1;E=A.length}else{if(E<A.length){G=-1}}for(var C=0;C<E;C++){var D=F(B[C],A[C]);if(D){return D}}return G},registerRepr:function(B,A,D,C){MochiKit.Base.reprRegistry.register(B,A,D,C)},repr:function(D){if(typeof (D)=="undefined"){return"undefined"}else{if(D===null){return"null"}}try{if(typeof (D.__repr__)=="function"){return D.__repr__()}else{if(typeof (D.repr)=="function"&&D.repr!=arguments.callee){return D.repr()}}return MochiKit.Base.reprRegistry.match(D)}catch(B){if(typeof (D.NAME)=="string"&&(D.toString==Function.prototype.toString||D.toString==Object.prototype.toString)){return D.NAME}}try{var C=(D+"")}catch(B){return"["+typeof (D)+"]"}if(typeof (D)=="function"){D=C.replace(/^\s+/,"");var A=D.indexOf("{");if(A!=-1){D=D.substr(0,A)+"{...}"}}return C},reprArrayLike:function(B){var A=MochiKit.Base;return"["+A.map(A.repr,B).join(", ")+"]"},reprString:function(A){return('"'+A.replace(/(["\\])/g,"\\$1")+'"').replace(/[\f]/g,"\\f").replace(/[\b]/g,"\\b").replace(/[\n]/g,"\\n").replace(/[\t]/g,"\\t").replace(/[\r]/g,"\\r")},reprNumber:function(A){return A+""},registerJSON:function(B,A,D,C){MochiKit.Base.jsonRegistry.register(B,A,D,C)},evalJSON:function(){return eval("("+arguments[0]+")")},serializeJSON:function(A){var K=typeof (A);if(K=="undefined"){return"undefined"}else{if(K=="number"||K=="boolean"){return A+""}else{if(A===null){return"null"}}}var C=MochiKit.Base;var L=C.reprString;if(K=="string"){return L(A)}var J=arguments.callee;var E;if(typeof (A.__json__)=="function"){E=A.__json__();if(A!==E){return J(E)}}if(typeof (A.json)=="function"){E=A.json();if(A!==E){return J(E)}}if(K!="function"&&typeof (A.length)=="number"){var I=[];for(var G=0;G<A.length;G++){var B=J(A[G]);if(typeof (B)!="string"){B="undefined"}I.push(B)}return"["+I.join(", ")+"]"}try{E=C.jsonRegistry.match(A);if(A!==E){return J(E)}}catch(H){if(H!=C.NotFound){throw H}}if(K=="function"){return null}I=[];for(var D in A){var F;if(typeof (D)=="number"){F='"'+D+'"'}else{if(typeof (D)=="string"){F=L(D)}else{continue}}B=J(A[D]);if(typeof (B)!="string"){continue}I.push(F+":"+B)}return"{"+I.join(", ")+"}"},objEqual:function(B,A){return(MochiKit.Base.compare(B,A)===0)},arrayEqual:function(B,A){if(B.length!=A.length){return false}return(MochiKit.Base.compare(B,A)===0)},concat:function(){var B=[];var C=MochiKit.Base.extend;for(var A=0;A<arguments.length;A++){C(B,arguments[A])}return B},keyComparator:function(B){var A=MochiKit.Base;var D=A.compare;if(arguments.length==1){return function(F,E){return D(F[B],E[B])}}var C=A.extend(null,arguments);return function(F,E){var I=0;for(var H=0;(I===0)&&(H<C.length);H++){var G=C[H];I=D(F[G],E[G])}return I}},reverseKeyComparator:function(B){var A=MochiKit.Base.keyComparator.apply(this,arguments);return function(D,C){return A(C,D)}},partial:function(B){var A=MochiKit.Base;return A.bind.apply(this,A.extend([B,undefined],arguments,1))},listMinMax:function(F,A){if(A.length===0){return null}var E=A[0];var C=MochiKit.Base.compare;for(var B=1;B<A.length;B++){var D=A[B];if(C(D,E)==F){E=D}}return E},objMax:function(){return MochiKit.Base.listMinMax(1,arguments)},objMin:function(){return MochiKit.Base.listMinMax(-1,arguments)},findIdentical:function(A,D,E,B){if(typeof (B)=="undefined"||B===null){B=A.length}if(typeof (E)=="undefined"||E===null){E=0}for(var C=E;C<B;C++){if(A[C]===D){return C}}return -1},mean:function(){var D=0;var A=MochiKit.Base;var B=A.extend(null,arguments);var E=B.length;while(B.length){var F=B.shift();if(F&&typeof (F)=="object"&&typeof (F.length)=="number"){E+=F.length-1;for(var C=F.length-1;C>=0;C--){D+=F[C]}}else{D+=F}}if(E<=0){throw new TypeError("mean() requires at least one argument")}return D/E},median:function(){var B=MochiKit.Base.flattenArguments(arguments);if(B.length===0){throw new TypeError("median() requires at least one argument")}B.sort(compare);if(B.length%2==0){var A=B.length/2;return(B[A]+B[A-1])/2}else{return B[(B.length-1)/2]}},findValue:function(A,E,F,B){if(typeof (B)=="undefined"||B===null){B=A.length}if(typeof (F)=="undefined"||F===null){F=0}var D=MochiKit.Base.compare;for(var C=F;C<B;C++){if(D(A[C],E)===0){return C}}return -1},nodeWalk:function(C,D){var A=[C];var E=MochiKit.Base.extend;while(A.length){var B=D(A.shift());if(B){E(A,B)}}},nameFunctions:function(B){var C=B.NAME;if(typeof (C)=="undefined"){C=""}else{C=C+"."}for(var A in B){var E=B[A];if(typeof (E)=="function"&&typeof (E.NAME)=="undefined"){try{E.NAME=C+A}catch(D){}}}},queryString:function(H,I){if(typeof (MochiKit.DOM)!="undefined"&&arguments.length==1&&(typeof (H)=="string"||(typeof (H.nodeType)!="undefined"&&H.nodeType>0))){var G=MochiKit.DOM.formContents(H);H=G[0];I=G[1]}else{if(arguments.length==1){var A=H;H=[];I=[];for(var B in A){var J=A[B];if(typeof (J)!="function"){H.push(B);I.push(J)}}}}var E=[];var F=Math.min(H.length,I.length);var C=MochiKit.Base.urlEncode;for(var D=0;D<F;D++){J=I[D];if(typeof (J)!="undefined"&&J!==null){E.push(C(H[D])+"="+C(J))}}return E.join("&")},parseQueryString:function(H,I){var C=H.replace(/\+/g,"%20").split("&");var D={};var A;if(typeof (decodeURIComponent)!="undefined"){A=decodeURIComponent}else{A=unescape}if(I){for(var F=0;F<C.length;F++){var E=C[F].split("=");var B=A(E[0]);var G=D[B];if(!(G instanceof Array)){G=[];D[B]=G}G.push(A(E[1]))}}else{for(F=0;F<C.length;F++){E=C[F].split("=");D[A(E[0])]=A(E[1])}}return D}});MochiKit.Base.AdapterRegistry=function(){this.pairs=[]};MochiKit.Base.AdapterRegistry.prototype={register:function(B,A,D,C){if(C){this.pairs.unshift([B,A,D])}else{this.pairs.push([B,A,D])}},match:function(){for(var A=0;A<this.pairs.length;A++){var B=this.pairs[A];if(B[1].apply(this,arguments)){return B[2].apply(this,arguments)}}throw MochiKit.Base.NotFound},unregister:function(A){for(var B=0;B<this.pairs.length;B++){var C=this.pairs[B];if(C[0]==A){this.pairs.splice(B,1);return true}}return false}};MochiKit.Base.EXPORT=["flattenArray","noop","camelize","counter","clone","extend","update","updatetree","setdefault","keys","items","NamedError","operator","forwardCall","itemgetter","typeMatcher","isCallable","isUndefined","isUndefinedOrNull","isNull","isEmpty","isNotEmpty","isArrayLike","isDateLike","xmap","map","xfilter","filter","methodcaller","compose","bind","bindMethods","NotFound","AdapterRegistry","registerComparator","compare","registerRepr","repr","objEqual","arrayEqual","concat","keyComparator","reverseKeyComparator","partial","merge","listMinMax","listMax","listMin","objMax","objMin","nodeWalk","zip","urlEncode","queryString","serializeJSON","registerJSON","evalJSON","parseQueryString","findValue","findIdentical","flattenArguments","method","average","mean","median"];MochiKit.Base.EXPORT_OK=["nameFunctions","comparatorRegistry","reprRegistry","jsonRegistry","compareDateLike","compareArrayLike","reprArrayLike","reprString","reprNumber"];MochiKit.Base._exportSymbols=function(D,B){if(typeof (MochiKit.__export__)=="undefined"){MochiKit.__export__=(MochiKit.__compat__||(typeof (JSAN)=="undefined"&&typeof (dojo)=="undefined"))}if(!MochiKit.__export__){return }var C=B.EXPORT_TAGS[":all"];for(var A=0;A<C.length;A++){D[C[A]]=B[C[A]]}};MochiKit.Base.__new__=function(){var A=this;A.noop=A.operator.identity;A.forward=A.forwardCall;A.find=A.findValue;if(typeof (encodeURIComponent)!="undefined"){A.urlEncode=function(C){return encodeURIComponent(C).replace(/\'/g,"%27")}}else{A.urlEncode=function(C){return escape(C).replace(/\+/g,"%2B").replace(/\"/g,"%22").rval.replace(/\'/g,"%27")}}A.NamedError=function(C){this.message=C;this.name=C};A.NamedError.prototype=new Error();A.update(A.NamedError.prototype,{repr:function(){if(this.message&&this.message!=this.name){return this.name+"("+A.repr(this.message)+")"}else{return this.name+"()"}},toString:A.forwardCall("repr")});A.NotFound=new A.NamedError("MochiKit.Base.NotFound");A.listMax=A.partial(A.listMinMax,1);A.listMin=A.partial(A.listMinMax,-1);A.isCallable=A.typeMatcher("function");A.isUndefined=A.typeMatcher("undefined");A.merge=A.partial(A.update,null);A.zip=A.partial(A.map,null);A.average=A.mean;A.comparatorRegistry=new A.AdapterRegistry();A.registerComparator("dateLike",A.isDateLike,A.compareDateLike);A.registerComparator("arrayLike",A.isArrayLike,A.compareArrayLike);A.reprRegistry=new A.AdapterRegistry();A.registerRepr("arrayLike",A.isArrayLike,A.reprArrayLike);A.registerRepr("string",A.typeMatcher("string"),A.reprString);A.registerRepr("numbers",A.typeMatcher("number","boolean"),A.reprNumber);A.jsonRegistry=new A.AdapterRegistry();var B=A.concat(A.EXPORT,A.EXPORT_OK);A.EXPORT_TAGS={":common":A.concat(A.EXPORT_OK),":all":B};A.nameFunctions(this)};MochiKit.Base.__new__();if(MochiKit.__export__){compare=MochiKit.Base.compare}MochiKit.Base._exportSymbols(this,MochiKit.Base);_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_ie=(document.all!=null)?true:false;_dom=(document.getElementById!=null)?true:false;_isQuirksMode=(document.compatMode!="CSS1Compat");_dtd4=!_ie||(document.compatMode!="BackCompat");_moz=_dom&&!_ie;_show="visible";_hide="hidden";_hand=_ie?"hand":"pointer";_appVer=navigator.appVersion.toLowerCase();_webKit=(_userAgent.indexOf("safari")>=0)||(_userAgent.indexOf("applewebkit")>=0);_mac=(_appVer.indexOf("macintosh")>=0)||(_appVer.indexOf("macos")>=0);_opera=(_userAgent.indexOf("opera")!=-1);_userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;_saf=_moz&&(_userAgent.indexOf("safari")>=0);_ctrl=0;_shift=1;_alt=2;_ie6=_ie&&(_appVer.indexOf("msie 5")<0);_small=(screen.height<=600);_curDoc=document;_curWin=self;_tooltipWin=self;_tooltipDx=0;_tooltipDy=0;_codeWinName="_CW";_leftBtn=(_ie||_saf)?1:0;_preloadArr=new Array;_widgets=new Array;_resizeW=_ie6?"col-resize":"E-resize";_resizeH=_ie6?"row-resize":"S-resize";_ddData=new Array;_dontNeedEncoding=null;_thex=null;_defaultButtonWidth=60;function initDom(D,C,E,B,A){_skin=D;_lang=E;_style=C;if(B){_curWin=B;_curDoc=B.document}_tooltipWin=_curWin;if(A){_codeWinName="_CW"+A}_curWin[_codeWinName]=self}function styleSheet(){includeCSS("style")}function isLayerDisplayed(A){var B=A?A.style:null;if(B){if(B.display=="none"||B.visibility=="hidden"){return false}else{var C=A.parentNode;if(C!=null){return isLayerDisplayed(C)}else{return true}}}else{return true}}function safeSetFocus(A){if(A&&A.focus&&isLayerDisplayed(A)){A.focus()}}function newWidget(B){var A=new Object;A.id=B;A.layer=null;A.css=null;A.getHTML=Widget_getHTML;A.beginHTML=Widget_getHTML;A.endHTML=Widget_getHTML;A.write=Widget_write;A.begin=Widget_begin;A.end=Widget_end;A.init=Widget_init;A.move=Widget_move;A.resize=Widget_resize;A.setBgColor=Widget_setBgColor;A.show=Widget_show;A.getWidth=Widget_getWidth;A.getHeight=Widget_getHeight;A.setHTML=Widget_setHTML;A.setDisabled=Widget_setDisabled;A.focus=Widget_focus;A.setDisplay=Widget_setDisplay;A.isDisplayed=Widget_isDisplayed;A.appendHTML=Widget_appendHTML;A.setTooltip=Widget_setTooltip;A.initialized=Widget_initialized;A.widx=_widgets.length;_widgets[A.widx]=A;return A}function new_Widget(A){return newWidget(A.id)}function getEvent(B,A){if(_ie&&(B==null)){B=A?A.event:_curWin.event}return B}function Widget_param(C,B,A){var D=C?C[B]:null;return D==null?A:D}function Widget_appendHTML(){append(_curDoc.body,this.getHTML())}function Widget_getHTML(){return""}function Widget_write(A){_curDoc.write(this.getHTML(A))}function Widget_begin(){_curDoc.write(this.beginHTML())}function Widget_end(){_curDoc.write(this.endHTML())}function Widget_init(){var A=this;A.layer=getLayer(A.id);A.css=A.layer.style;A.layer._widget=A.widx;if(A.initialHTML){A.setHTML(A.initialHTML)}}function Widget_move(A,C){var B=this.css;if(A!=null){if(_moz){B.left=""+A+"px"}else{B.pixelLeft=A}}if(C!=null){if(_moz){B.top=""+C+"px"}else{B.pixelTop=C}}}function Widget_focus(){safeSetFocus(this.layer)}function Widget_setBgColor(A){this.css.backgroundColor=A}function Widget_show(A){this.css.visibility=A?_show:_hide}function Widget_getWidth(){return this.layer.offsetWidth}function Widget_getHeight(){return this.layer.offsetHeight}function Widget_setHTML(A){var B=this;if(B.layer){B.layer.innerHTML=A}else{B.initialHTML=A}}function Widget_setDisplay(A){if(this.css){this.css.display=A?"":"none"}}function Widget_isDisplayed(){if(this.css.display=="none"){return false}else{return true}}function Widget_setDisabled(A){if(this.layer){this.layer.disabled=A}}function Widget_resize(A,B){if(A!=null){this.css.width=""+(Math.max(0,A))+"px"}if(B!=null){this.css.height=""+(Math.max(0,B))+"px"}}function Widget_setTooltip(A){this.layer.title=A}function Widget_initialized(){return this.layer!=null}function newGrabberWidget(A,D,G,F,H,E,C,B,I){o=newWidget(A);o.resizeCB=D;o.x=G;o.y=F;o.w=H;o.h=E;o.dx=0;o.dy=0;o.min=null;o.max=null;o.isHori=C;o.preloaded=new Image;o.preloaded.src=_skin+"../resizepattern.gif";o.buttonCB=B;o.allowGrab=true;o.collapsed=false;o.isFromButton=false;o.showGrab=GrabberWidget_showGrab;o.setCollapsed=GrabberWidget_setCollapsed;o.tooltipButton=I;o.getHTML=GrabberWidget_getHTML;o.enableGrab=GrabberWidget_enableGrab;o.setMinMax=GrabberWidget_setMinMax;if(window._allGrabbers==null){window._allGrabbers=new Array}o.index=_allGrabbers.length;_allGrabbers[o.index]=o;o.buttonLyr=null;o.setButtonImage=GrabberWidget_setButtonImage;o.getImgOffset=GrabberWidget_getImgOffset;return o}function GrabberWidget_setCollapsed(B,A){this.collapsed=B;this.setButtonImage(false,A)}function GrabberWidget_getImgOffset(A){var B=this;if(B.isHori){B.dx=(B.collapsed?12:0)+(A?6:0);B.dy=0}else{B.dy=(B.collapsed?12:0)+(A?6:0);B.dx=0}}function GrabberWidget_setButtonImage(A,B){var C=this;C.getImgOffset(A);C.tooltipButton=B;if(C.layer){if(C.buttonLyr==null){C.buttonLyr=getLayer("grabImg_"+C.id)}if(C.buttonLyr){changeSimpleOffset(C.buttonLyr,C.dx,C.dy,null,B)}}}function GrabberWidget_enableGrab(A){var B=this;B.allowGrab=A;if(B.css){B.css.cursor=B.allowGrab?(B.isHori?_resizeW:_resizeH):"default"}}function GrabberWidget_getHTML(){var B=this;var A=B.isHori?_resizeW:_resizeH;var D='onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".GrabberWidget_down(event,'"+B.index+"',this);return false;\"";var C=_ie?('<img onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+B.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+A+'">'):('<div onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" id="modal_'+B.id+'" style="z-index:10000;display:none;position:absolute;top:0px;left:0px;width:1px;height:1px;cursor:'+A+'"></div>');return getBGIframe("grabIframe_"+B.id)+C+'<table cellpadding="0" cellspacing="0" border="0" '+D+' id="'+B.id+'" style="overflow:hidden;position:absolute;left:'+B.x+"px;top:"+B.y+"px;width:"+B.w+"px;height:"+B.h+"px;cursor:"+A+'"><tr><td></td></tr></table>'}function GrabberWidget_setMinMax(B,A){this.min=B;this.max=A}function GrabberWidget_button(e,index,lyr){var o=_allGrabbers[index];o.isFromButton=true;lyr.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_buttonup")}function GrabberWidget_buttonover(C,B,A){var D=_allGrabbers[B];D.setButtonImage(true)}function GrabberWidget_buttonout(C,B,A){var D=_allGrabbers[B];D.setButtonImage(false)}function GrabberWidget_buttonup(A){GrabberWidget_up(A)}function GrabberWidget_showGrab(){var D=this,C=D.mod,E=D.iframe,B=D.layer.style,A=C.style;E.setDisplay(true)}function GrabberWidget_down(e,index,lyr){var o=_allGrabbers[index];window._theGrabber=o;if(o.mod==null){o.mod=getLayer("modal_"+o.id);o.iframe=newWidget("grabIframe_"+o.id);o.iframe.init()}o.mod.onmousemove=eval("_curWin."+_codeWinName+".GrabberWidget_move");o.mod.onmouseup=eval("_curWin."+_codeWinName+".GrabberWidget_up");o.grabStartPosx=parseInt(lyr.style.left);o.grabStartPosy=parseInt(lyr.style.top);o.grabStartx=eventGetX(e);o.grabStarty=eventGetY(e);var mod=o.mod,ifr=o.iframe,stl=o.layer.style,st=mod.style;stl.backgroundImage="url('"+_skin+"../resizepattern.gif')";o.prevZ=stl.zIndex;stl.zIndex=9999;ifr.css.zIndex=9998;st.width="100%";st.height="100%";mod.style.display="block";var p=getPos(o.layer);ifr.move(p.x,p.y);ifr.resize(o.getWidth(),o.getHeight());if(!o.isFromButton){o.showGrab()}return false}function GrabberWidget_move(F){var C=_theGrabber,E=C.layer,H=C.mod;if(C.isFromButton){if(C.isHori){var I=eventGetX(F),D=C.grabStartx;if((I<D-3)||(I>D+3)){C.isFromButton=false}}else{var A=eventGetY(F),B=C.grabStarty;if((G<B-3)||(G>B+3)){C.isFromButton=false}}if(!C.isFromButton){C.showGrab()}}if(!C.isFromButton){if(C.allowGrab){var I=C.isHori?Math.max(0,C.grabStartPosx-C.grabStartx+eventGetX(F)):null;var G=C.isHori?null:Math.max(0,C.grabStartPosy-C.grabStarty+eventGetY(F));if(C.isHori){if(C.min!=null){I=Math.max(I,C.min)}if(C.max!=null){I=Math.min(I,C.max)}}else{if(C.min!=null){G=Math.max(G,C.min)}if(C.max!=null){G=Math.min(G,C.max)}}eventCancelBubble(F);C.move(I,G);getPos(C.layer);if(C.buttonCB){var J=C.buttonLyr.style;if(J.display!="none"){J.display="none"}}C.iframe.move(I,G)}}}function GrabberWidget_up(E){var F=_theGrabber,B=F.layer,D=F.mod,C=B.style;C.backgroundImage="";C.zIndex=F.prevZ;var G=F.iframe;G.move(-100,-100);G.resize(1,1);G.setDisplay(false);eventCancelBubble(E);var A=D.style;A.display="none";A.width="0px";A.height="0px";if(F.buttonCB){F.buttonLyr.style.display=""}if(F&&(F.isFromButton)){if(F.buttonCB){F.buttonCB()}F.isFromButton=false}if(F.allowGrab&&(!F.isFromButton)){if(F.resizeCB){F.resizeCB(parseInt(B.style.left),parseInt(B.style.top))}}}function newButtonWidget(C,L,H,D,F,Q,G,I,B,M,K,P,N,J,A,O){var E=newWidget(C);E.label=L;E.cb=H;E.width=D;E.hlp=F;E.tooltip=Q;E.tabIndex=G;E.isGray=false;E.isDefault=false;E.txt=null;E.icn=null;E.margin=I?I:0;E.extraStyle="";E.isDelayCallback=true;if(B){E.url=B;E.w=M;E.h=K;E.dx=P;E.dy=N;E.disDx=(A!=null)?A:P;E.disDy=(O!=null)?O:N;E.imgRight=J?true:false}E.getHTML=ButtonWidget_getHTML;E.setDisabled=ButtonWidget_setDisabled;E.setText=ButtonWidget_setText;E.changeImg=ButtonWidget_changeImg;E.oldInit=E.init;E.init=ButtonWidget_init;E.isDisabled=ButtonWidget_isDisabled;E.setDefaultButton=ButtonWidget_setDefaultButton;E.executeCB=ButtonWidget_executeCB;E.setTooltip=ButtonWidget_setTooltip;E.setDelayCallback=ButtonWidget_setDelayCallback;E.instIndex=ButtonWidget_currInst;ButtonWidget_inst[ButtonWidget_currInst++]=E;return E}ButtonWidget_inst=new Array;ButtonWidget_currInst=0;function ButtonWidget_getHTML(){with(this){var clk=_codeWinName+".ButtonWidget_clickCB("+this.instIndex+');return false;"';var clcbs='onclick="'+clk+'" ';if(_ie){clcbs+='ondblclick="'+clk+'" '}var isDefaultSty=(this.isDefault&&!this.isGray);clcbs+='onkeydown=" return '+_codeWinName+".ButtonWidget_keydownCB(event,"+this.instIndex+');" ';var url1=_skin+"button.gif",addPar=' style="'+extraStyle+"cursor:"+_hand+";margin-left:"+margin+"px; margin-right:"+margin+'px; "'+clcbs+" ",tip=attr("title",tooltip),idText="theBttn"+id,idIcon="theBttnIcon"+id;var bg=backImgOffset(url1,0,isDefaultSty?105:42);var lnkB="<a "+attr("id",idText)+" "+tip+" "+attr("tabindex",tabIndex)+' href="javascript:void(0)" class="wizbutton" role="button">';var l=(label!=null);var im=(this.url?('<td align="'+(l?(this.imgRight?"right":"left"):"center")+'" style="'+bg+'" width="'+(!l&&(width!=null)?width+6:w+6)+'">'+(l?"":lnkB)+simpleImgOffset(url,w,h,this.isGray?disDs:dx,this.isGray?disDy:dy,idIcon,null,(l?"":tooltip),"cursor:"+_hand)+(l?"":"</a>")+"</td>"):"");return'<table onmouseover="return true" '+attr("id",id)+" "+addPar+' border="0" cellspacing="0" cellpadding="0"><tr valign="middle"><td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?63:0)+'"></td>'+(this.imgRight?"":im)+(l?("<td "+attr("width",width)+attr("id","theBttnCenterImg"+id)+' align="center" class="'+(this.isGray?"wizbuttongray":"wizbutton")+'" style="padding-left:3px;padding-right:3px;'+bg+'"><nobr>'+lnkB+label+"</a></nobr></td>"):"")+(this.imgRight?im:"")+'<td height="21" width="5" style="'+backImgOffset(url1,0,isDefaultSty?84:21)+'"></td></tr></table>'}}function ButtonWidget_setDelayCallback(A){this.isDelayCallback=(A==true)}function ButtonWidget_setDisabled(F){var E=this,D=F?"default":_hand;E.isGray=F;if(E.layer){var B=F?"wizbuttongray":"wizbutton";if(E.txt.className!=B){E.txt.className=B;E.txt.style.cursor=D;E.css.cursor=D;if(E.icn){changeSimpleOffset(E.icn,E.isGray?E.disDx:E.dx,E.isGray?E.disDy:E.dy);E.icn.style.cursor=D}if(E.isDefault){var A=!F,C=_skin+"button.gif";changeSimpleOffset(E.leftImg,0,A?63:0,C);changeOffset(E.centerImg,0,A?105:42,C);changeSimpleOffset(E.rightImg,0,A?84:21,C)}}}}function ButtonWidget_setDefaultButton(){var C=this;if(C.layer){var A=!C.isGray,B=_skin+"button.gif";changeSimpleOffset(C.leftImg,0,A?63:0,B);changeOffset(C.centerImg,0,A?105:42,B);changeSimpleOffset(C.rightImg,0,A?84:21,B)}C.isDefault=true}function ButtonWidget_isDisabled(){return this.isGray}function ButtonWidget_setText(A){this.txt.innerHTML=convStr(A)}function ButtonWidget_setTooltip(A){var B=this;B.tooltip=A;B.layer.title=A;if(B.txt){B.txt.title=A}if(B.icn){B.icn.title=A}}function ButtonWidget_init(){var B=this;B.oldInit();B.txt=getLayer("theBttn"+this.id);B.icn=getLayer("theBttnIcon"+this.id);B.leftImg=getLayer("theBttnLeftImg"+this.id);B.centerImg=getLayer("theBttnCenterImg"+this.id);B.rightImg=getLayer("theBttnRightImg"+this.id);var A=B.isGray?"wizbuttongray":"wizbutton";if(B.txt.className!=A){B.setDisabled(B.isGray)}}function ButtonWidget_changeImg(B,A,E,D,C,F){var G=this;if(C){G.url=C}if(B!=null){G.dx=B}if(A!=null){G.dy=A}if(E!=null){G.disDx=E}if(D!=null){G.disDy=D}if(F!=null){G.tooltip=F}if(G.icn){changeSimpleOffset(G.icn,G.isGray?G.disDx:G.dx,G.isGray?G.disDy:G.dy,G.url,G.tooltip)}}function ButtonWidget_clickCB(A){var B=ButtonWidget_inst[A];if(B&&!B.isGray){if(B.isDelayCallback){setTimeout("ButtonWidget_delayClickCB("+A+")",1)}else{ButtonWidget_delayClickCB(A)}}}function ButtonWidget_delayClickCB(A){var B=ButtonWidget_inst[A];B.executeCB()}function ButtonWidget_executeCB(){var o=this;if(o.cb){if(typeof o.cb!="string"){o.cb()}else{eval(o.cb)}}}function ButtonWidget_keydownCB(D,B){var A=eventGetKey(D);var C=ButtonWidget_inst[B];if(A==13&&C.cb){eventCancelBubble(D)}return true}function newScrolledZoneWidget(G,C,E,B,D,A){var F=newWidget(G);F.borderW=C;F.padding=E;F.w=B;F.h=D;F.oldResize=F.resize;F.beginHTML=ScrolledZoneWidget_beginHTML;F.endHTML=ScrolledZoneWidget_endHTML;F.resize=ScrolledZoneWidget_resize;F.bgClass=(A)?A:"insetBorder";return F}function ScrolledZoneWidget_beginHTML(){var A=this.w,B=this.h;var C=_moz?2*(this.borderW+this.padding):0;if(typeof (A)=="number"){if(_moz){A=Math.max(0,A-C)}A=""+A+"px"}if(typeof (B)=="number"){if(_moz){B=Math.max(0,B-C)}B=""+B+"px"}return'<div tabindex=-1 align="left" class="'+this.bgClass+'" id="'+this.id+'" style="border-width:'+this.borderW+"px;padding:"+this.padding+"px;"+sty("width",A)+sty("height",B)+'overflow:auto">'}function ScrolledZoneWidget_endHTML(){return"</div>"}function ScrolledZoneWidget_resize(A,B){if(_moz){var C=2*(this.borderW+this.padding);if(A!=null){A=Math.max(0,A-C)}if(B!=null){B=Math.max(0,B-C)}}this.oldResize(A,B)}function newComboWidget(F,E,A,B,C){var D=newWidget(F);D.tooltip=C;D.size=1;D.getHTML=ComboWidget_getHTML;D.beginHTML=ComboWidget_beginHTML;D.endHTML=ComboWidget_endHTML;D.changeCB=E;D.noMargin=A;D.width=B==null?null:""+B+"px";D.add=ComboWidget_add;D.del=ComboWidget_del;D.getSelection=ComboWidget_getSelection;D.select=ComboWidget_select;D.valueSelect=ComboWidget_valueSelect;D.getCount=ComboWidget_getCount;D.oldSetDisabled=D.setDisabled;D.setDisabled=ComboWidget_setDisabled;D.setUndefined=ComboWidget_setUndefined;D.delByID=ComboWidget_delByID;D.findByValue=ComboWidget_findByValue;D.findByText=ComboWidget_findByText;D.getValue=ComboWidget_getValue;D.isGrayed=ComboWidget_isGrayed;D.clearSelection=ComboWidget_clearSelection;D.isDisabled=false;D.multi=false;D.undef=false;D.isCombo=true;D.undefId=D.id+"__undef";D.disabledId=D.id+"__disabled";return D}_extrCmbS=(_moz?"font-size:12px;":"");function ComboWidget_beginHTML(){var B=this,A=((_moz&&!B.isCombo)?"font-size:12px;":"");return"<select "+(B.multi?"multiple":"")+" "+(B.noMargin?'style="'+sty("width",B.width)+A+'"':'style="'+sty("width",B.width)+"margin-left:10px;"+A+'"')+' class="listinputs" '+attr("onchange",_codeWinName+".ComboWidget_changeCB(event,this)")+attr("onclick",_codeWinName+".ComboWidget_clickCB(event,this)")+attr("ondblclick",_codeWinName+".ComboWidget_dblClickCB(event,this)")+attr("onkeyup",_codeWinName+".ComboWidget_keyUpCB(event,this)")+attr("onkeydown",_codeWinName+".ComboWidget_keyDownCB(event,this)")+attr("id",B.id)+attr("name",B.id)+attr("title",B.tooltip)+'size="'+B.size+'">'}function ComboWidget_clearSelection(){var A=this;if(A.layer){A.layer.selectedIndex=-1}}function ComboWidget_endHTML(){return"</select>"}function ComboWidget_getHTML(A){return this.beginHTML()+(A?A:"")+this.endHTML()}function ComboWidget_add(B,F,C,G,E){var D=this.layer,A=_curDoc.createElement("option");if(_ie){D.options.add(A)}else{D.appendChild(A)}if(A.innerText!=null){A.innerText=B}else{A.innerHTML=convStr(B)}A.value=F;if(G!=null){A.id=G}if(C){A.selected=true}if(E){A.style.color="gray"}return A}function ComboWidget_getSelection(){var C=this.layer,B=C.selectedIndex;if(B<0){return null}var A=new Object;A.index=B;A.value=C.options[B].value;A.text=C.options[B].text;return A}function ComboWidget_select(B){var D=this,C=D.layer,A=C.options.length;if(B==null){C.selectedIndex=-1}if((B<0)||(B>=A)){B=A-1}if(B>=0){C.selectedIndex=B}D.setUndefined(false)}function ComboWidget_valueSelect(B){var F=this,E=F.layer,D=E.options,A=D.length;for(var C=0;C<A;C++){if(D[C].value==B){D[C].selected=true;F.setUndefined(false);break}}}function ComboWidget_del(A){var B=this.layer;if(A==null){B.options.length=0}else{if(_ie){B.remove(A)}else{B.options[A]=null}this.select(A)}}function ComboWidget_changeCB(B,A){var C=getWidget(A);if(C.changeCB){C.changeCB(B)}}function ComboWidget_clickCB(B,A){var C=getWidget(A);if(C.clickCB){C.clickCB(B)}}function ComboWidget_dblClickCB(B,A){var C=getWidget(A);if(C.dblClickCB){C.dblClickCB(B)}}function ComboWidget_keyUpCB(B,A){var C=getWidget(A);if(C.keyUpCB){C.keyUpCB(B)}}function ComboWidget_keyDownCB(C,A){var B=eventGetKey(C);var D=getWidget(A);if(D.isCombo&&(B==27||B==13)){eventCancelBubble(C)}else{if(B==13&&D.keyUpCB){eventCancelBubble(C)}}}function ComboWidget_getCount(){return this.layer.options.length}function ComboWidget_delByID(B){var A=getLayer(B);if(A!=null){this.del(A.index)}A=null}function ComboWidget_setDisabled(D,B){var C=this;C.oldSetDisabled(D);C.isDisabled=D;if(D==true){var A=getLayer(C.disabledId);if(A==null){C.add("","",true,C.disabledId)}else{C.layer.selectedIndex=A.index}}else{C.delByID(C.disabledId)}}function ComboWidget_setUndefined(B){var C=this;C.undef=B;if(B==true){var A=getLayer(C.undefId);if(A==null){C.add("","",true,C.undefId)}else{C.layer.selectedIndex=A.index}}else{C.delByID(C.undefId)}}function ComboWidget_findByValue(G){var F=this,E=F.layer,D=E.options,A=D.length;for(var C=0;C<A;C++){if(D[C].value==G){var B=new Object;B.index=C;B.value=E.options[C].value;B.text=E.options[C].text;return B}}return null}function ComboWidget_findByText(B){var G=this,F=G.layer,E=F.options,A=E.length;for(var D=0;D<A;D++){if(E[D].text==B){var C=new Object;C.index=D;C.value=F.options[D].value;C.text=F.options[D].text;return C}}return null}function ComboWidget_getValue(C){var F=this,E=F.layer,D=E.options,A=D.length;if(C==null||C<0||C>A){return null}var B=new Object;B.index=C;B.value=E.options[C].value;return B}function ComboWidget_isGrayed(B){var E=this,D=E.layer,C=D.options,A=C.length;if(B==null||B<0||B>A){return false}return(D.options[B].style.color=="gray")}function newListWidget(A,F,E,B,J,I,G,D,H){var C=newComboWidget(A,F,true,B,I);C.clickCB=H;C.dblClickCB=G;C.keyUpCB=D;C.size=J;C.multi=E;C.getMultiSelection=ListWidget_getMultiSelection;C.setUndefined=ListWidget_setUndefined;C.isUndefined=ListWidget_isUndefined;C.change=ListWidget_change;C.isCombo=false;return C}function ListWidget_setUndefined(A){var B=this;B.undef=A;if(A==true){B.layer.selectedIndex=-1}}function ListWidget_isUndefined(){return(this.layer.selectedIndex==-1)}function ListWidget_getMultiSelection(){var F=this.layer,E=new Array,A=F.options.length;for(var D=0;D<A;D++){var C=F.options[D];if(C.selected){var B=new Object;B.index=D;B.value=C.value;B.text=C.text;E[E.length]=B}}return E}function ListWidget_change(B,A){var C=this;if(B!=null){C.multi=B;C.layer.multiple=B}if(A!=null){C.size=A;C.layer.size=A}}function newInfoWidget(F,E,B,D,A){var C=newWidget(F);C.title=E?E:"";C.boldTitle=B?B:"";C.text=D?D:"";C.height=(A!=null)?A:55;C.getHTML=InfoWidget_getHTML;C.setText=InfoWidget_setText;C.setTitle=InfoWidget_setTitle;C.setTitleBold=InfoWidget_setTitleBold;C.oldResize=C.resize;C.resize=InfoWidget_resize;C.textLayer=null;return C}function InfoWidget_setText(D,B){var C=this;D=D?D:"";C.text=D;if(C.layer){var A=C.textLayer;if(A==null){A=C.textLayer=getLayer("infozone_"+C.id)}if(A){A.innerHTML=B?D:convStr(D,false,true)}}}function InfoWidget_setTitle(C){var B=this;C=C?C:"";B.title=C;if(B.layer){var A=B.titleLayer;if(A==null){A=B.titleLayer=getLayer("infotitle_"+B.id)}if(A){A.innerHTML=convStr(C)}}}function InfoWidget_setTitleBold(C){var B=this;C=C?C:"";B.boldTitle=C;if(B.layer){var A=B.titleLayerBold;if(A==null){A=B.titleLayerBold=getLayer("infotitlebold_"+B.id)}if(A){A.innerHTML=convStr(C)}}}function InfoWidget_getHTML(){var A=this;return'<div class="dialogzone" align="left" style="overflow:hidden;'+sty("width",A.width)+sty("height",""+A.height+"px")+'" id="'+A.id+'"><nobr>'+img(_skin+"../help.gif",16,16,"top",null,_helpLab)+'<span class="dialogzone" style="padding-left:5px" id="infotitle_'+A.id+'">'+convStr(A.title)+'</span><span style="padding-left:5px" class="dialogzonebold" id="infotitlebold_'+A.id+'">'+convStr(A.boldTitle)+"</span></nobr><br>"+getSpace(1,2)+'<div class="infozone" align="left" id="infozone_'+A.id+'" style="height:'+(A.height-18-(_moz?10:0))+"px;overflow"+(_ie?"-y":"")+':auto">'+convStr(A.text,false,true)+"</div></div>"}function InfoWidget_resize(B,C){var D=this;if(B!=null){D.w=B}if(C!=null){D.h=C}D.oldResize(B,C);if(D.layer){var A=D.textLayer;if(A==null){A=D.textLayer=getLayer("infozone_"+D.id)}if(A){if(D.h!=null){A.style.height=""+Math.max(0,D.h-(_ie?18:28))+"px"}}}}function newCheckWidget(A,H,F,E,I,G,D,C){var B=newWidget(A);B.text=H;B.convText=C;B.changeCB=F;B.idCheckbox="check_"+A;B.checkbox=null;B.kind="checkbox";B.name=B.idCheckbox;B.bold=E;B.imgUrl=I;B.imgW=G;B.imgH=D;B.getHTML=CheckWidget_getHTML;B.setText=CheckWidget_setText;B.parentInit=Widget_init;B.init=CheckWidget_init;B.check=CheckWidget_check;B.isChecked=CheckWidget_isChecked;B.setDisabled=CheckWidget_setDisabled;B.isDisabled=CheckWidget_isDisabled;B.uncheckOthers=CheckWidget_uncheckOthers;B.isIndeterminate=CheckWidget_isIndeterminate;B.setIndeterminate=CheckWidget_setIndeterminate;B.layerClass=("dialogzone"+(B.bold?"bold":""));B.nobr=true;return B}function CheckWidget_getHTML(){var B=this,A=B.layerClass;return'<table border="0" onselectstart="return false" cellspacing="0" cellpadding="0" class="'+A+'"'+attr("id",B.id)+'><tr valign="middle"><td style="height:20px;width:21px"><input style="margin:'+(_moz?3:0)+'px" onclick="'+_codeWinName+'.CheckWidget_changeCB(event,this)" type="'+B.kind+'"'+attr("id",B.idCheckbox)+attr("name",B.name)+"></td>"+(B.imgUrl?'<td><label style="padding-left:2px" for="'+B.idCheckbox+'">'+img(B.imgUrl,B.imgW,B.imgH)+"</label></td>":"")+"<td>"+(B.nobr?"<nobr>":"")+'<label style="padding-left:'+(B.imgUrl?4:2)+'px" id="label_'+B.id+'" for="'+B.idCheckbox+'">'+(B.convText?convStr(B.text):B.text)+"</label>"+(B.nobr?"</nobr>":"")+"</td></tr></table>"}function CheckWidget_setText(A){var B=this;B.text=A;if(B.layer){if(B.labelLyr==null){B.labelLyr=getLayer("label_"+B.id)}B.labelLyr.innerHTML=B.convText?convStr(A):A}}function CheckWidget_init(){this.parentInit();this.checkbox=getLayer(this.idCheckbox)}function CheckWidget_check(A){this.checkbox.checked=A;if(A){this.uncheckOthers()}}function CheckWidget_isChecked(){return this.checkbox.checked}function CheckWidget_changeCB(B,A){var C=getWidget(A);C.uncheckOthers();if(C.changeCB){C.changeCB(B)}}function CheckWidget_setDisabled(A){this.checkbox.disabled=A;if(_moz){this.checkbox.className=(A?"dialogzone":"")}}function CheckWidget_isDisabled(){return this.checkbox.disabled}function CheckWidget_uncheckOthers(){}function CheckWidget_isIndeterminate(){return this.checkbox.indeterminate}function CheckWidget_setIndeterminate(A){this.checkbox.indeterminate=A}function newRadioWidget(A,I,J,G,F,K,H,D,C){var B=newCheckWidget(A,J,G,F,K,H,D,C);B.kind="radio";B.name=I;if(_RadioWidget_groups[I]==null){_RadioWidget_groups[I]=new Array}B.groupInstance=_RadioWidget_groups[I];var E=B.groupInstance;B.groupIdx=E.length;E[E.length]=B;B.uncheckOthers=RadioWidget_uncheckOthers;return B}var _RadioWidget_groups=new Array;function RadioWidget_uncheckOthers(){var D=this.groupInstance,B=this.groupIdx,A=D.length;for(var C=0;C<A;C++){if(C!=B){var E=D[C].checkbox;if(E){E.checked=false}}}}function newTextFieldWidget(C,G,J,F,I,B,K,D,A,H){var E=newWidget(C);E.tooltip=K;E.changeCB=G;E.maxChar=J;E.keyUpCB=F;E.enterCB=I;E.noMargin=B;E.width=D==null?null:""+D+"px";E.focusCB=A;E.blurCB=H;E.disabled=false;E.getHTML=TextFieldWidget_getHTML;E.getValue=TextFieldWidget_getValue;E.setValue=TextFieldWidget_setValue;E.intValue=TextFieldWidget_intValue;E.intPosValue=TextFieldWidget_intPosValue;E.select=TextFieldWidget_select;E.setDisabled=TextFieldWidget_setDisabled;E.beforeChange=null;E.wInit=E.init;E.init=TextFieldWidget_init;E.oldValue="";E.helpTxt="";E.isHelpTxt=false;E.setHelpTxt=TextFieldWidget_setHelpTxt;E.eraseHelpTxt=TextFieldWidget_eraseHelpTxt;E.enterCancelBubble=true;return E}function TextFieldWidget_setDisabled(B){var A=this;A.disabled=B;if(A.layer){A.layer.disabled=B}}function TextFieldWidget_init(){var A=this;A.wInit();A.layer.value=""+(A.oldValue!="")?A.oldValue:"";if(A.helpTxt&&!A.oldValue){A.setHelpTxt(A.helpTxt)}}function TextFieldWidget_getHTML(){var A=this;return"<input"+(A.disabled?" disabled":"")+' oncontextmenu="event.cancelBubble=true;return true" style="'+sty("width",this.width)+(_moz?"margin-top:1px;margin-bottom:1px;padding-left:5px;padding-right:2px;":"")+(_isQuirksMode?"height:20px;":"height:16px;")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="'+_codeWinName+'.TextFieldWidget_focus(this)" onblur="'+_codeWinName+'.TextFieldWidget_blur(this)" onchange="'+_codeWinName+'.TextFieldWidget_changeCB(event,this)" onkeydown=" return '+_codeWinName+'.TextFieldWidget_keyDownCB(event,this);" onkeyup=" return '+_codeWinName+'.TextFieldWidget_keyUpCB(event,this);" onkeypress=" return '+_codeWinName+'.TextFieldWidget_keyPressCB(event,this);" type="text" '+attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="textinputs" id="'+this.id+'" name="'+this.id+'"'+attr("title",this.tooltip)+' value="">'}function TextFieldWidget_getValue(){var A=this;if(A.isHelpTxt){return""}else{return A.layer?A.layer.value:A.oldValue}}function TextFieldWidget_setValue(A){var B=this;if(B.layer){B.eraseHelpTxt();B.layer.value=""+A}else{B.oldValue=A}}function TextFieldWidget_changeCB(B,A){var C=getWidget(A);C.eraseHelpTxt();if(C.beforeChange){C.beforeChange()}if(C.changeCB){C.changeCB(B)}}function TextFieldWidget_keyPressCB(B,A){var C=getWidget(A);if(eventGetKey(B)==13){C.enterKeyPressed=true;return false}else{C.enterKeyPressed=false}return true}function TextFieldWidget_keyUpCB(B,A){var C=getWidget(A);C.eraseHelpTxt();if(eventGetKey(B)==13&&C.enterKeyPressed){if(C.beforeChange){C.beforeChange()}if(C.enterCB){if(C.enterCancelBubble){eventCancelBubble(B)}C.enterCB(B)}return false}else{if(C.keyUpCB){C.keyUpCB(B)}}C.enterKeyPressed=false;return true}function TextFieldWidget_keyDownCB(B,A){var C=getWidget(A);C.eraseHelpTxt();C.enterKeyPressed=false;if(eventGetKey(B)==13){return true}else{if(eventGetKey(B)==8){eventCancelBubble(B)}}return true}function TextFieldWidget_eraseHelpTxt(){var A=this;if(A.isHelpTxt){A.layer.value=""}A.isHelpTxt=false;A.layer.style.color="black"}function TextFieldWidget_focus(A){var B=getWidget(A);B.eraseHelpTxt();if(B.focusCB){B.focusCB()}}function TextFieldWidget_blur(A){var B=getWidget(A);if(B.beforeChange){B.beforeChange()}if(B.blurCB){B.blurCB()}}function TextFieldWidget_intValue(A){var B=parseInt(this.getValue());return isNaN(B)?A:B}function TextFieldWidget_intPosValue(A){var B=this.intValue(A);return(B<0)?A:B}function TextFieldWidget_select(){this.layer.select()}function TextFieldWidget_setHelpTxt(A){var B=this;B.helpTxt=A;if(B.layer&&(B.layer.value=="")){B.isHelpTxt=true;B.layer.value=A;B.layer.style.color="#808080"}}function newIntFieldWidget(C,G,I,F,H,B,J,D,A){var E=newTextFieldWidget(C,G,I,F,H,B,J,D);E.min=-Number.MAX_VALUE;E.max=Number.MAX_VALUE;E.customCheckCB=A;E.setMin=IntFieldWidget_setMin;E.setMax=IntFieldWidget_setMax;E.setValue=IntFieldWidget_setValue;E.beforeChange=IntFieldWidget_checkChangeCB;E.value="";return E}function IntFieldWidget_setMin(A){if(!isNaN(A)){this.min=A}}function IntFieldWidget_setMax(A){if(!isNaN(A)){this.max=A}}function IntFieldWidget_setValue(B){var C=this,A=C.layer;B=""+B;if(B==""){if(A){A.value=""}C.oldValue="";return }var D=parseInt(B);value="";if(!isNaN(D)&&(D>=C.min)&&(D<=C.max)&&((C.customCheckCB==null)||C.customCheckCB(D))){value=D;C.oldValue=value}else{if(C.oldValue){value=C.oldValue}}if(A){A.value=""+value}}function IntFieldWidget_checkChangeCB(){var A=this;A.setValue(A.layer.value)}function newFrameZoneWidget(E,A,C,B){var D=newWidget(E);D.w=(A!=null)?""+Math.max(0,A-10)+"px":null;D.h=(C!=null)?""+Math.max(0,C-10)+"px":null;D.reverse=(B!=null)?B:false;D.cont=null;D.beginHTML=FrameZoneWidget_beginHTML;D.endHTML=FrameZoneWidget_endHTML;D.oldResize=D.resize;D.resize=FrameZoneWidget_resize;return D}function FrameZoneWidget_resize(A,B){var D=this;var C=D.layer.display!="none";if(C&_moz&&!_saf){D.setDisplay(false)}D.oldResize(A,B);if(C&_moz&&!_saf){D.setDisplay(true)}}function FrameZoneWidget_beginHTML(){var A=this;return'<table width="100%" style="'+sty("width",A.w)+sty("height",A.h)+'" id="'+A.id+'" cellspacing="0" cellpadding="4" border="0"><tbody><tr><td valign="top" class="dlgFrame" id="frame_cont_'+A.id+'" style="padding:5px">'}function FrameZoneWidget_endHTML(){var A=this;return"</td></tr></tbody></table>"}function arrayAdd(E,G,D,B){var F=E[G],A=F.length;if((B==null)||(typeof B!="number")){B=-1}if((B<0)||(B>A)){B=A}if(B!=A){var C=F.slice(B);F.length=B+1;F[B]=D;F=F.concat(C)}else{F[B]=D}E[G]=F;return B}function arrayRemove(D,F,A){var E=D[F],C=E.length-1;if(A==null){E.length=0;D[F]=E;return -1}if((A<0)||(A>C)){return -1}if(A==C){E.length=C}else{var B=E.slice(A+1);E.length=A;E=E.concat(B)}D[F]=E;return A}function getFrame(name,par){if(par==null){par=self}var frames=par.frames,w=eval("frames."+name);if(w==null){return w}var l=frames.length;for(var i=0;i<l;i++){w=frames[i];try{if(w.name==name){return w}}catch(exc){}}return null}function frameGetUrl(A){return A.location.href}function frameReload(A){var B=A.location;B.replace(B.href)}function setTopFrameset(){_curWin._topfs="topfs"}function getTopFrameset(A){if(A==null){A=self}if(A._topfs=="topfs"){return A}else{if(A!=top){return getTopFrameset(A.parent)}else{return null}}}function convStr(D,A,C){D=""+D;var B=D.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;");if(A){B=B.replace(/ /g,"&nbsp;")}if(C){B=B.replace(/\n/g,"<br>")}return B}function escapeCR(B){B=""+B;var A=B.replace(/\r/g,"").replace(/\n/g,"\\n");return A}function addDblClickCB(B,A){if(B.addEventListener&&!_saf){B.addEventListener("dblclick",A,false)}else{B.ondblclick=A}}function img(E,A,C,F,B,D){B=(B?B:"");if(D==null){D=""}return"<img"+attr("width",A)+attr("height",C)+attr("src",E)+attr("alt",D)+attr("align",F)+' border="0" hspace="0" vspace="0" '+(B?B:"")+">"}function imgOffset(A,G,D,J,I,B,F,C,H,E){return img(_skin+"../transp.gif",G,D,E,(F?F:"")+" "+attr("id",B)+' style="float:left;'+backImgOffset(A,J,I)+(H?H:"")+'"',C)}function simpleImgOffset(A,G,D,J,I,B,F,C,H,E){if(_ie){if(J==null){J=0}if(I==null){I=0}return"<div "+(F?F:"")+" "+attr("id",B)+' style="position:relative;padding:0px;width:'+G+"px;height:"+D+"px;overflow:hidden;"+(H?H:"")+'">'+img(A,null,null,(E?E:"top"),'style="margin:0px;position:relative;top:'+(-I)+"px;left:"+(-J)+'px" tabIndex="-1"',C)+"</div>"}else{return imgOffset(A,G,D,J,I,B,F,C,H,E)}}function changeSimpleOffset(E,B,A,D,F){if(_ie){E=E.childNodes[0];var C=E.style;if((D!=null)&&(D!=E.src)){E.src=D}if(B!=null){C.left=""+(-B)+"px"}if(A!=null){C.top=""+(-A)+"px"}if(F!=null){E.title=F;E.alt=F}}else{changeOffset(E,B,A,D,F)}}function backImgOffset(C,B,A){return"background-image:url('"+C+"');background-position:"+(-B)+"px "+(-A)+"px;"}function changeOffset(E,B,A,D,F){var C=E.style;if(C){if((B!=null)&&(A!=null)){C.backgroundPosition=""+(-B)+"px "+(-A)+"px"}if(D){C.backgroundImage="url('"+D+"')"}}if(F){E.title=F}}function includeCSS(B,C){if(typeof (_skin)=="string"&&_skin!=""){var A="";if(C){A=_skin+"../"+B}else{A=_skin+B}A+=".css";_curDoc.write('<link rel="stylesheet" type="text/css" href="'+A+'">')}}function getLayer(A){return _curDoc.getElementById(A)}function setLayerTransp(A,B){if(_ie){A.style.filter=(B==null)?"":"progid:DXImageTransform.Microsoft.Alpha( style=0,opacity="+B+")"}else{A.style.MozOpacity=(B==null)?1:B/100}}function getPos(B,A){A=A?A:null;for(var D=0,C=0;(B!=null)&&(B!=A);D+=B.offsetLeft,C+=B.offsetTop,B=B.offsetParent){}return{x:D,y:C}}function getPos2(B,A){var A=A?A:null;var D=0;var C=0;while(B.parentNode||B.offsetParent){if(B.offsetParent){D+=B.offsetLeft;C+=B.offsetTop;B=B.offsetParent}else{if(B.parentNode){if(B.style){if(B.style.left){D+=B.style.left}if(B.style.top){C+=B.style.top}}B=B.parentNode}else{break}}}if(A){relToCord=getPos2(A);D-=relToCord.x;C-=relToCord.y}return{x:D,y:C}}function getPosScrolled(C,B){B=B?B:null;if(_ie){for(var E=0,D=0;(C!=null)&&(C!=B);E+=C.offsetLeft-C.scrollLeft,D+=C.offsetTop-C.scrollTop,C=C.offsetParent){}}else{var A=C;for(var E=0,D=0;(C!=null)&&(C!=B);E+=C.offsetLeft,D+=C.offsetTop,C=C.offsetParent){}for(C=A;(C!=null)&&(C!=B);C=C.parentNode){if(C.scrollLeft!=null){E-=C.scrollLeft;D-=C.scrollTop}}}E+=getScrollX();D+=getScrollY();return{x:E,y:D}}function getWidget(B){if(B==null){return null}var A=B._widget;if(A!=null){return _widgets[A]}else{return getWidget(B.parentNode)}}function getWidgetFromID(B){if(B==null){return null}var A=getLayer(B);return getWidget(A)}function attr(A,B){return(B!=null?" "+A+'="'+B+'" ':"")}function sty(A,B){return(B!=null?A+":"+B+";":"")}function getSep(B,A){if(B==null){B=0}var C=B>0?'<td width="'+B+'">'+getSpace(B,1)+"</td>":"";return'<table style="margin-top:5px;margin-bottom:5px;" width="100%" cellspacing="0" cellpadding="0"><tr>'+C+'<td background="'+_skin+"sep"+(A?"_solid":"")+'.gif" class="smalltxt"><img alt="" src="'+_skin+'../transp.gif" width="10" height="2"></td>'+C+"</tr></table>"}function writeSep(B,A){_curDoc.write(getSep(B,A))}function getSpace(A,B){return'<table height="'+B+'" border="0" cellspacing="0" cellpadding="0"><tr><td>'+img(_skin+"../transp.gif",A,B)+"</td></tr></table>"}function writeSpace(A,B){_curDoc.write(getSpace(A,B))}function documentWidth(B){var B=B?B:_curWin;var A=Math.max(document.body.clientWidth,document.documentElement.clientWidth);A=Math.max(A,document.body.scrollWidth);return A}function documentHeight(B){var B=B?B:_curWin;var A=Math.max(document.body.clientHeight,document.documentElement.clientHeight);A=Math.max(A,document.body.scrollHeight);return A}function winWidth(B){var A;var B=B?B:_curWin;if(_ie){if(_isQuirksMode){A=B.document.body.clientWidth}else{A=B.document.documentElement.clientWidth}}else{A=B.innerWidth}return A}function winHeight(B){var B=B?B:_curWin;var A;if(_ie){if(_isQuirksMode){A=document.body.clientHeight}else{A=document.documentElement.clientHeight}}else{A=B.innerHeight}return A}function getScrollX(A){var B=0;var A=A?A:_curWin;if(typeof (A.scrollX)=="number"){B=A.scrollX}else{B=Math.max(A.document.body.scrollLeft,A.document.documentElement.scrollLeft)}return B}function getScrollY(B){var A=0;var B=B?B:_curWin;if(typeof (B.scrollY)=="number"){A=window.scrollY}else{A=Math.max(B.document.body.scrollTop,B.document.documentElement.scrollTop)}return A}function winScrollTo(A,C,B){B=B?B:_curWin;B.scrollTo(A,C)}function eventGetKey(B,A){A=A?A:_curWin;return _ie?A.event.keyCode:B.keyCode}function eventGetX(A){return _ie?_curWin.event.clientX:A.clientX?A.clientX:A.pageX}function eventGetY(A){return _ie?_curWin.event.clientY:A.clientY?A.clientY:A.pageY}function xpos(D,C,B,A){if((A==null)||(!_ie)){A=1}return((C.clientX/A)-getPos(D).x)+getScrollX()}function ypos(D,C,B,A){if((A==null)||(!_ie)){A=1}return((C.clientY/A)-getPos(D).y)+(_ie?B.body.scrollTop:0)}function absxpos(B,A){if((A==null)||(!_ie)){return B.clientX}else{return B.clientX/A}}function absypos(B,A){if((A==null)||(!_ie)){return B.clientY}else{return B.clientY/A}}function eventCancelBubble(C,B){B=B?B:_curWin;var A=_ie?B.event:C;if(A){A.cancelBubble=true;if(A.stopPropagation){A.stopPropagation()}}}function isHidden(B){if((B==null)||(B.tagName=="BODY")){return false}var A=B.style;if((A==null)||(A.visibility==_hide)||(A.display=="none")){return true}return isHidden(B.parentNode)}function opt(C,A,B){return'<option value="'+C+'" '+(B?"selected":"")+">"+convStr(""+A)+"</option>"}function lnk(C,D,A,F,B,E){if(D==null){D="return false"}B=B?B:"";return"<a"+attr("class",A)+attr("id",F)+attr("href","javascript:void(0)")+attr("onclick",D)+attr("ondblclick",E)+B+">"+C+"</a>"}_oldErrHandler=null;function localErrHandler(){return true}function canScanFrames(A){var B=true,D=null;if(_moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{D=A.document;B=false}catch(C){}if(_moz){window.onerror=_oldErrHandler}return(!B&&(D!=null))}function getBGIframe(A){return'<iframe id="'+A+'" name="'+A+'" style="display:none;left:0px;position:absolute;top:0px" src="'+_skin+'../../empty.html" frameBorder="0" scrolling="no"></iframe>'}function getDynamicBGIFrameLayer(){var A=false;if(_curWin.BGIFramePool){BGIFrames=_curWin.BGIFramePool.split(",");BGIFCount=BGIFrames.length;for(var B=0;B<BGIFCount;B++){if(BGIFrames[B]!="1"){A=true;break}}}else{B=0;BGIFrames=new Array}BGIFrames[B]="1";_curWin.BGIFramePool=BGIFrames.join(",");if(!A){targetApp(getBGIframe("BGIFramePool_"+B))}return getLayer("BGIFramePool_"+B)}function holdBGIFrame(B){var A=getLayer(B);if(A){A.style.display=""}id=parseInt(B.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=1;_curWin.BGIFramePool=BGIFrames.join(",")}function releaseBGIFrame(B){var A=getLayer(B);if(A){A.style.display="none"}id=parseInt(B.split("_")[1]);BGIFrames=_curWin.BGIFramePool.split(",");BGIFrames[id]=0;_curWin.BGIFramePool=BGIFrames.join(",")}function append(D,B,F){if(_ie){D.insertAdjacentHTML("BeforeEnd",B)}else{var A=F?F:_curDoc;var C=A.createRange();C.setStartBefore(D);var E=C.createContextualFragment(B);D.appendChild(E)}}function append2(D,B,F){if(_ie){D.insertAdjacentHTML("afterBegin",B)}else{var A=F?F:_curDoc;var C=A.createRange();C.setStartBefore(D);var E=C.createContextualFragment(B);D.appendChild(E)}}function insBefore(D,B,F){if(_ie){D.insertAdjacentHTML("BeforeBegin",B)}else{var A=F?F:_curDoc;var C=_curDoc.createRange();C.setEndBefore(D);var E=C.createContextualFragment(B);D.parentNode.insertBefore(E,D)}}function insBefore2(D,B,F){if(_ie){D.insertAdjacentHTML("BeforeBegin",B)}else{var A=F?F:_curDoc;var C=_curDoc.createRange();C.setStartBefore(D);var E=C.createContextualFragment(B);D.parentNode.insertBefore(E,D)}}function targetApp(A){append(_curDoc.body,A)}function preloadImg(B){var A=_preloadArr[_preloadArr.length]=new Image;A.src=B}_staticUnicBlockWhileWaitWidgetID="staticUnicBlockWhileWaitWidgetID";function hideBlockWhileWaitWidget(){var A=getLayer(_staticUnicBlockWhileWaitWidgetID);if(A){A.style.display="none"}}function newBlockWhileWaitWidget(A){if(window._BlockWhileWaitWidget!=null){return window._BlockWhileWaitWidget}var B=newWidget(_staticUnicBlockWhileWaitWidgetID);B.getPrivateHTML=BlockWhileWaitWidget_getPrivateHTML;B.init=BlockWhileWaitWidget_init;B.show=BlockWhileWaitWidget_show;window._BlockWhileWaitWidget=B;return B}function BlockWhileWaitWidget_init(){}function BlockWhileWaitWidget_getPrivateHTML(){return'<div id="'+this.id+'" onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0"  style="background-image:url('+_skin+'../transp.gif);z-index:5000;cursor:wait;position:absolute;top:0px;left:0px;width:100%;height:100%"></div>'}function BlockWhileWaitWidget_show(A){var B=this;if(B.layer==null){B.layer=getLayer(B.id);if(B.layer==null){targetApp(B.getPrivateHTML());B.layer=getLayer(B.id);B.css=B.layer.style}else{B.css=B.layer.style}}B.setDisplay(A)}function isTextInput(A){var B=_ie?A.srcElement:A.target;var C=false;if(B.tagName=="TEXTAREA"){C=true}if((B.tagName=="INPUT")&&((B.type.toLowerCase()=="text")||(B.type.toLowerCase()=="password"))){C=true}return C}function isTextArea(A){var B=_ie?A.srcElement:A.target;if(B.tagName=="TEXTAREA"){return true}else{return false}}function LZ(A){return(A<0||A>9?"":"0")+A}if(bobj.crv.config.isDebug){localErrHandler=null}initDom(bobj.crvUri("../dhtmllib/images/")+bobj.crv.config.skin+"/","",bobj.crv.config.lang);styleSheet();if(window._DHTML_LIB_DIALOG_JS_LOADED==null){_DHTML_LIB_DIALOG_JS_LOADED=true;DialogBoxWidget_modals=new Array;DialogBoxWidget_instances=new Array;DialogBoxWidget_current=null;_promptDlgInfo=0;_promptDlgWarning=1;_promptDlgCritical=2;_dlgTitleLBorderToTxt=20;_dlgTitleHeight=25;_dlgTitleMarginBottom=4;_dlgTitleRBorderToClose=10;_dlgTitleCloseBtnImgFile="dialogtitle.gif";_dlgTitleCloseBtnW=11;_dlgTitleCloseBtnH=10;_dlgTitleCloseBtnDy=26;_dlgTitleCloseBtnHoverDy=37;_dlgBottomMargin=14}function newDialogBoxWidget(A,F,B,H,I,G,E,D){var C=newWidget(A);C.title=F;C.width=B;C.height=H;C.defaultCB=I;C.cancelCB=G;C.noCloseButton=E?E:false;C.isAlert=D;C.closeCB=null;C.resizeable=false;C.oldMouseDown=null;C.oldCurrent=null;C.modal=null;C.hiddenVis=new Array;C.lastLink=null;C.firstLink=null;C.titleLayer=null;C.defaultBtn=null;C.divLayer=null;C.oldInit=C.init;C.oldShow=C.show;C.init=DialogBoxWidget_init;C.setResize=DialogBoxWidget_setResize;C.beginHTML=DialogBoxWidget_beginHTML;C.endHTML=DialogBoxWidget_endHTML;C.show=DialogBoxWidget_Show;C.center=DialogBoxWidget_center;C.focus=DialogBoxWidget_focus;C.setTitle=DialogBoxWidget_setTitle;C.getContainerWidth=DialogBoxWidget_getContainerWidth;C.getContainerHeight=DialogBoxWidget_getContainerHeight;DialogBoxWidget_instances[A]=C;C.modal=newWidget("modal_"+A);C.placeIframe=DialogBoxWidget_placeIframe;C.oldResize=C.resize;C.resize=DialogBoxWidget_resize;C.attachDefaultButton=DialogBoxWidget_attachDefaultButton;C.unload=DialogBoxWidget_unload;C.close=DialogBoxWidget_close;C.setCloseCB=DialogBoxWidget_setCloseCB;C.setNoCloseButton=DialogBoxWidget_setNoCloseButton;if(!_ie){if(C.width!=null){C.width=Math.max(0,B+4)}if(C.height!=null){C.height=Math.max(0,H+4)}}return C}function DialogBoxWidget_setResize(A,C,D,B,F){var E=this;E.resizeable=true;E.resizeCB=A;E.minWidth=C?C:50;E.minHeight=D?D:50;E.noResizeW=B;E.noResizeH=F}function DialogBoxWidget_setTitle(B){var A=this;A.title=B;if(A.titleLayer==null){A.titleLayer=getLayer("titledialog_"+this.id)}A.titleLayer.innerHTML=convStr(B)}function DialogBoxWidget_setCloseIcon(A,B){changeOffset(A,0,(B==1?0:18))}function DialogBoxWidget_beginHTML(){with(this){var moveableCb=' onselectstart="return false" ondragstart="return false" onmousedown="'+_codeWinName+".DialogBoxWidget_down(event,'"+id+"',this,false);return false;\" ";var titleBG="background-image:url("+_skin+"dialogtitle.gif)";var mdl='<div onselectstart="return false" onmouseup="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+'\');" onmousedown="'+_codeWinName+'.eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_skin+'../transp.gif" id="modal_'+id+'" style="background-color:#888888;opacity:0.3;filter:alpha(opacity:30);position:absolute;top:0px;left:0px;width:1px;height:1px">'+(_ie?img(_skin+"../transp.gif","100%","100%",null):"")+"</div>";var btn="";if(_dtd4){btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px"><div id="dialogClose_'+id+'" class="dlgCloseBtn" title="'+_closeDialog+'"></div></td>'}else{btn='<td style="padding-right:'+_dlgTitleRBorderToClose+'px">'+simpleImgOffset(_skin+_dlgTitleCloseBtnImgFile,_dlgTitleCloseBtnW,_dlgTitleCloseBtnH,0,_dlgTitleCloseBtnDy,"dialogClose_"+id,null,_closeDialog)+"</td>"}var closeBtn='<td class="dlgCloseArea" align="left" valign="middle"><table border="0" cellspacing="0" cellpadding="0"><tbody><tr style="height:'+_dlgTitleHeight+'px">'+btn+"</tr></tbody></table></td>";var dlgtitle='<table style="height:'+_dlgTitleHeight+'" class="dlgTitle" width="100%"  border="0" cellspacing="0" cellpadding="0"><tr valign="top" style="height:'+_dlgTitleHeight+'px"><td '+moveableCb+' style="cursor:move;padding-left:'+_dlgTitleLBorderToTxt+'px;" width="100%" valign="middle" align="left"><nobr><span id="titledialog_'+id+'" tabIndex="0" class="titlezone">'+convStr(title)+"</span></nobr></td>"+closeBtn+"</tr></table>";var s="";s=mdl;var dims=sty("width",width?(""+width+"px"):null)+sty("height",height?(""+Math.max(0,height+(_moz?-2:0))+"px"):null);s+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="firstLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";s+='<table border="0" cellspacing="0" cellpadding="0" id="'+id+'" style="display:none;padding:0px;visibility:'+_hide+";position:absolute;top:-2000px;left:-2000px;"+dims+'" '+(isAlert?'role="alertdialog"':'role="dialog"')+">";s+="<tr><td "+(_moz?'style="'+dims+'" ':"")+'class="dialogbox" id="td_dialog_'+id+'" onresize="'+_codeWinName+".DialogBoxWidget_resizeIframeCB('"+id+'\',this)"  valign="top">';s+='<table class="dlgBox2" width="100%" border="0" cellspacing="0" cellpadding="0"><tbody>';s+='<tr><td height="'+_dlgTitleHeight+'" valign="top">'+dlgtitle+"</td></tr>";s+='<tr><td class="dlgBody" valign="top" id="div_dialog_'+id+'">';return s}}function DialogBoxWidget_endHTML(){var A="</td></tr>";A+='<tr><td style="height:'+_dlgBottomMargin+'px;"></td></tr>';A+="</tbody></table>";A+="</td></tr></table>";A+='<button style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="lastLink_'+this.id+'" onfocus="'+_codeWinName+".DialogBoxWidget_keepFocus('"+this.id+"');return false;\" ></button>";return A}function DialogBoxWidget_getContainerWidth(){var A=this;return A.width-(2+2)}function DialogBoxWidget_getContainerHeight(){var A=this;return A.height-(2+18+2+2+2)}function DialogBoxWidget_close(B){var A=DialogBoxWidget_instances[B];if(A){A.show(false);if(A.cancelCB!=null){A.cancelCB()}}}function DialogBoxWidget_setCloseCB(A){this.closeCB=A}function DialogBoxWidget_setNoCloseButton(A){if(this.noCloseButton!==A){this.noCloseButton=A;if(this.initialized()){this.closeButton.style.visibility=this.noCloseButton?_hide:_show}}}function DialogBoxWidget_resizeIframeCB(B,A){DialogBoxWidget_instances[B].placeIframe()}function DialogBoxWidget_placeIframe(){var B=this;if(B.iframe){var A=B.td_lyr;if(A==null){B.td_lyr=A=getLayer("td_dialog_"+B.id)}B.iframe.resize(A.offsetWidth,A.offsetHeight);B.iframe.move(B.layer.offsetLeft,B.layer.offsetTop)}}function DialogBoxWidget_resize(A,B){var C=this;C.oldResize(A,B);if(C.iframe){C.iframe.resize(A,B)}}function DialogBoxWidget_init(){if(this.layer!=null){return }var A=this;A.oldInit();A.modal.init();A.lastLink=newWidget("lastLink_"+A.id);A.firstLink=newWidget("firstLink_"+A.id);A.lastLink.init();A.firstLink.init();if(_saf){A.webKitFocusElem=getLayer("webKitFocusElem"+A.id)}A.closeButton=getLayer("dialogClose_"+A.id);A.closeButton.style.visibility=A.noCloseButton?_hide:_show;A.closeButton.onmouseover=DialogBoxWidget_moverCloseBtn;A.closeButton.onmouseout=DialogBoxWidget_moverCloseBtn;A.closeButton.onclick=function(){A.close(A.id)}}function DialogBoxWidget_moverCloseBtn(A){var A=getEvent(A);var B=(A&&A.type=="mouseover")?true:false;if(_dtd4){this.className=B?"dlgCloseBtnHover":"dlgCloseBtn"}else{changeOffset(this,0,B?_dlgTitleCloseBtnHoverDy:_dlgTitleCloseBtnDy)}}function DialogBoxWidget_attachDefaultButton(A){this.defaultBtn=A;this.defaultBtn.setDefaultButton()}_theLYR=null;_dlgResize=null;function DialogBoxWidget_down(e,id,obj,isResize){_dlgResize=isResize;var o=DialogBoxWidget_instances[id],lyr=o.layer,mod=o.modal.layer;lyr.onmousemove=mod.onmousemove=eval("_curWin."+_codeWinName+".DialogBoxWidget_move");lyr.onmouseup=mod.onmouseup=eval("_curWin."+_codeWinName+".DialogBoxWidget_up");lyr.dlgStartPosx=mod.dlgStartPosx=parseInt(lyr.style.left);lyr.dlgStartPosy=mod.dlgStartPosy=parseInt(lyr.style.top);lyr.dlgStartx=mod.dlgStartx=eventGetX(e);lyr.dlgStarty=mod.dlgStarty=eventGetY(e);lyr.dlgStartw=mod.dlgStartw=o.getWidth();lyr.dlgStarth=mod.dlgStarth=o.getHeight();lyr._widget=mod._widget=o.widx;_theLYR=lyr;eventCancelBubble(e);if(lyr.setCapture){lyr.setCapture(true)}}function DialogBoxWidget_move(C){var E=_theLYR,D=getWidget(E);if(_dlgResize){var G=Math.max(D.minWidth,E.dlgStartw+eventGetX(C)-E.dlgStartx);var B=Math.max(D.minHeight,E.dlgStarth+eventGetY(C)-E.dlgStarty);D.resize(D.noResizeW?null:G,D.noResizeH?null:B);if(D.firstTR){if(!D.noResizeW){D.firstTR.style.width=G-4}if(!D.noResizeH){D.secondTR.style.height=B-44}}if(D.resizeCB){D.resizeCB(G,B)}}else{var A=Math.max(0,E.dlgStartPosx-E.dlgStartx+eventGetX(C));var F=Math.max(0,E.dlgStartPosy-E.dlgStarty+eventGetY(C));D.iframe.move(A,F);D.move(A,F)}eventCancelBubble(C);return false}function DialogBoxWidget_up(C){var D=getWidget(_theLYR),A=D.layer,B=D.modal.layer;A.onmousemove=B.onmousemove=null;A.onmouseup=B.onmouseup=null;if(A.releaseCapture){A.releaseCapture()}_theLYR=null}function DialogBoxWidget_keypress(A){eventCancelBubble(A);var B=DialogBoxWidget_current;if(B!=null){switch(eventGetKey(A)){case 13:if(B.yes&&!B.no){if(B.defaultCB){B.defaultCB()}return false}if(isTextArea(_ie?_curWin.event:A)){return true}if(B.defaultBtn!=null&&!B.defaultBtn.isDisabled()){B.defaultBtn.executeCB();return false}break;case 27:if(!B.noCloseButton){B.show(false);hideBlockWhileWaitWidget();if(B.cancelCB!=null){B.cancelCB()}}return false;break;case 8:return isTextInput(_ie?_curWin.event:A);break}}}function DialogBoxWidgetResizeModals(H){var G=DialogBoxWidget_current&&DialogBoxWidget_current.isDisplayed();if(G){DialogBoxWidget_current.setDisplay(false);DialogBoxWidget_current.iframe.setDisplay(false)}var E=[];for(var D=0,A=DialogBoxWidget_modals.length;D<A;D++){E[D]=DialogBoxWidget_modals[D].display;DialogBoxWidget_modals[D].display="none"}var C=documentWidth()+"px";var B=documentHeight()+"px";if(G){DialogBoxWidget_current.setDisplay(true);DialogBoxWidget_current.iframe.setDisplay(true)}for(var D=0,A=DialogBoxWidget_modals.length;D<A;D++){var F=DialogBoxWidget_modals[D];F.display=E[D];F.width=C;F.height=B}}function DialogBoxWidget_center(){var H=this;var F={modalDisplay:H.modal.css.display,layerDisplay:H.css.display};H.modal.css.display="none";H.css.display="none";var B=getScrollY(),C=getScrollX();H.modal.css.display=F.modalDisplay;H.css.display="block";var A=H.layer.offsetHeight,E=H.layer.offsetWidth;H.css.display=F.layerDisplay;var G=(winHeight()-A)/2;G=(G<0)?0:G;var D=(winWidth()-E)/2;D=(D<0)?0:D;H.move(Math.max(0,C+D),Math.max(0,B+G));H.placeIframe()}function DialogBoxWidget_Show(sh){with(this){m_sty=modal.css;l_sty=css;if(sh){if(!this.iframe){this.iframe=newWidget(getDynamicBGIFrameLayer().id);this.iframe.init()}oldCurrent=DialogBoxWidget_current;DialogBoxWidget_current=this;if(_ie){layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");modal.layer.onkeydown=eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress");window.attachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.addEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.addEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}oldMouseDown=_curDoc.onmousedown;_curDoc.onmousedown=null;hideBlockWhileWaitWidget()}else{DialogBoxWidget_current=oldCurrent;oldCurrent=null;if(_ie){layer.onkeydown=null;modal.layer.onkeydown=null;window.detachEvent("onresize",eval("DialogBoxWidget_onWindowResize"))}else{_curDoc.removeEventListener("keydown",eval("_curWin."+_codeWinName+".DialogBoxWidget_keypress"),false);window.removeEventListener("resize",eval("DialogBoxWidget_onWindowResize"),false)}_curDoc.onmousedown=oldMouseDown}var sameState=(layer.isShown==sh);if(sameState){return }layer.isShown=sh;if(sh){if(_curWin.DialogBoxWidget_zindex==null){_curWin.DialogBoxWidget_zindex=1000}this.iframe.css.zIndex=_curWin.DialogBoxWidget_zindex++;m_sty.zIndex=_curWin.DialogBoxWidget_zindex++;l_sty.zIndex=_curWin.DialogBoxWidget_zindex++;DialogBoxWidget_modals[DialogBoxWidget_modals.length]=m_sty;m_sty.display="";l_sty.display="block";this.iframe.setDisplay(true);holdBGIFrame(this.iframe.id);DialogBoxWidgetResizeModals();this.height=layer.offsetHeight;this.width=layer.offsetWidth;if(_small&&height){if(divLayer==null){divLayer=getLayer("div_dialog_"+id)}if(divLayer){divLayer.style.overflow="auto";divLayer.style.height=(winHeight()<height)?(winHeight()-40):getContainerHeight();divLayer.style.width=(_moz?width+20:getContainerWidth())}resize(null,((winHeight()<height)?(winHeight()-10):null))}if(isHidden(layer)){this.center()}if(!_small&&this.resizeCB){this.resizeCB(width,height)}}else{var l=DialogBoxWidget_modals.length=Math.max(0,DialogBoxWidget_modals.length-1);m_sty.width="1px";m_sty.height="1px";m_sty.display="none";l_sty.display="none";if(this.iframe!=null){this.iframe.setDisplay(false);releaseBGIFrame(this.iframe.id)}}modal.show(sh);firstLink.show(sh);lastLink.show(sh);oldShow(sh);if(DialogBoxWidget_current!=null&&sh==true){DialogBoxWidget_current.focus()}if(!sh&&closeCB!=null){closeCB()}}}function DialogBoxWidget_onWindowResize(){DialogBoxWidgetResizeModals()}function DialogBoxWidget_unload(){if(this.iframe){releaseBGIFrame(this.iframe.id)}}function DialogBoxWidget_keepFocus(B){var A=DialogBoxWidget_instances[B];if(A){A.focus()}}function DialogBoxWidget_focus(){with(this){if(titleLayer==null){titleLayer=getLayer("titledialog_"+id)}if(_saf&&webKitFocusElem&&webKitFocusElem.focus){webKitFocusElem.focus()}else{if(titleLayer.focus){titleLayer.focus()}}}}function newPromptDialog(A,H,J,G,D,I,K,C,F,E){var B=newDialogBoxWidget(A,H,300,null,PromptDialog_defaultCB,PromptDialog_cancelCB,F,E);B.text=J;B.getHTML=PromptDialog_getHTML;B.yes=G?newButtonWidget(A+"_yesBtn",G,'PromptDialog_yesCB("'+B.id+'")',70):null;B.no=D?newButtonWidget(A+"_noBtn",D,'PromptDialog_noCB("'+B.id+'")',70):null;B.yesCB=K;B.noCB=C;B.promptType=I;B.txtLayer=null;B.imgLayer=null;B.setPromptType=PromptDialog_setPromptType;B.setText=PromptDialog_setText;if(B.yes){B.attachDefaultButton(B.yes)}else{if(B.no){B.attachDefaultButton(B.no)}}return B}function PromptDialog_getimgPath(B){var A=_skin;switch(B){case _promptDlgInfo:A+="information_icon.gif";break;case _promptDlgWarning:A+="warning_icon.gif";break;default:A+="critical_icon.gif";break}return A}function PromptDialog_getimgAlt(B){var A="";return A}function PromptDialog_setPromptType(B){var A=this;if(A.imgLayer==null){A.imgLayer=getLayer("dlg_img_"+A.id)}A.imgLayer.src=PromptDialog_getimgPath(B);A.imgLayer.alt=PromptDialog_getimgAlt(B)}function PromptDialog_setText(B){var A=this;A.text=B;if(A.txtLayer==null){A.txtLayer=getLayer("dlg_txt_"+A.id)}A.txtLayer.innerHTML='<div tabindex="0">'+convStr(B,false,true)+"</div>"}function PromptDialog_getHTML(){var C=this;var B=PromptDialog_getimgPath(C.promptType);var A=PromptDialog_getimgAlt(C.promptType);return C.beginHTML()+'<table class="dialogzone" width="290" cellpadding="0" cellspacing="5" border="0"><tr><td><table class="dialogzone" cellpadding="5" cellspacing="0" border="0"><tr><td align="right" width="32" >'+img(B,32,32,null,'id="dlg_img_'+C.id+'"',A)+'</td><td></td><td id="dlg_txt_'+C.id+'" align="left" tabindex="0">'+convStr(C.text,false,true)+"</td></tr></table></td></tr><tr><td>"+getSep()+'</td></tr><tr><td align="right"><table cellpadding="5" cellspacing="0" border="0"><tr>'+(C.yes?"<td>"+C.yes.getHTML()+"</td>":"")+(C.no?"<td>"+C.no.getHTML()+"</td>":"")+"</tr></table></td></tr></table>"+C.endHTML()}function PromptDialog_defaultCB(){var o=this;if(o.yesCB){if(typeof o.yesCB!="string"){o.yesCB()}else{eval(o.yesCB)}}this.show(false)}function PromptDialog_cancelCB(){var o=this;if(o.noCB){if(typeof o.noCB!="string"){o.noCB()}else{eval(o.noCB)}}this.show(false)}function PromptDialog_yesCB(A){DialogBoxWidget_instances[A].defaultCB()}function PromptDialog_noCB(A){DialogBoxWidget_instances[A].cancelCB()}function newWaitDialogBoxWidget(A,I,E,G,C,H,K,J,F){var D=250;var L=150;if(I<D){I=D}if(E<L){E=L}var B=newDialogBoxWidget(A,G,I,null,null,WaitDialogBoxWidget_cancelCB,F,true);B.pad=5;B.frZone=newFrameZoneWidget(A+"_frZone",null,null);B.showLabel=(K!=null)?K:false;B.showCancel=(C!=null)?C:false;B.label=newWidget(A+"_label");B.label.text=J;if(B.showCancel){B.cancelButton=newButtonWidget(A+"_cancelButton",_cancelButtonLab,CancelButton_cancelCB);B.cancelButton.par=B}else{B.cancelButton={};B.cancelButton.init=function(){};B.cancelButton.setDisplay=function(M){};B.cancelButton.setDisabled=function(M){};B.cancelButton.getHTML=function(){return""}}B.cancelCB=H;B.oldDialogBoxInit=B.init;B.init=WaitDialogBoxWidget_init;B.getHTML=WaitDialogBoxWidget_getHTML;B.setShowCancel=WaitDialogBoxWidget_setShowCancel;B.setShowLabel=WaitDialogBoxWidget_setShowLabel;return B}function WaitDialogBoxWidget_init(){var A=this;A.oldDialogBoxInit();A.frZone.init();A.label.init();A.label.setDisplay(A.showLabel);A.cancelButton.init();A.cancelButton.setDisplay(A.showCancel)}function WaitDialogBoxWidget_getHTML(){var B=this,A="";A+=B.beginHTML();A+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>';A+='<tr><td align="center" valign="top">'+B.frZone.beginHTML();A+='<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody><tr><td align="center" style="padding-top:5px;">'+img(_skin+"wait01.gif",200,40)+'</td></tr><tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;"><div id="'+B.label.id+'" class="iconText" style="wordWrap:break_word;text-align:center;">'+convStr(B.label.text,false,true)+"</div></td></tr></tbody></table>";A+=B.frZone.endHTML()+"</td></tr>";A+='<tr><td align="right" valign="middle" style="padding-top:5px;padding-right:9px">'+B.cancelButton.getHTML()+"</td></tr>";A+="</tbody></table>";A+=B.endHTML();return A}function WaitDialog_FrameZoneWidget_beginHTML(){var A=this;return'<table class="waitdialogzone" style="'+sty("width",A.w)+sty("height",A.h)+'" id="'+A.id+'" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dialogzone" id="frame_cont_'+A.id+'">'}function WaitDialog_FrameZoneWidget_endHTML(){var A=this;return"</td></tr></tbody></table>"}function WaitDialogBoxWidget_setShowCancel(A,C){var B=this;B.showCancel=A;B.cancelButton.setDisabled(false);B.cancelButton.setDisplay(A);B.cancelCB=C}function WaitDialogBoxWidget_setShowLabel(A,C){var B=this;B.showLabel=A;B.label.text=C;B.label.setHTML(C);B.label.setDisplay(A)}function WaitDialogBoxWidget_cancelCB(){var A=this;if(A.cancelCB!=null){A.cancelCB();A.cancelButton.setDisabled(true)}}function CancelButton_cancelCB(){var A=this;if(A.par.cancelCB!=null){A.par.cancelCB();A.par.cancelButton.setDisabled(true)}}if(typeof bobj=="undefined"){bobj={}}if(typeof bobj.constants=="undefined"){bobj.constants={modalLayerIndex:1000}}bobj.uniqueId=function(){return"bobjid_"+(++bobj.uniqueId._count)};if(typeof bobj.uniqueId._count=="undefined"){bobj.uniqueId._count=new Date().getTime()}bobj.updateIf=function(G,C,E){if(C===null){C={}}for(var D=1,A=arguments.length;D<A;D++){var F=arguments[D];if(typeof (F)!="undefined"&&F!==null){for(var B in F){if(G(C,E,B)){C[B]=F[B]}}}}return C};bobj.fillIn=function(A,B){var C=function(E,F,D){return(typeof (E[D])=="undefined")};bobj.updateIf(C,A,B)};bobj.isObject=function(A){return(A&&typeof A=="object")};bobj.isArray=function(B){if(bobj.isObject(B)){try{return B.constructor==Array}catch(A){return false}}return false};bobj.isString=function(A){return(typeof (A)=="string")};bobj.isNumber=function(A){return typeof (A)=="number"&&isFinite(A)};bobj.isBoolean=function(A){return typeof A=="boolean"};bobj.isFunction=function(A){return typeof (A)=="function"};bobj.isBorderBoxModel=function(){if(typeof bobj.isBorderBoxModel._cachedValue=="undefined"){if(document.body){var A=document.createElement("div");A.style.width="10px";A.style.padding="1px";A.style.position="absolute";A.style.visibility="hidden";document.body.appendChild(A);bobj.isBorderBoxModel._cachedValue=(A.offsetWidth==10);document.body.removeChild(A)}else{return _ie&&bobj.isQuirksMode()}}return bobj.isBorderBoxModel._cachedValue};bobj.isQuirksMode=function(){return(document.compatMode!="CSS1Compat")};bobj.setVisualStyle=function(B,C){if(B===null||C===null){return }var A=B.style;if(C.className){B.className=C.className}MochiKit.Iter.forEach(["background","borderWidth","borderStyle","borderColor","fontFamily","fontStyle","fontSize","fontWeight","textDecoration","color","width","height","left","top"],function(D){if(C[D]){A[D]=C[D]}})};bobj.setOuterSize=function(E,B,D,C){var G=null;var A=E.style;if(A.display=="none"){G={visibility:A.visibility,position:A.position,display:"none"};A.visibility="hidden";A.position="absolute";A.display=""}function F(H){var I=MochiKit.DOM.getStyle(E,H);if(bobj.isString(I)&&I.substring(I.length-2=="px")){return(parseInt(I,10)||0)}return 0}if(bobj.isNumber(B)){if(!bobj.isBorderBoxModel()){B-=F("border-left-width");B-=F("border-right-width");B-=F("padding-left");B-=F("padding-right");if(C){B-=F("margin-left");B-=F("margin-right")}}A.width=Math.max(0,B)+"px"}if(bobj.isNumber(D)){if(!bobj.isBorderBoxModel()){if(C){D-=F("margin-top");D-=F("margin-bottom")}D-=F("border-top-width");D-=F("border-bottom-width");D-=F("padding-top");D-=F("padding-bottom")}A.height=Math.max(0,D)+"px"}if(G){A.display=G.display;A.position=G.position;A.visibility=G.visibility}};bobj.getContainer=function(A){if(A&&A.layer){return A.layer.parentNode}return null};bobj.checkParent=function(D,B){var A=false;if(D&&B){B=B.toUpperCase();var C=D.parentNode;while(C){if(C.tagName==B){A=true;break}C=C.parentNode}}return A};bobj.slice=function(A,F,C){if(bobj.isArray(A)){return A.slice(F,C)}else{if(MochiKit.Base.isArrayLike(A)){var B=[];var E=A.length;if(bobj.isNumber(C)&&C<E){E=C}F=Math.max(F,0);for(var D=F;D<E;++D){B.push(A[D])}return B}}return null};bobj.extractRange=function(D,G,A){if(D&&bobj.isNumber(G)){if(!bobj.isNumber(A)||A>D.length){A=D.length}G=Math.max(0,G);if(G<A){var C=0,F=G;var B=A,E=D.length;if(D.substring){return(D.substring(C,F)+D.substring(B,E))}else{return bobj.slice(D,C,F).concat(bobj.slice(D,B,E))}}}return D};bobj.unitValue=function(B,A){if(bobj.isNumber(B)){return B+(A||"px")}return B};bobj.evalInWindow=function(expression){if(window.execScript){return window.execScript(expression)}else{return MochiKit.Base.bind(eval,window,expression).call()}};bobj.loadJSResourceAndExecCallBack=function(D,E){if(!D||!E){return }if(!D.isLoaded){var B=function(G,H,F){G.isLoaded=true;bobj.evalInWindow(F.responseText);H.apply()};var C=MochiKit.Async.getXMLHttpRequest();C.open("GET",bobj.crvUri(D.path),true);C.setRequestHeader("Accept","application/x-javascript, text/javascript");var A=MochiKit.Async.sendXMLHttpRequest(C);A.addCallback(MochiKit.Base.bind(B,this,D,E))}else{setTimeout(function(){E.apply()},0)}};bobj.trimLeft=function(A){A=A||"";return A.replace(/^\s+/g,"")};bobj.trimRight=function(A){A=A||"";return A.replace(/\s+$/g,"")};bobj.trim=function(A){return bobj.trimLeft(bobj.trimRight(A))};bobj.equals=function(B,A){if(typeof (B)!=typeof (A)){return false}if(bobj.isObject(B)){var D=true;for(var C in B){D=D&&bobj.equals(B[C],A[C])}return D}else{return B==A}};bobj.includeLink=function(B){var C=document.getElementsByTagName("head")[0];var A=document.body;var D=document.createElement("link");D.setAttribute("rel","stylesheet");D.setAttribute("type","text/css");D.setAttribute("href",B);if(C){C.appendChild(D)}else{if(A){A.appendChild(D)}}};bobj.includeCSSLinksAndExecuteCallback=function(C,E){if(C==null||C.length<1){E.apply();return }var B=function(){var F=arguments.callee;var G=F.callback;F.hrefCount--;if(F.hrefCount==0){G.apply()}};B.hrefCount=C.length;B.callback=E;for(var D=0,A=C.length;D<A;D++){bobj.includeCSSLinkAndExecuteCallback(C[D],B)}};bobj.includeCSSLinkAndExecuteCallback=function(A,H){var D=encodeURIComponent(A);if(getLayer(D)){H.apply();return }var F=function(L,K,J){bobj.addStyleSheet(J.responseText,K);L.apply()};var C=function(J){J.apply()};var G=MochiKit.Async.getXMLHttpRequest();G.open("GET",A,true);G.setRequestHeader("Accept","text/css");var I=MochiKit.Async.sendXMLHttpRequest(G);var B=MochiKit.Base.bind(F,this,H,D);var E=MochiKit.Base.bind(C,this,H);I.addCallbacks(B,E)};bobj.addStyleSheet=function(C,E){var D=document.createElement("style");D.setAttribute("type","text/css");if(E){D.setAttribute("id",E)}if(D.styleSheet){D.styleSheet.cssText=C}else{D.appendChild(document.createTextNode(C))}var B=document.getElementsByTagName("head");var A=document.getElementsByTagName("body");if(B&&B[0]){B[0].appendChild(D)}else{if(A&&A[0]){A[0].appendChild(D)}}};bobj.removeAllChildElements=function(A){if(A){while(A.lastChild){A.removeChild(A.lastChild)}}};bobj.getValueHashCode=function(C,A){var B=bobj.crv.params.DataTypes;switch(C){case B.BOOLEAN:case B.CURRENCY:case B.NUMBER:case B.STRING:return""+A;case B.TIME:return""+A.h+","+A.min+","+A.s+","+A.ms;case B.DATE:return""+A.y+","+A.m+","+A.d;case B.DATE_TIME:return""+A.y+","+A.m+","+A.d+","+A.h+","+A.min+","+A.s+","+A.ms}};bobj.getElementByIdOrName=function(B){if(!B){return null}var C=document.getElementById(B);if(C){return C}var A=document.getElementsByName(B);if(A&&A.length>0){return A[0]}return null};bobj.getRect=function(D,B,A,C){return"rect("+D+"px, "+B+"px,"+A+"px,"+C+"px)"};bobj.getBodyScrollDimension=function(){var A=0;var C=0;var B=document.getElementsByTagName("Body");if(B&&B[0]){A=B[0].scrollWidth;C=B[0].scrollHeight}return{w:A,h:C}};bobj.disableTabbingKey=function(B,A){if(B){B.tabIndex=A?-1:0}};bobj.getStringWidth=function(B,A,E){if(document.body){var D=document.createElement("span");D.appendChild(document.createTextNode(B));D.style.position="absolute";D.style.visibility="hidden";if(A){D.style.fontFamily=A}if(E){D.style.fontSize=E}document.body.appendChild(D);var C=D.offsetWidth;document.body.removeChild(D);return C}return 0};bobj.deleteWidget=function(B){if(B&&B.widx){if(B.layer){B.layer.click=null;B.layer.onmouseup=null;B.layer.onmousedown=null;B.layer.onmouseover=null;B.layer.onmousemove=null;B.layer.onmouseout=null;B.layer.onchange=null;B.layer.onfocus=null;B.layer.onkeydown=null;B.layer.onkeyup=null;B.layer.onkeypress=null;var A=B.layer.parentNode;if(A){A.removeChild(B.layer)}delete B.layer}delete B.css;delete _widgets[B.widx];_widgets[B.widx]=null;delete B}};bobj.cloneArray=function(A){return A.slice()};bobj.bindFunctionToObject=function(A,B){return function(){return A.apply(B,arguments)}};bobj.extendClass=function(A,D,B){MochiKit.Base.update(A,D);A.superClass={};for(var C in B){A.superClass[C]=bobj.bindFunctionToObject(B[C],A)}};bobj.displayElementWithAnimation=function(A){if(A!=null){MochiKit.DOM.setOpacity(A,0);MochiKit.Style.setDisplayForElement("block",A);new MochiKit.Visual.appear(A,{duration:0.5})}};bobj.getHiddenElementDimensions=function(D){var C={w:0,h:0};if(D){var A=document.body;var E=D.cloneNode(true);var B=E.style;B.display="";B.visibility="hidden";B.width="";B.height="";B.position="absolute";B.left="-1000px";B.top="-1000px";A.appendChild(E);C={w:E.offsetWidth,h:E.offsetHeight};A.removeChild(E)}return C};bobj.hasPDFReaderWithJSFunctionality=function(){if(window.ActiveXObject){try{var E=new ActiveXObject("AcroPDF.PDF.1");if(E){return true}}catch(D){}}else{if(navigator.plugins){var B=navigator.plugins;for(var C=0,A=B.length;C<A;C++){if(B[C].description.indexOf("Adobe PDF Plug-In")!=-1){return true}}}}return false};if(typeof bobj.crv.PrintUI=="undefined"){bobj.crv.PrintUI={}}if(typeof bobj.crv.ExportUI=="undefined"){bobj.crv.ExportUI={}}if(typeof bobj.crv.ErrorDialog=="undefined"){bobj.crv.ErrorDialog={}}if(typeof bobj.crv.ReportProcessingUI=="undefined"){bobj.crv.ReportProcessingUI={}}bobj.crv.newPrintUI=function(C){if(!C.id){C=MochiKit.Base.update({id:bobj.uniqueId()},C)}var D=C.submitBtnLabel;if(!D){D=L_bobj_crv_submitBtnLbl}var B=C.infoTitle;if(!B){B=L_bobj_crv_PrintInfoTitle}var A=C.dialogTitle;if(!A){if(C.isActxPrinting){A=L_bobj_crv_ActiveXPrintDialogTitle}else{A=L_bobj_crv_PDFPrintDialogTitle}}var F=C.infoMsg;if(!F){F=L_bobj_crv_PrintInfo1;F+="\n";F+=L_bobj_crv_PrintInfo2}var E=newDialogBoxWidget(C.id+"_dialog",A,300,100,null,bobj.crv.PrintUI._cancel,false);E.infoMsg=F;E.infoTitle=B;E.actxId=E.id+"_actx";E.actxContainerId=E.id+"_actxdiv";E._processingPrinting=false;E._initOld=E.init;E._showOld=E.show;if(!C.isActxPrinting){E._fromBox=newIntFieldWidget(E.id+"_fromBox",null,null,null,null,true,"",50);E._fromBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;E._toBox=newIntFieldWidget(E.id+"_toBox",null,null,null,null,true,"",50);E._toBox.setDisabled=bobj.crv.PrintUI.disabledTextFieldWidget;E._submitBtn=newButtonWidget(E.id+"_submitBtn",D,MochiKit.Base.bind(bobj.crv.PrintUI._submitBtnCB,E));E._submitBtn.setDelayCallback(false);E._allRadio=newRadioWidget(E.id+"_allRadio",E.id+"_grp",L_bobj_crv_PrintAllLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,E,true));E._allRadio.layerClass="dlgContent";E._rangeRadio=newRadioWidget(E.id+"_rangeRadio",E.id+"_grp",L_bobj_crv_PrintPagesLbl,MochiKit.Base.bind(bobj.crv.PrintUI.disabledPageRange,E,false));E._rangeRadio.layerClass="dlgContent"}E.widgetType="PrintUI";bobj.fillIn(E,C);MochiKit.Base.update(E,bobj.crv.PrintUI);return E};bobj.crv.PrintUI.disabledTextFieldWidget=function(A){TextFieldWidget_setDisabled.call(this,A);if(A){MochiKit.DOM.addElementClass(this.layer,"textDisabled")}else{MochiKit.DOM.removeElementClass(this.layer,"textDisabled")}};bobj.crv.PrintUI.disabledPageRange=function(A){if(this._fromBox&&this._toBox){this._fromBox.setDisabled(A);this._toBox.setDisabled(A)}};bobj.crv.PrintUI._submitBtnCB=function(){var B=null;var A=null;if(this._rangeRadio.isChecked()){B=parseInt(this._fromBox.getValue(),10);A=parseInt(this._toBox.getValue(),10);if(!B||!A||(B<0)||(B>A)){alert(L_bobj_crv_PrintPageRangeError);return }}if(this.widgetType=="PrintUI"){MochiKit.Signal.signal(this,"printSubmitted",B,A)}else{MochiKit.Signal.signal(this,"exportSubmitted",B,A,this._comboBox.getSelection().value)}this.show(false)};bobj.crv.PrintUI._getRPSafeURL=function(C){if(!C){return }if(C.indexOf("/")===0){return C}var B=window.location.href;var D=B.lastIndexOf("?");if(D>0){B=B.substring(0,D)}var A=B.lastIndexOf("/");if(A<0){return C}B=B.substring(0,A);return B+"/"+C};bobj.crv.PrintUI._getObjectTag=function(B){var C=[];C.push('<OBJECT width="0" height="0" ID="');C.push(this.actxId);C.push('" CLASSID="CLSID:');C.push(bobj.crv.ActxPrintControl_CLSID);C.push('" CODEBASE="');C.push(this._getRPSafeURL(this.codeBase));C.push("#Version=");C.push(bobj.crv.ActxPrintControl_Version);C.push('" VIEWASTEXT>');C.push('<PARAM NAME="PostBackData" VALUE="');C.push(B);C.push('">');C.push('<PARAM NAME="ServerResourceVersion" VALUE="');C.push(bobj.crv.ActxPrintControl_Version);C.push('">');if(this.lcid){C.push('<PARAM NAME="LocaleID" VALUE="');C.push(this.lcid);C.push('">')}if(this.url){C.push('<PARAM NAME="URL" VALUE="');C.push(this._getRPSafeURL(this.url));C.push('">')}if(this.title){C.push('<PARAM NAME="Title" VALUE="');C.push(this.title);C.push('">')}if(this.maxPage){C.push('<PARAM NAME="MaxPageNumber" VALUE="');C.push(this.maxPage);C.push('">')}if(this.paperOrientation){C.push('<PARAM NAME="PageOrientation" VALUE="');C.push(this.paperOrientation);C.push('">')}if(this.paperSize){C.push('<PARAM NAME="PaperSize" VALUE="');C.push(this.paperSize);C.push('">')}if(this.paperWidth){C.push('<PARAM NAME="PaperWidth" VALUE="');C.push(this.paperWidth);C.push('">')}if(this.paperLength){C.push('<PARAM NAME="PaperLength" VALUE="');C.push(this.paperLength);C.push('">')}if(this.driverName){C.push('<PARAM NAME="PrinterDriverName" VALUE="');C.push(this.driverName);C.push('">')}if(this.useDefPrinter){C.push('<PARAM NAME="UseDefaultPrinter" VALUE="');C.push(this.useDefPrinter);C.push('">')}if(this.useDefPrinterSettings){C.push('<PARAM NAME="UseDefaultPrinterSettings" VALUE="');C.push(this.useDefPrinterSettings);C.push('">')}if(this.sendPostDataOnce){C.push('<PARAM NAME="SendPostDataOnce" VALUE="');C.push(this.sendPostDataOnce);C.push('">')}C.push("</OBJECT>");C.push('<table id="');C.push(this.actxId);C.push('_wait" border="0" cellspacing="0" cellpadding="0" width="100%" ><tbody>');C.push('<tr><td align="center" valign="top">');var E=this;var D=E.getContainerWidth()-10;var A=E.getContainerHeight()-(2*E.pad+21+10);C.push('<table style="');C.push(sty("width",D));C.push(sty("height",A));C.push('" id="frame_table_');C.push(E.id);C.push('" cellspacing="0" cellpadding="0" border="0"><tbody><tr><td valign="top" class="dlgFrame" style="padding:5px" id="frame_cont_');C.push(E.id);C.push('">');C.push('<table border="0" cellspacing="0" cellpadding="0" width="100%"><tbody>');C.push('<tr><td align="center" style="padding-top:5px;">');C.push(img(_skin+"wait01.gif",200,40));C.push("</td></tr>");C.push('<tr><td align="left" style="padding-left:2px;padding-right:2px;padding-top:5px;">');C.push('<div class="icontext" style="wordWrap:break_word;">');C.push(convStr(L_bobj_crv_PrintControlProcessingMessage,false,true));C.push("</div></td></tr></tbody></table>");C.push("</td></tr></tbody></table>");C.push("</td></tr></tbody></table>");return C.join("")};bobj.crv.PrintUI._cancel=function(){if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML="";this._processingPrinting=false}};bobj.crv.PrintUI._processPrinting=function(){if(!this._processingPrinting){var B=document.getElementById(this.actxId);var A=document.getElementById(this.actxId+"_wait");if(B&&A){B.width="100%";B.height="100%";A.style.display="none"}this._processingPrinting=true}};bobj.crv.PrintUI.show=function(B,A){this._processingPrinting=false;if(B){if(!this.layer){targetApp(this.getHTML());this.init()}if(this.isActxPrinting){document.getElementById(this.actxContainerId).innerHTML=this._getObjectTag(A)}this._showOld(true)}else{if(this.layer){this._showOld(false)}}};bobj.crv.PrintUI.init=function(){this._initOld();if(!this.isActxPrinting){this._fromBox.init();this._toBox.init();this._submitBtn.init();this._allRadio.init();this._rangeRadio.init();this._allRadio.check(true);this._toBox.setDisabled(true);this._fromBox.setDisabled(true);if(this.widgetType=="ExportUI"){this._updateExportList()}}};bobj.crv.PrintUI.getHTML=function(){var B=bobj.html;var C=this;var A=C.beginHTML();if(!this.isActxPrinting){A+="<table cellspacing=0 cellpadding=0 border=0><tr><td><div class='dlgFrame'><table cellspacing=0 cellpadding=0 border=0 style='height:"+(this.height*0.9)+"px;width:"+this.width+"px;'><tr><td valign='top' class='naviBarFrame naviFrame'>"+(this.isExporting?this._getExportList():"")+"<fieldset style='border:0px;padding:0px'><legend style='position:relative;"+(_ie?"margin:0px -7px":"")+"'><table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_PrintRangeLbl+"</label></td><td class='dialogTitleLevel2Underline' style='width:100%'>&nbsp;</td></tr></table></legend><div style='margin:10px 25px;'>"+C._allRadio.getHTML()+C._rangeRadio.getHTML()+"<div style='padding-left:25px'><table class=dlgContent datatable='0'><tr><td align=right><label for='"+C._fromBox.id+"'> "+L_bobj_crv_PrintFromLbl+"</label></td><td align=left> "+C._fromBox.getHTML()+"</td></tr><tr><td align=right><label for='"+C._toBox.id+"'> "+L_bobj_crv_PrintToLbl+"</label></td><td align=left>"+C._toBox.getHTML()+"</td></tr></table></div></div></fieldset>"+(!this.isExporting?"<table style='width:100%;line-height:10px;'><tr><td class='dialogTitleLevel2' tabIndex=0><label>"+this.infoTitle+"</label></td><td class='dialogTitleLevel2Underline' style='width:100%'>&nbsp;</td></tr></table><div style='margin:10px 0px 10px 25px;' class='dlgHelpText'>"+this.infoMsg+"</div>":"")+"</td></tr></table></div></td></tr><tr><td align='right' valign='top'><table style='margin:6px 9px 0px 0px' cellspacing=0 cellpadding=0 border=0><tbody><tr><td>"+this._submitBtn.getHTML()+"</td></tbody></tr></table></td></tr></table>"}else{A+="<div id='"+this.actxContainerId+"'></div><script for=\""+this.actxId+'" EVENT="Finished(status, statusText)" language="javascript">getWidgetFromID("'+this.id+'").show(false);<\/script><script for="'+this.actxId+'" EVENT="PrintingProgress(pageNumber)" language="javascript">getWidgetFromID("'+this.id+'")._processPrinting();<\/script>'}A+=C.endHTML();A+=bobj.crv.getInitHTML(this.widx);return A};bobj.crv.newExportUI=function(A){A=MochiKit.Base.update({submitBtnLabel:L_bobj_crv_ExportBtnLbl,dialogTitle:L_bobj_crv_ExportDialogTitle,infoTitle:L_bobj_crv_ExportInfoTitle,infoMsg:L_bobj_crv_PrintInfo1,isExporting:true},A);var B=bobj.crv.newPrintUI(A);B._comboBox=newCustomCombo(B.id+"_combo",MochiKit.Base.bind(bobj.crv.ExportUI._onSelectFormat,B),false,270,L_bobj_crv_ExportFormatLbl,_skin+"../transp.gif",0,14);if(B._comboBox){B._comboBox.icon.border=0;B._comboBox.icon.h=14;B._comboBox.arrow.h=12;B._comboBox.arrow.dy+=2;B._comboBox.arrow.disDy+=2}B.widgetType="ExportUI";MochiKit.Base.update(B,bobj.crv.ExportUI);return B};bobj.crv.ExportUI._onSelectFormat=function(){var A=this._comboBox.getSelection().value;if(A=="CrystalReports"||A=="RPTR"||A=="RecordToMSExcel"||A=="RecordToMSExcel2007"||A=="CharacterSeparatedValues"||A=="XML"){this._fromBox.setDisabled(true);this._toBox.setDisabled(true);this._rangeRadio.check(false);this._rangeRadio.setDisabled(true);this._allRadio.check(true)}else{this._rangeRadio.setDisabled(false)}};bobj.crv.ExportUI.update=function(A){if(!A||A.cons!=="bobj.crv.newExportUI"){return }this.availableFormats=A.args.availableFormats;if(this._comboBox.initialized()){this._updateExportList()}};bobj.crv.ExportUI._updateExportList=function(){if(!this._comboBox.initialized()){this._comboBox.init()}this._updateComboItems();var A=this._comboBox.getItemByIndex(0);if(A!=null){this._comboBox.selectItem(A)}this._onSelectFormat()};bobj.crv.ExportUI._updateComboItems=function(){this._comboBox.removeAllMenuItems();var A=(bobj.isArray(this.availableFormats)?this.availableFormats.length:0);for(var B=0;B<A;B++){var C=this.availableFormats[B];this._comboBox.add(C.name,C.value,C.isSelected)}};bobj.crv.ExportUI._getExportList=function(){return"<table datatable='0' style='width:100%;line-height:10px;'><tr>"+(_ie?"<td class='dialogTitleLevel2'><label>":"<td class='dialogTitleLevel2'><label>")+L_bobj_crv_ExportFormatLbl+"</label></td><td class='dialogTitleLevel2Underline' style='width:100%'>&nbsp;</td></tr></table><div style='margin:10px 25px;'>"+this._comboBox.getHTML()+"</div>"};bobj.crv.ErrorDialog.getInstance=function(){if(!bobj.crv.ErrorDialog.__instance){bobj.crv.ErrorDialog.__instance=bobj.crv.newErrorDialog()}return bobj.crv.ErrorDialog.__instance};bobj.crv.newErrorDialog=function(A){A=MochiKit.Base.update({id:bobj.uniqueId(),title:L_bobj_crv_Error,text:null,detailText:null,okLabel:L_bobj_crv_OK,promptType:_promptDlgCritical},A);var B=newPromptDialog(A.id,A.title,A.text,A.okLabel,null,A.promptType,null,null,true,true);B.widgetType="ErrorDialog";bobj.fillIn(B,A);B._promptDlgInit=B.init;B._promptDialogSetText=B.setText;B._promptDialogShow=B.show;B._promptDialogSetTitle=B.setTitle;B._promptDialogSetPromptType=B.setPromptType;MochiKit.Base.update(B,bobj.crv.ErrorDialog);B.noCB=MochiKit.Base.bind(B._onClose,B);B.yesCB=B.noCB;B._detailBtn=newIconWidget(B.id+"_detailBtn",bobj.skinUri("../help.gif"),MochiKit.Base.bind(bobj.crv.ErrorDialog._onDetailBtnClick,B),L_bobj_crv_showDetails,L_bobj_crv_showDetails,16,16,0,0,22,0,true);return B};bobj.crv.ErrorDialog.init=function(){this._promptDlgInit();this._detailBtn.init();this._detailRow=document.getElementById(this.id+"_detRow");this._detailArea=document.getElementById(this.id+"_detArea");if(!this.detailText){this._detailBtn.show(false)}};bobj.crv.ErrorDialog.getHTML=function(){var I=bobj.html.TABLE;var B=bobj.html.TBODY;var E=bobj.html.TR;var K=bobj.html.TD;var L=bobj.html.PRE;var H=bobj.html.DIV;var J=PromptDialog_getimgPath(this.promptType);var F=PromptDialog_getimgAlt(this.promptType);var A="320";var D="300px";var G="100px";var C=I({"class":"dlgBody",width:A,cellpadding:"0",cellspacing:"5",border:"0"},B(null,E(null,K(null,I({"class":"dlgBody",cellpadding:"5",cellspacing:"0",border:"0"},B(null,E(null,K({align:"right",width:"32"},img(J,32,32,null,'id="dlg_img_'+this.id+'"',F)),K(),K({id:"dlg_txt_"+this.id,align:"left"},H({tabindex:"0"},convStr(this.text,false,true)))))))),E({id:this.id+"_detRow",style:{display:"none"}},K(null,H({"class":"infozone",style:{width:D,height:G,overflow:"auto"}},L({id:this.id+"_detArea"},this.detailText)))),E(null,K(null,getSep())),E(null,K(null,I({cellpadding:"5",cellspacing:"0",border:"0",width:"100%"},B(null,E(null,K({align:"left"},this._detailBtn.getHTML()),K({align:"right"},this.yes.getHTML()))))))));return this.beginHTML()+C+this.endHTML()};bobj.crv.ErrorDialog.setText=function(C,A){this.text=C;this.detailText=A;if(this.layer){this._promptDialogSetText(C||"");if(this._detailArea){this._detailArea.innerHTML=A||""}var B=A?true:false;this._detailBtn.show(B);if(!B){this.showDetails(false)}}};bobj.crv.ErrorDialog.setTitle=function(A){this.title=A;if(this.layer){this._promptDialogSetTitle(A||"")}};bobj.crv.ErrorDialog.setPromptType=function(A){this.promptType=A;if(this.layer){this._promptDialogSetPromptType(A)}};bobj.crv.ErrorDialog.show=function(A,B){if(typeof A=="undefined"){A=true}if(A){this._closeCB=B;if(!this.layer){targetApp(this.getHTML());this.init()}this.layer.onkeyup=DialogBoxWidget_keypress;DialogBoxWidget_keypress=MochiKit.Base.noop;this._promptDialogShow(true)}else{if(this.layer){this._closeCB=null;this._promptDialogShow(false)}}};bobj.crv.ErrorDialog.showDetails=function(A){if(typeof A=="undefined"){A=true}if(this._detailRow&&this._detailBtn){if(A){this._detailRow.style.display="";this._detailBtn.changeText(L_bobj_crv_hideDetails)}else{this._detailRow.style.display="none";this._detailBtn.changeText(L_bobj_crv_showDetails)}}};bobj.crv.ErrorDialog._onDetailBtnClick=function(){if(this._detailRow){this.showDetails(this._detailRow.style.display=="none")}};bobj.crv.ErrorDialog._onClose=function(){if(this._closeCB){this._closeCB();this._closeCB=null}DialogBoxWidget_keypress=this.layer.onkeyup;this.layer.onkeyup=null};bobj.crv.newReportProcessingUI=function(B){B=MochiKit.Base.update({id:bobj.uniqueId(),delay:250,message:L_bobj_crv_ReportProcessingMessage},B);var D=document.createElement("div");D.style.visibility="hidden";D.innerHTML=B.message;var A=D.innerHTML;D=null;var C=newWaitDialogBoxWidget(B.id,0,0,"",false,bobj.crv.ReportProcessingUI.cancelCB,true,A,true);C.widgetType="ReportProcessingUI";C.delay=B.delay;MochiKit.Base.update(C,bobj.crv.ReportProcessingUI);return C};bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null;bobj.crv.ReportProcessingUI.cancelCB=function(){bobj.crv.reportProcessingDialog.cancelled=true;if(bobj.crv.reportProcessingDialog.deferred!==null){bobj.crv.reportProcessingDialog.deferred.cancel()}bobj.crv.reportProcessingDialog.cancelShow()};bobj.crv.ReportProcessingUI.wasCancelled=function(){return bobj.crv.reportProcessingDialog.cancelled};bobj.crv.ReportProcessingUI._prepareToShow=function(){if(bobj.crv.reportProcessingDialog!==null){bobj.crv.reportProcessingDialog.cancelShow()}if(!this.layer){append2(document.body,this.getHTML());this.init()}this.deferred=null;bobj.crv.reportProcessingDialog=this};bobj.crv.ReportProcessingUI.Show=function(){this._prepareToShow();bobj.crv.reportProcessingDialog.show(true)};bobj.crv.ReportProcessingUI.delayedShow=function(){this._prepareToShow();bobj.crv.timerID=setTimeout("bobj.crv._showReportProcessingDialog ()",bobj.crv.reportProcessingDialog.delay)};bobj.crv.ReportProcessingUI.cancelShow=function(){if(bobj.crv.timerID){clearTimeout(bobj.crv.timerID)}if(bobj.crv.reportProcessingDialog){bobj.crv.reportProcessingDialog.show(false)}bobj.crv.reportProcessingDialog=null;bobj.crv.timerID=null};bobj.crv.ReportProcessingUI.setDeferred=function(A){bobj.crv.reportProcessingDialog.deferred=A;if(bobj.crv.reportProcessingDialog.wasCancelled()===true){A.cancel()}};bobj.crv._showReportProcessingDialog=function(){if(bobj.crv.reportProcessingDialog&&bobj.crv.reportProcessingDialog.delay!==0){bobj.crv.logger.info("ShowReportProcessingDialog");bobj.crv.reportProcessingDialog.show(true)}};