var PE_VALUE_DESC_SEPARATOR=" - ";if(typeof (_pe)=="undefined"){_pe=new function(){var A=this;A._ie=(document.all!=null)?true:false;A._dom=(document.getElementById!=null)?true:false;A._isQuirksMode=(document.compatMode!="CSS1Compat");A._moz=A._dom&&!A._ie;A._appVer=navigator.appVersion.toLowerCase();A._mac=(A._appVer.indexOf("macintosh")>=0)||(A._appVer.indexOf("macos")>=0);A._userAgent=navigator.userAgent?navigator.userAgent.toLowerCase():null;A._saf=A._moz&&(A._userAgent.indexOf("safari")>=0);A._ie6=A._ie&&(A._appVer.indexOf("msie 6")>=0);A._root="";A._images=A._root+"/images/";A._prompts=new Array;A._lovBS=1000;A._st="s";A._nm="n";A._cy="c";A._bo="b";A._da="d";A._tm="t";A._dt="dt";_BlockWaitWidgetID="PEBlockWidgetID";A._theLYR=null;A._dlgResize=null;A._widgets=new Array;A.DlgBox_modals=new Array;A.DlgBox_instances=new Array;A.DlgBox_current=null;A._show="visible";A._hide="hidden";A._hand=A._ie?"hand":"pointer";A.init=PE_init;A.canSubmit=PE_canSubmit;A.beginBlocking=PE_beginBlocking;A.endBlocking=PE_endBlocking;A.setLOVMsg=PE_setLOVMsg}}function PE_init(B,A){var C=this;if(B&&B.length>0){if(B.charAt(B.length-1)!="/"){B+="/"}C._root=B;C._images=B+"images/"}else{C._root=null;C._images=null}if(A>0){C._lovBS=A}}function PE_canSubmit(){return(this.DlgBox_current)?false:true}function PE_setLOVMsg(C,A,B){var D=document.getElementById(A);var E=D.value;if(E.length>0){E+="&"}E+="cmd=1&ap="+B;D.value=E}var DateTimeFormatSetting={datePattern:"Y-M-D",isTwoDigitMonth:true,isTwoDigitDay:true,dateRegex:null,dateTimeRegex:null};function promptengine_getDatePattern(){return DateTimeFormatSetting.datePattern}function promptengine_setDatePattern(A){DateTimeFormatSetting.datePattern=A}function promptengine_getIsTwoDigitMonth(){return DateTimeFormatSetting.isTwoDigitMonth}function promptengine_setIsTwoDigitMonth(A){DateTimeFormatSetting.isTwoDigitMonth=A}function promptengine_getIsTwoDigitDay(){return DateTimeFormatSetting.isTwoDigitDay}function promptengine_setIsTwoDigitDay(A){DateTimeFormatSetting.isTwoDigitDay=A}function promptengine_getDateRegex(){return DateTimeFormatSetting.dateRegex}function promptengine_setDateRegex(A){DateTimeFormatSetting.dateRegex=A}function promptengine_getDateTimeRegex(){return DateTimeFormatSetting.dateTineRegex}function promptengine_setDateTimeRegex(A){DateTimeFormatSetting.dateTineRegex=A}function _convStr(D,A,C){D=""+D;var B=D.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;");if(A){B=B.replace(/ /g,"&nbsp;")}if(C){B=B.replace(/\n/g,"<br>")}return B}function _opt(C,A,B){return'<option value="'+_convStr(C)+'" '+(B?"selected":"")+">"+_convStr(A)+"</option>"}function _canScanFrames(A){var B=true,D=null;if(_pe._moz){_oldErrHandler=window.onerror;window.onerror=localErrHandler}try{D=A.document;B=false}catch(C){}if(_pe._moz){window.onerror=_oldErrHandler}return(!B&&(D!=null))}function _restoreAllDisabledInputs(F,A){if(_pe._ie&&window._peInputStackLevel!=null){F=F?F:window;if(_canScanFrames(F)){if(A==null){A=--window._peInputStackLevel}var I=F.document.body,D=I?I.getElementsByTagName("SELECT"):null,G=D?D.length:0;for(var C=0;C<G;C++){var E=D[C];if(E._peDisableLevel==A){E.disabled=false;E._peDisableLevel=null}}var H=F.frames,J=H.length;for(var B=0;B<J;B++){_restoreAllDisabledInputs(H[B],A)}}}}function _disableAllInputs(M,K,N,E,I,A){if(_pe._ie){I=I?I:window;if(_canScanFrames(I)){var L=I.document.body,F=L?L.getElementsByTagName("SELECT"):null,H=F?F.length:0;if(A==null){if(window._peInputStackLevel==null){window._peInputStackLevel=0}A=window._peInputStackLevel++}for(var D=0;D<H;D++){var G=F[D];var B=(M==null)||_isLayerIntersectRect(G,M,K,N,E);if(!G.disabled&&B){G._peDisableLevel=A;G.disabled=true}}var J=I.frames,O=J.length;for(var C=0;C<O;C++){_disableAllInputs(null,null,null,null,J[C],A)}}}}function _getBGIframe(A){return'<iframe id="'+A+'" style="display:none;left:0px;position:absolute;top:0px" src="'+_pe._images+'transp.gif" frameBorder="0" scrolling="no"></iframe>'}function _eventCancelBubble(B,A){A=A?A:window;_pe._ie?A.event.cancelBubble=true:B.cancelBubble=true}function _append(D,B){if(_pe._ie){D.insertAdjacentHTML("BeforeEnd",B)}else{var A=document;var C=A.createRange();C.setStartBefore(D);var E=C.createContextualFragment(B);D.appendChild(E)}}function _targetApp(A){_append(document.body,A)}function _isLayerIntersectRect(C,B,J,K,F){var E=_getPos(C).x,H=_getPos(C).y,D=E+C.offsetWidth,G=H+C.offsetHeight,A=B+K,I=J+F;return((B>E)||(A>E))&&((B<D)||(A<D))&&((J>H)||(I>H))&&((J<G)||(I<G))}function _getPos(B,A){A=A?A:null;for(var D=0,C=0;(B!=null)&&(B!=A);D+=B.offsetLeft,C+=B.offsetTop,B=B.offsetParent){}return{x:D,y:C}}function _getLayer(A){return document.getElementById(A)}function _getWidget(B){if(B==null){return null}var A=B._widget;if(A!=null){return _pe._widgets[A]}else{return _getWidget(B.parentNode)}}function _isHidden(B){if((B==null)||(B.tagName=="BODY")){return false}var A=B.style;if((A==null)||(A.visibility==_pe._hide)||(A.display=="none")){return true}return _isHidden(B.parentNode)}function _attr(A,B){return(B!=null?" "+A+'="'+B+'" ':"")}function _img(E,A,C,F,B,D){B=(B?B:"");if(D==null){D=""}return"<img"+_attr("width",A)+_attr("height",C)+_attr("src",E)+_attr(_pe._ie?"alt":"title",D)+_attr("align",F)+' border="0" hspace="0" vspace="0" '+(B?B:"")+">"}function _imgOffset(A,G,D,J,I,B,F,C,H,E){return _img(_pe._images+"transp.gif",G,D,E,(F?F:"")+" "+_attr("id",B)+' style="'+_backImgOffset(A,J,I)+(H?H:"")+'"',C)}function _changeOffset(E,B,A,D,F){var C=E.style;if(C){if((B!=null)&&(A!=null)){C.backgroundPosition=""+(-B)+"px "+(-A)+"px"}if(D){C.backgroundImage="url('"+D+"')"}}if(F){E.alt=F}}function _simpleImgOffset(A,G,D,J,I,B,F,C,H,E){if(_pe._ie){if(J==null){J=0}if(I==null){I=0}return"<div "+(F?F:"")+" "+_attr("id",B)+' style="position:relative;padding:0px;width:'+G+"px;height:"+D+"px;overflow:hidden;"+(H?H:"")+'">'+_img(A,null,null,"top",'style="margin:0px;position:relative;top:'+(-I)+"px;left:"+(-J)+'px"',C)+"</div>"}else{return _imgOffset(A,G,D,J,I,B,F,C,H,E)}}function _changeSimpleOffset(E,B,A,D,F){if(_pe._ie){E=E.childNodes[0];var C=E.style;if((D!=null)&&(D!=E.src)){E.src=D}if(B!=null){C.left=""+(-B)+"px"}if(A!=null){C.top=""+(-A)+"px"}if(F!=null){E.alt=F}}else{_changeOffset(E,B,A,D,F)}}function _backImgOffset(C,B,A){return"background-image:url('"+C+"');background-position:"+(-B)+"px "+(-A)+"px;"}function _sty(A,B){return(B!=null?A+":"+B+";":"")}function _getSpace(A,B){return'<table height="'+B+'" border="0" cellspacing="0" cellpadding="0"><tr><td>'+_img(_pe._images+"transp.gif",A,B)+"</td></tr></table>"}function _isTextInput(A){var B=_pe._ie?A.srcElement:A.target;var C=false;if(B.tagName=="TEXTAREA"){C=true}if((B.tagName=="INPUT")&&(B.type.toLowerCase()=="text")){C=true}return C}function _documentWidth(B){var B=B?B:window;var A=Math.max(document.body.clientWidth,document.documentElement.clientWidth);A=Math.max(A,document.body.scrollWidth);return A}function _documentHeight(B){var B=B?B:window;var A=Math.max(document.body.clientHeight,document.documentElement.clientHeight);A=Math.max(A,document.body.scrollHeight);return A}function _winWidth(B){var B=B?B:window;var A;if(_pe._ie){if(_pe._isQuirksMode){A=B.document.body.clientWidth}else{A=B.document.documentElement.clientWidth}}else{A=B.innerWidth}return A}function _winHeight(B){var B=B?B:window;var A;if(_pe._ie){if(_pe._isQuirksMode){A=document.body.clientHeight}else{A=document.documentElement.clientHeight}}else{A=B.innerHeight}return A}function _getScrollX(A){var B=0;var A=A?A:window;if(typeof (A.scrollX)=="number"){B=A.scrollX}else{B=Math.max(A.document.body.scrollLeft,A.document.documentElement.scrollLeft)}return B}function _getScrollY(B){var A=0;var B=B?B:window;if(typeof (B.scrollY)=="number"){A=window.scrollY}else{A=Math.max(B.document.body.scrollTop,B.document.documentElement.scrollTop)}return A}function _eventGetX(A){return _pe._ie?window.event.clientX:A.clientX?A.clientX:A.pageX}function _eventGetY(A){return _pe._ie?window.event.clientY:A.clientY?A.clientY:A.pageY}function _eventGetKey(B,A){A=A?A:window;return _pe._ie?A.event.keyCode:B.keyCode}function _isLayerDisplayed(A){var B=A?A.style:null;if(B){if(B.display=="none"||B.visibility=="hidden"){return false}else{var C=A.parentNode;if(C!=null){return _isLayerDisplayed(C)}else{return true}}}else{return true}}function _safeSetFocus(A){if(A&&A.focus&&_isLayerDisplayed(A)&&!A.disabled){A.focus()}}function PE_getLB(A){var B=new Object;B.lyr=A;B.arr=new Array;B.size=0;B.add=LB_add;B.update=LB_update;return B}function LB_add(D,A,B){var C=this;C.arr[++C.size]=_opt(D,A,B)}function LB_update(){var F=this;var A=F.arr;if(!F.lyr){return }var E=F.lyr.parentNode;var C=E.innerHTML;var B=C.indexOf(">");if(B==-1){return }var D=C.lastIndexOf("<");if(D<=B){return }A[0]=C.substring(0,B+1);A[F.size+1]=C.substr(D);E.innerHTML=A.join("")}function newUnits(A,B){var C=new Object;C.parr=new Array;if(B){C.idh=B}else{C.idh=""}C.num=A;C.init=Units_init;C.show=Units_show;C.toggle=Units_toggle;C.updateForm=Units_updateForm;C.activate=Units_activate;C.addP=Units_addP;return C}function Units_init(A){var B=this;B.toggle(A)}function Units_show(D,A){var E=this;var B=document.getElementById(E.idh+D+"_PU");if(B){var C=B.style;if(A){C.display=""}else{C.display="none"}}}function Units_activate(E){var G=this;var A=document.getElementById(G.idh+E+"_PU");if(A){var B=_getScrollY(),D=_getScrollX();var F=A.offsetHeight,C=_winHeight(),H=_getPos(A).y;if(H<B){window.scrollTo(D,H)}else{if(H+F>B+C){window.scrollTo(D,Math.max(H,H+F-C))}}}}function Units_toggle(B){var C=this,D=C.num;for(var A=0;A<D;A++){C.show(A,true)}C.activate(B)}function Units_addP(B){var C=this;var A=C.parr;A[A.length]=B}function Units_updateForm(E,A,B){var G=this,D=G.parr;for(var C in D){var F=D[C];if(F){if(!F.updateForm(E,A,B)){G.toggle(F.uid);return false}}}return true}function P_updateForm(D,B,C){var E=this,A=false;if(E.readonly==true){return true}if(E.mul){A=promptengine_updateMultiValue(D,B,E.id,E.vt,C,E.valueRequired)}else{if(E.rn){A=promptengine_updateRangeValue(D,B,E.id,E.vt,C,E.valueRequired)}else{A=promptengine_updateDiscreteValue(D,B,E.id,E.vt,C,E.valueRequired)}}return A}function P_addV(B,D){var C=this;if(C.vl==null){C.vl=new Array;if(D){C.dl=new Array}}var A=C.vl.length;C.vl[A]=B;if(C.dl){C.dl[A]=D}}function P_findBatch(F,C){if(!C){return(-1)}var G=this;var E=G.vl;if(F){var A=G.lov[F];if(A&&A.vl){E=A.vl}}if(E){for(var D in E){var B=E[D];if(B&&B==C){return(Math.floor(D/_pe._lovBS))}}}return(-1)}function P_updateLOVNB(D,K){var C=this;var G=C.id;var A=document.getElementById(G+D+"Batch");if(!A){return }var J=C.lov[D];if(K){var B=A.options;B.length=0;var E=C.vl,F=0;if(J.vl){E=J.vl}var H=Math.ceil(E.length/_pe._lovBS);while(F<H){var I=F+1;if(J.sidx==F){I+="*"}B[B.length]=new Option(I,F,false,false);F++}}if(J.bidx>=0){A.selectedIndex=J.bidx}}function P_updateLOV(N,B){var H=this;var G=H.id;var D=G+N;var J=document.getElementById(D);if(!J){return }var A=H.lov[N];var K=null;var R=H.vl;var L=H.dl;var S=-1;if(A){K=A.sel;if(A.vl){R=A.vl;L=A.dl}}var F=PE_getLB(J);if(B){F.add("","...")}var I=A.sidx;if(A.bidx<0){A.sidx=H.findBatch(N,K);if(A.sidx>=0){A.bidx=A.sidx}else{A.bidx=0}I=-2}var C=A.bidx;var O=C*_pe._lovBS,P=R.length,M=0;while(O<P){if(M>=_pe._lovBS){break}var E=R[O];var Q=null;if(L){Q=L[O];if(Q==""){Q=E}else{if(H.dop==0){Q=E+PE_VALUE_DESC_SEPARATOR+Q}}}else{Q=E}if(K&&K==E){S=M;H.sidx=C}F.add(E,Q);O++;M++}F.update();J=document.getElementById(D);if(S!=-1){if(B){S++}J.selectedIndex=S}H.updateLOVNB(N,I!=A.sidx)}function P_getDesc(A){if(!A){return null}var F=this;var E=F.vl;var C=F.dl;if(!C){return null}var B=-1;for(var D in E){if(E[D]==A){B=D;break}}if(B>=0){return C[B]}return null}function P_updateSLOV(){var A=this;var K=A.id;var J=document.getElementById(K+"ListBox");if(!J){return }var B=A.sl;if(typeof (B)!="object"){return }var M=PE_getLB(J);var E=A.vl;var L=A.dl;for(var I in B){var P=B[I];if(typeof (P)=="string"){var O=A.getDesc(P);var H;if(O&&O!=""){if(A.dop){H=O}else{H=P+PE_VALUE_DESC_SEPARATOR+O}}else{H=P}M.add(P,H,false)}else{var N=P.l;var G=P.u;var F=P.lt;var D=P.ut;var C=null;var H=null;if(F==0||F==1){C="(";H="("}else{C="[";H="["}if(F){C+=N;var O=A.getDesc(N);if(O&&O!=""){if(A.dop){H+=O}else{H+=N+PE_VALUE_DESC_SEPARATOR+O}}else{H+=N}}C+="_crRANGE_";H+="  ..  ";if(D){C+=G;var O=A.getDesc(G);if(O&&O!=""){if(A.dop){H+=O}else{H+=G+PE_VALUE_DESC_SEPARATOR+O}}else{H+=G}}if(D==0||D==1){C+=")";H+=")"}else{C+="]";H+="]"}M.add(C,H,false)}}M.update()}function P_update(A){var B=this;if(A){if(A=="AvailableList"){B.updateLOV(A)}else{if(A=="ListBox"){B.updateSLOV()}else{B.updateLOV(A,true)}}}else{B.updateLOV("SelectValue",true);B.updateLOV("AvailableList");B.updateLOV("SelectLowerRangeValue",true);B.updateLOV("SelectUpperRangeValue",true);B.updateSLOV()}}function P_setLOV(B,A){var C=this;C.vl=B;if(B){if(!A||B.length!=A.length){C.dl=null}else{C.dl=A}}else{C.dl=null}}function P_setInitSel(B){var C=this;var A=C.lov.SelectValue;A.sel=B}function P_setInitSelList(A){this.sl=A}function P_setInitBound(B,A){var E=this;var D=E.lov.SelectLowerRangeValue;D.sel=B;var C=E.lov.SelectUpperRangeValue;C.sel=A}function P_back(C){var D=this;var A=D.lov[C];if(!A){return }var B=A.bidx;if(B>0){B--;if(B<0){B=0}A.bidx=B;D.update(C)}}function P_next(D){var E=this;var B=E.lov[D];if(!B){return }var A=((B.vl)?B.vl.length:E.vl.length);var C=B.bidx;if((C+1)*_pe._lovBS<A){B.bidx=C+1;E.update(D)}}function newLOV(){var A=new Object;A.bidx=-1;A.sidx=-1;A.sel=null;A.filter=null;A.vl=null;A.dl=null;return A}function newP(I,H,B,K,E,J,G,D,F,A){var C=new Object;C.id=B;C.vt=K;C.mul=E;C.di=J;C.rn=G;C.dop=D;C.readonly=F;C.valueRequired=A;C.units=I;C.uid=H;C.lov=new Array;C.lov.SelectValue=newLOV();C.lov.AvailableList=newLOV();C.lov.SelectLowerRangeValue=newLOV();C.lov.SelectUpperRangeValue=newLOV();C.vl=null;C.dl=null;C.sl=null;C.addV=P_addV;C.update=P_update;C.setLOV=P_setLOV;C.updateLOV=P_updateLOV;C.updateSLOV=P_updateSLOV;C.updateLOVNB=P_updateLOVNB;C.getDesc=P_getDesc;C.findBatch=P_findBatch;C.back=P_back;C.next=P_next;C.showFilter=P_showFilter;C.applyFilter=P_applyFilter;C.setInitSel=P_setInitSel;C.setInitBound=P_setInitBound;C.setInitSelList=P_setInitSelList;C.updateForm=P_updateForm;_pe._prompts[B]=C;if(I){I.addP(C)}return C}function P_navigateCB(E,A,C,B){var D=_pe._prompts[A];if(!D){return }if(B=="p"){D.back(C)}else{if(B=="n"){D.next(C)}}}function P_selectCB(D,B,G,F){var H=_pe._prompts[B];if(!H){return }var C=B+F;var E=B+G;promptengine_selectValue(D,E,C);var A=H.lov[G];A.sel=document.getElementById(C).value;if(!A.sel||A.sel==""){A.sidx=-1;A.sel=null}else{if(A.sidx!=A.bidx){A.sidx=A.bidx}}H.updateLOVNB(G,true)}function P_batchCB(G,B,E){var F=_pe._prompts[B];var D=document.getElementById(B+E+"Batch");if(!F||!D){return }var C=D.selectedIndex;if(C>=0){var A=F.lov[E];if(!A){return }A.bidx=C;F.update(E)}}function P_applyFilter(D,B){if(B==null){return }var C=this;var E=C.vl;var J=C.dl;if(!E||E.constructor!=Array||E.length==0){return }var I=true;if(!J||J.constructor!=Array){I=false}var M=C.lov[D];if(!M){return }var P=M.filter;if(!P){P=""}if(B==P){return }var A=null;var G=null;if(B==""){B=null}else{A=[];if(I){G=[]}B=B.replace(String.fromCharCode(160)," ");var F=0;for(var H=0,L=E.length;H<L;H++){var O=E[H];var K=(I?J[H]:"");var N="";if(C.dop==1){if(K==""){N=O}else{N=K}}else{N=O;if(K!=""){N+=PE_VALUE_DESC_SEPARATOR;N+=K}}N=N.replace(String.fromCharCode(160)," ");if(N&&N.toLowerCase().indexOf(B.toLowerCase())!=-1){A[F]=O;if(I){G[F]=J[H]}F++}}}M.filter=B;M.vl=A;M.dl=G;M.bidx=-1;M.sidx=-1;C.updateLOV(D,true)}function P_promptFilter(F,D,H){var C=_pe._prompts[F];if(!C){return }var E=C.vl;var G=C.dl;if(!E||E.length==0){return }var K=C.lov[D];if(!K){return }var B=K.filter;if(!B){B=""}var A=H.target?H.target:H.srcElement;var J=_findPos(A);var L=J.x+A.offsetWidth;var I=J.y+A.offsetHeight;C.showFilter(D,B,L,I)}function P_promptClearFilter(A,C,B){var D=_pe._prompts[A];if(!D){return }if(D.filterDlg){D.filterDlg.setValue("");D.applyFilter(C,"")}}function P_showFilter(C,B,A,F){var E=this;if(!E.filterDlg){E.filterDlg=newFilterDlg(E.id)}var D=E.filterDlg;D.wty=C;D.setValue(B);D.show(true);D.initDlg(A,F)}function _findPos(B,A){var A=A?A:null;var D=0;var C=0;while(B.parentNode||B.offsetParent){if(B.offsetParent){D+=B.offsetLeft;C+=B.offsetTop;B=B.offsetParent}else{if(B.parentNode){if(B.style){if(B.style.left){D+=B.style.left}if(B.style.top){C+=B.style.top}}B=B.parentNode}else{break}}}if(A){relToCord=getPos2(A);D-=relToCord.x;C-=relToCord.y}return{x:D,y:C}}function FilterDlg_okCB(B){var D=this;if(B){D=_getWidget(_getLayer(B))}if(D){var C=_pe._prompts[D.promptid];var A=D.getValue();D.show(false);C.applyFilter(D.wty,A)}}function FilterDlg_cancelCB(A){var B=this;if(A){B=_getWidget(_getLayer(A))}if(B){B.show(false)}}function FilterDlg_enterCB(){}function newFilterDlg(F){var I=60;var K=52;var D=300;var J=100;var G=0.9*D;var B="filterDlg"+F;var A=newDlgBox(B,L_SetFilter,D,J,FilterDlg_okCB,FilterDlg_cancelCB,false);A.promptid=F;A.setValue=FilterDlg_setValue;A.getValue=FilterDlg_getValue;A.initDlg=FilterDlg_initDlg;var H=newBtn(B+"_okBtn",L_OK,"FilterDlg_okCB('"+B+"')",I,"OK","OK",0,0);var C=newBtn(B+"_cancelBtn",L_Cancel,"FilterDlg_cancelCB('"+B+"')",I,"Cancel","Cancel",0,0);var E=newTextField(B+"_textFld",null,null,null,FilterDlg_enterCB,true,null,G);_targetApp(A.beginHTML()+'<table cellspacing="0" cellpadding="5" border="0"><tbody><tr><td><table cellspacing="0" cellpadding="0" border="0"><tbody><tr><td><div style="overflow:auto">'+E.getHTML()+'</div></td></tr></tbody></table></td></tr><tr><td align="center" valign="right"></td></tr><tr><td align="right" valign="center"><table cellspacing="0" cellpadding="0" border="0"><tbody><tr><td>'+H.getHTML()+"</td><td>"+_getSpace(5,1)+"</td><td>"+C.getHTML()+"</td></tr></tbody></table></td></tr></table>"+A.endHTML());A.init();H.init();C.init();E.init();A.textField=E;return A}function FilterDlg_setValue(A){var B=this;B.textField.setValue(A)}function FilterDlg_getValue(){var A=this;if(A.textField){return A.textField.getValue()}return null}function FilterDlg_initDlg(A,D){var C=this;if(A+C.getWidth()>_winWidth()+_getScrollX()){A=Math.max(0,_winWidth()+_getScrollX()-C.getWidth()-10)}if(D+C.getHeight()>_winHeight()+_getScrollY()){D=Math.max(0,_winHeight()+getScrollY()-C.getHeight()-10)}C.move(A,D);C.placeIframe(true,true);var B=C.textField;B.select();B.focus()}function newCtl(B){var A=new Object;A.id=B;A.layer=null;A.css=null;A.getHTML=Ctl_getHTML;A.beginHTML=Ctl_getHTML;A.endHTML=Ctl_getHTML;A.write=Ctl_write;A.begin=Ctl_begin;A.end=Ctl_end;A.init=Ctl_init;A.move=Ctl_move;A.resize=Ctl_resize;A.setBgColor=Ctl_setBgColor;A.show=Ctl_show;A.getWidth=Ctl_getWidth;A.getHeight=Ctl_getHeight;A.setHTML=Ctl_setHTML;A.setDisabled=Ctl_setDisabled;A.focus=Ctl_focus;A.setDisplay=Ctl_setDisplay;A.isDisplayed=Ctl_isDisplayed;A.setTooltip=Ctl_setTooltip;A.initialized=Ctl_initialized;A.widx=_pe._widgets.length;_pe._widgets[A.widx]=A;return A}function Ctl_getHTML(){return""}function Ctl_write(B){var A=this.getHTML(B);if(parent.writeSource){parent.writeSource(A)}document.write(A)}function Ctl_begin(){document.write(this.beginHTML())}function Ctl_end(){document.write(this.endHTML())}function Ctl_init(){var A=this;A.layer=_getLayer(A.id);A.css=A.layer.style;A.layer._widget=A.widx;if(A.initialHTML){A.setHTML(A.initialHTML)}}function Ctl_move(A,B){c=this.css;if(A!=null){if(_pe._moz){c.left=""+A+"px"}else{c.pixelLeft=A}}if(B!=null){if(_pe._moz){c.top=""+B+"px"}else{c.pixelTop=B}}}function Ctl_focus(){_safeSetFocus(this.layer)}function Ctl_setBgColor(A){this.css.backgroundColor=A}function Ctl_show(A){this.css.visibility=A?_pe._show:_pe._hide}function Ctl_getWidth(){return this.layer.offsetWidth}function Ctl_getHeight(){return this.layer.offsetHeight}function Ctl_setHTML(A){var B=this;if(B.layer){B.layer.innerHTML=A}else{B.initialHTML=A}}function Ctl_setDisplay(A){this.css.display=A?"":"none"}function Ctl_isDisplayed(){if(this.css.display=="none"){return false}else{return true}}function Ctl_setDisabled(A){if(this.layer){this.layer.disabled=A}}function Ctl_resize(A,B){if(A!=null){this.css.width=""+(Math.max(0,A))+"px"}if(B!=null){this.css.height=""+(Math.max(0,B))+"px"}}function Ctl_setTooltip(A){this.layer.title=A}function Ctl_initialized(){return this.layer!=null}function PE_beginBlocking(){var A=newBlockWidget();A.show(true)}function PE_endBlocking(){var A=_getLayer(_BlockWaitWidgetID);if(A){A.style.display="none"}}function newBlockWidget(){if(window._PEBlockWidget!=null){return window._PEBlockWidget}var A=newCtl(_BlockWaitWidgetID);A.getPrivateHTML=BlockWidget_getPrivateHTML;A.init=BlockWidget_init;A.show=BlockWidget_show;window._PEBlockWidget=A;return A}function BlockWidget_init(){}function BlockWidget_getPrivateHTML(){return'<div id="'+this.id+'" onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0"  style="background-image:url(\''+_pe._images+'transp.gif\')";z-index:6000;cursor:wait;position:absolute;top:0px;left:0px;width:100%;height:100%"></div>'}function BlockWidget_show(A){var B=this;if(B.layer==null){B.layer=_getLayer(B.id);if(B.layer==null){_targetApp(B.getPrivateHTML());B.layer=_getLayer(B.id);B.css=B.layer.style}else{B.css=B.layer.style}}B.setDisplay(A)}function newBtn(C,L,H,D,F,Q,G,I,B,M,K,P,N,J,A,O){var E=newCtl(C);E.label=L;E.cb=H;E.width=D;E.hlp=F;E.tooltip=Q;E.tabIndex=G;E.isGray=false;E.txt=null;E.icn=null;E.margin=I?I:0;E.extraStyle="";if(B){E.url=B;E.w=M;E.h=K;E.dx=P;E.dy=N;E.disDx=(A!=null)?A:P;E.disDy=(O!=null)?O:N;E.imgRight=J?true:false}E.getHTML=Btn_getHTML;E.setDisabled=Btn_setDisabled;E.setText=Btn_setText;E.changeImg=Btn_changeImg;E.oldInit=E.init;E.init=Btn_init;E.isDisabled=Btn_isDisabled;E.instIndex=Btn_currInst;Btn_inst[Btn_currInst++]=E;return E}Btn_inst=new Array;Btn_currInst=0;function Btn_getHTML(){with(this){var clk="Btn_clickCB("+this.instIndex+');return false;"';var clcbs='onclick="'+clk+'" ';if(_pe._ie){clcbs+='ondblclick="'+clk+'" '}var url1=_pe._images+"button.gif",addPar=' style="'+extraStyle+"cursor:"+_pe._hand+";margin-left:"+margin+"px; margin-right:"+margin+'px; "'+clcbs+" ",tip=_attr("title",tooltip),idText="theBttn"+id,bg=_backImgOffset(url1,0,42),idIcon="theBttnIcon"+id;var lnkB="<a "+_attr("id",idText)+" "+tip+" "+_attr("tabindex",tabIndex)+' href="javascript:void(0)" class="wizbutton">';var l=(label!=null);var im=(this.url?('<td align="'+(l?(this.imgRight?"right":"left"):"center")+'" style="'+bg+'" width="'+(!l&&(width!=null)?width+6:w+6)+'">'+(l?"":lnkB)+_simpleImgOffset(url,w,h,this.isGray?disDs:dx,this.isGray?disDy:dy,idIcon,null,(l?"":tooltip),"cursor:"+_pe._hand)+(l?"":"</a>")+"</td>"):"");return"<table "+_attr("id",id)+" "+addPar+' border="0" cellspacing="0" cellpadding="0"><tr valign="middle"><td width="5">'+_simpleImgOffset(url1,5,21,0,0)+"</td>"+(this.imgRight?"":im)+(l?("<td "+_attr("width",width)+' align="center" class="'+(this.isGray?"wizbuttongray":"wizbutton")+'" style="padding-left:3px;padding-right:3px;'+bg+'"><nobr>'+lnkB+label+"</a></nobr></td>"):"")+(this.imgRight?im:"")+'<td width="5">'+_simpleImgOffset(url1,5,21,0,21)+"</td></tr></table>"}}function Btn_setDisabled(C){var B=this,A=C?"default":_pe._hand;B.isGray=C;if(B.layer){B.txt.className=C?"wizbuttongray":"wizbutton";B.txt.style.cursor=A;B.css.cursor=A;if(B.icn){_changeSimpleOffset(B.icn,B.isGray?B.disDx:B.dx,B.isGray?B.disDy:B.dy);B.icn.style.cursor=A}}}function Btn_isDisabled(){return this.isGray}function Btn_setText(A){this.txt.innerHTML=convStr(A)}function Btn_init(){var B=this;B.oldInit();B.txt=_getLayer("theBttn"+this.id);B.icn=_getLayer("theBttnIcon"+this.id);var A=B.isGray?"wizbuttongray":"wizbutton";if(B.txt.className!=A){B.txt.className=A}}function Btn_changeImg(B,A,E,D,C,F){var G=this;if(C){G.url=C}if(B!=null){G.dx=B}if(A!=null){G.dy=A}if(E!=null){G.disDx=E}if(D!=null){G.disDy=D}if(F!=null){G.tooltip=F}if(G.icn){_changeSimpleOffset(G.icn,G.isGray?G.disDx:G.dx,G.isGray?G.disDy:G.dy,G.url,G.tooltip)}}function Btn_clickCB(A){var B=Btn_inst[A];if(B&&!B.isGray){setTimeout("Btn_delayClickCB("+A+")",1)}}function Btn_delayClickCB(index){var btn=Btn_inst[index];if(btn.cb){if(typeof btn.cb!="string"){btn.cb()}else{eval(btn.cb)}}}function newTextField(C,G,J,F,I,B,K,D,A,H){var E=newCtl(C);E.tooltip=K;E.changeCB=G;E.maxChar=J;E.keyUpCB=F;E.enterCB=I;E.noMargin=B;E.width=D==null?null:""+D+"px";E.focusCB=A;E.blurCB=H;E.getHTML=TextField_getHTML;E.getValue=TextField_getValue;E.setValue=TextField_setValue;E.intValue=TextField_intValue;E.intPosValue=TextField_intPosValue;E.select=TextField_select;E.beforeChange=null;E.wInit=E.init;E.init=TextField_init;E.oldValue="";return E}function TextField_init(){var A=this;A.wInit();A.layer.value=""+A.oldValue}function TextField_getHTML(){return'<input oncontextmenu="event.cancelBubble=true;return true" style="'+_sty("width",this.width)+(_pe._moz?"padding-left:3px;padding-right:3px;":"")+"margin-left:"+(this.noMargin?0:10)+'px" onfocus="TextField_focus(this)" onblur="TextField_blur(this)" onchange="TextField_changeCB(event,this)" onkeyup="TextField_keyUpCB(event,this);return true" type="text" '+_attr("maxLength",this.maxChar)+' ondragstart="event.cancelBubble=true;return true" onselectstart="event.cancelBubble=true;return true" class="textinputs" id="'+this.id+'" name="'+this.id+'"'+_attr("title",this.tooltip)+' value="">'}function TextField_getValue(){return this.layer.value}function TextField_setValue(A){if(this.layer){this.layer.value=""+A}else{this.oldValue=A}}function TextField_changeCB(B,A){var C=_getWidget(A);if(C.beforeChange){C.beforeChange()}if(C.changeCB){C.changeCB(B)}}function TextField_keyUpCB(B,A){var C=_getWidget(A);if(_eventGetKey(B)==13){if(C.beforeChange){C.beforeChange()}if(C.enterCB){C.enterCB(B)}return false}else{if(C.keyUpCB){C.keyUpCB(B);return true}}}function TextField_focus(A){var B=_getWidget(A);if(B.focusCB){B.focusCB()}}function TextField_blur(A){var B=_getWidget(A);if(B.beforeChange){B.beforeChange()}if(B.blurCB){B.blurCB()}}function TextField_intValue(A){var B=parseInt(this.getValue());return isNaN(B)?A:B}function TextField_intPosValue(A){var B=this.intValue(A);return(B<0)?A:B}function TextField_select(){this.layer.select()}function newDlgBox(H,F,D,A,B,G,C){var E=newCtl(H);E.title=F;E.width=D;E.height=A;E.defaultCB=B;E.cancelCB=G;E.noCloseButton=C?C:false;E.resizeable=false;E.oldKeyPress=null;E.oldMouseDown=null;E.oldCurrent=null;E.modal=null;E.hiddenVis=new Array;E.lastLink=null;E.firstLink=null;E.titleLayer=null;E.oldInit=E.init;E.oldShow=E.show;E.init=DlgBox_init;E.setResize=DlgBox_setResize;E.beginHTML=DlgBox_beginHTML;E.endHTML=DlgBox_endHTML;E.show=DlgBox_Show;E.center=DlgBox_center;E.focus=DlgBox_focus;E.setTitle=DlgBox_setTitle;E.getContainerWidth=DlgBox_getContainerWidth;E.getContainerHeight=DlgBox_getContainerHeight;_pe.DlgBox_instances[H]=E;E.modal=newCtl("modal_"+H);E.placeIframe=DlgBox_placeIframe;E.oldResize=E.resize;E.resize=DlgBox_resize;return E}function DlgBox_setResize(A,C,D,B,F){var E=this;E.resizeable=true;E.resizeCB=A;E.minWidth=C?C:50;E.minHeight=D?D:50;E.noResizeW=B;E.noResizeH=F}function DlgBox_setTitle(B){var A=this;A.title=B;if(A.titleLayer==null){A.titleLayer=_getLayer("titledialog_"+this.id)}A.titleLayer.innerHTML=_convStr(B)}function DlgBox_setCloseIcon(A,B){_changeOffset(A,0,(B==1?0:18))}function DlgBox_beginHTML(){with(this){var moveableCb=' onselectstart="return false" ondragstart="return false" onmousedown="DlgBox_down(event,\''+id+"',this,false);return false;\" ";var mdl=_pe._ie?('<img onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_pe._images+'transp.gif" id="modal_'+id+'" style="display:none;position:absolute;top:0px;left:0px;width:1px;height:1px">'):('<div onselectstart="return false" ondragstart="return false" onmousedown="_eventCancelBubble(event)" border="0" hspace="0" vspace="0" src="'+_pe._images+'transp.gif" id="modal_'+id+'" style="position:absolute;top:0px;left:0px;width:1px;height:1px"></div>');var titleBG="background-image:url('"+_pe._images+"dialogtitle.gif')";return mdl+'<a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="firstLink_'+this.id+'" href="javascript:void(0)" onfocus="DlgBox_keepFocus(\''+this.id+"');return false;\" ></a>"+_getBGIframe("dlgIF_"+id)+'<table  border="0" cellspacing="0" cellpadding="2" id="'+id+'" class="dialogbox" style="display:none;padding:0px;visibility:'+_pe._hide+";position:absolute;top:-2000px;left:-2000px;"+_sty("width",width?(""+width+"px"):null)+_sty("height",height?(""+height+"px"):null)+'"><tr><td id="dlgFirstTr_'+id+'" valign="top"><table width="100%" border="0" cellspacing="0" cellpadding="0"><tr valign="top"><td '+moveableCb+' style="cursor:move;'+titleBG+'" class="titlezone">'+_getSpace(5,18)+"</td><td "+moveableCb+' style="cursor:move;'+titleBG+'" class="titlezone" width="100%" valign="middle" align="left"><nobr><span id="titledialog_'+id+'" tabIndex="0" class="titlezone">'+_convStr(title)+'</span></nobr></td><td class="titlezone" style="'+titleBG+'">'+(noCloseButton?"":'<a href="javascript:void(0)" onclick="DlgBox_close(\''+id+'\');return false;" title="'+L_closeDialog+'">'+_imgOffset(_pe._images+"dialogelements.gif",18,18,0,18,"dialogClose_"+this.id,'onmouseover="DlgBox_setCloseIcon(this,1)" onmouseout="DlgBox_setCloseIcon(this,0)" ',L_closeDialog,"cursor:"+_pe._hand)+"</a>")+'</td></tr></table></td></tr><tr valign="top" height="100%"><td id="dlgSecTr_'+id+'" >'}}function DlgBox_endHTML(){var B=' onselectstart="return false" ondragstart="return false" onmousedown="DlgBox_down(event,\''+this.id+"',this,true);return false;\" ";var A=this.resizeable?('<tr  onselectstart="return false" height="18" valign="bottom" align="right"><td>'+_img(_pe._images+"resize.gif",14,14,null,B+' style="cursor:NW-resize" ')+"</td></tr>"):"";return"</td></tr>"+A+'</table><a style="position:absolute;left:-30px;top:-30px; visibility:hidden" id="lastLink_'+this.id+'" href="javascript:void(0)" onfocus="DlgBox_keepFocus(\''+this.id+"');return false;\" ></a>"}function DlgBox_getContainerWidth(){var A=this;return A.width-(2+2)}function DlgBox_getContainerHeight(){var A=this;return A.height-(2+18+2+2+2)}function DlgBox_close(B){var A=_pe.DlgBox_instances[B];if(A){A.show(false);if(A.cancelCB!=null){A.cancelCB()}}}function DlgBox_resizeIframeCB(A){_pe.DlgBox_instances[A].placeIframe(true,false)}function DlgBox_placeIframe(B,A){var C=this;if(C.iframe){if(B){C.iframe.resize(C.getWidth(),C.getHeight())}if(A){C.iframe.move(C.layer.offsetLeft,C.layer.offsetTop)}}}function DlgBox_resize(A,B){var C=this;C.oldResize(A,B);if(C.iframe){C.iframe.resize(A,B);if(C.firstTR){if(A!=null){C.firstTR.style.width=A-4}if(B!=null){C.secondTR.style.height=B-44}}}}function DlgBox_init(){if(this.layer!=null){return }var A=this;A.oldInit();A.modal.init();A.lastLink=newCtl("lastLink_"+A.id);A.firstLink=newCtl("firstLink_"+A.id);A.lastLink.init();A.firstLink.init();if(!A.noCloseButton){A.closeButton=_getLayer("dialogClose_"+A.id);DlgBox_setCloseIcon(A.closeButton,false)}if(_pe._moz&&!_pe._saf){A.firstTR=_getLayer("dlgFirstTr_"+A.id);A.secondTR=_getLayer("dlgSecTr_"+A.id)}A.iframe=newCtl("dlgIF_"+A.id);A.iframe.init()}function DlgBox_down(e,id,obj,isResize){_pe._dlgResize=isResize;var o=_pe.DlgBox_instances[id],lyr=o.layer,mod=o.modal.layer;lyr.onmousemove=mod.onmousemove=eval("DlgBox_move");lyr.onmouseup=mod.onmouseup=eval("DlgBox_up");lyr.dlgStartPosx=mod.dlgStartPosx=parseInt(lyr.style.left);lyr.dlgStartPosy=mod.dlgStartPosy=parseInt(lyr.style.top);lyr.dlgStartx=mod.dlgStartx=_eventGetX(e);lyr.dlgStarty=mod.dlgStarty=_eventGetY(e);lyr.dlgStartw=mod.dlgStartw=o.getWidth();lyr.dlgStarth=mod.dlgStarth=o.getHeight();lyr._widget=mod._widget=o.widx;_pe._theLYR=lyr;_eventCancelBubble(e);if(lyr.setCapture){lyr.setCapture(true)}}function DlgBox_move(C){var E=_pe._theLYR,D=_getWidget(E);if(D){if(_pe._dlgResize){var G=Math.max(D.minWidth,E.dlgStartw+_eventGetX(C)-E.dlgStartx);var B=Math.max(D.minHeight,E.dlgStarth+_eventGetY(C)-E.dlgStarty);D.resize(D.noResizeW?null:G,D.noResizeH?null:B);if(D.firstTR){if(!D.noResizeW){D.firstTR.style.width=G-4}if(!D.noResizeH){D.secondTR.style.height=B-44}}if(D.resizeCB){D.resizeCB(G,B)}}else{var A=Math.max(0,E.dlgStartPosx-E.dlgStartx+_eventGetX(C));var F=Math.max(0,E.dlgStartPosy-E.dlgStarty+_eventGetY(C));A=Math.min(Math.max(10,_winWidth()-10),A);F=Math.min(Math.max(10,_winHeight()-18),F);D.iframe.move(A,F);D.move(A,F)}}_eventCancelBubble(C);return false}function DlgBox_up(C){var D=_getWidget(_pe._theLYR),A=D.layer,B=D.modal.layer;A.onmousemove=B.onmousemove=null;A.onmouseup=B.onmouseup=null;if(A.releaseCapture){A.releaseCapture()}_pe._theLYR=null}function DlgBox_keypress(B){var C=_pe.DlgBox_current;if(C!=null){switch(_eventGetKey(B)){case 13:var A=_pe._ie?window.event.srcElement.id:B.target.id;if((A=="insRepText"||A=="renRepText")&&(C.defaultCB!=null)){C.defaultCB();return false}if(C.yes&&!C.no){C.defaultCB();return false}break;case 27:C.show(false);if(C.cancelCB!=null){C.cancelCB()}return false;break;case 8:return _isTextInput(_pe._ie?window.event:B);break}}}function DlgBoxResizeModals(B){for(var A in _pe.DlgBox_modals){m_sty=_pe.DlgBox_modals[A];m_sty.width=_documentWidth();m_sty.height=_documentHeight()}}function DlgBox_center(){var C=this,A=_getScrollY(),B=_getScrollX();C.height=C.layer.offsetHeight;C.width=C.layer.offsetWidth;C.move(Math.max(0,B+(_winWidth()-C.width)/2),Math.max(0,A+(_winHeight()-C.height)/2));C.placeIframe(true,true)}function DlgBox_Show(sh){with(this){m_sty=modal.css;l_sty=css;if(sh){oldCurrent=_pe.DlgBox_current;_pe.DlgBox_current=this;if(_pe._ie){oldKeyPress=document.onkeydown;document.onkeydown=eval("window.DlgBox_keypress")}else{document.addEventListener("keydown",eval("window.DlgBox_keypress"),false)}oldMouseDown=document.onmousedown;document.onmousedown=null;_disableAllInputs()}else{_pe.DlgBox_current=oldCurrent;oldCurrent=null;if(_pe._ie){document.onkeydown=oldKeyPress}else{document.removeEventListener("keydown",eval("window.DlgBox_keypress"),false)}document.onmousedown=oldMouseDown;_restoreAllDisabledInputs()}var sameState=(layer.isShown==sh);if(sameState){return }layer.isShown=sh;if(sh){if(window.DialogBoxWidget_zindex==null){window.DialogBoxWidget_zindex=1000}this.iframe.css.zIndex=window.DialogBoxWidget_zindex++;m_sty.zIndex=window.DialogBoxWidget_zindex++;l_sty.zIndex=window.DialogBoxWidget_zindex++;_pe.DlgBox_modals[_pe.DlgBox_modals.length]=m_sty;m_sty.display="";l_sty.display="block";this.iframe.setDisplay(true);DlgBoxResizeModals();this.height=layer.offsetHeight;this.width=layer.offsetWidth;if(_isHidden(layer)){this.center()}if(this.firstTR){this.firstTR.style.width=this.getWidth()-4;this.secondTR.style.height=this.getHeight()-44}if(this.resizeCB){this.resizeCB(this.width,this.height)}}else{var l=_pe.DlgBox_modals.length=Math.max(0,_pe.DlgBox_modals.length-1);m_sty.width="1px";m_sty.height="1px";m_sty.display="none";l_sty.display="none";move(-2000,-2000);this.iframe.setDisplay(false)}modal.show(sh);firstLink.show(sh);lastLink.show(sh);oldShow(sh);if(_pe.DlgBox_current!=null&&sh==true){_pe.DlgBox_current.focus()}}}function DlgBox_keepFocus(B){var A=_pe.DlgBox_instances[B];if(A){A.focus()}}function DlgBox_focus(){with(this){if(titleLayer==null){titleLayer=_getLayer("titledialog_"+id)}if(titleLayer.focus){titleLayer.focus()}}}var isJava=false;var isNetscape=navigator.appName.indexOf("Netscape")!=-1;var LEFT_ARROW_KEY=37;var RIGHT_ARROW_KEY=39;var ENTER_KEY=13;function promptengine_encodePrompt(A){if(isJava){return encodeURIComponent(A)}else{return promptengine_urlEncode(A)}}function promptengine_addDiscreteValue(D,I,H){var B=document.getElementById(D);var G=document.getElementById(H+"DiscreteValue");var A=G;var K=G.type.toLowerCase();var J=false;if(K!="text"&&K!="hidden"&&K!="password"){A=G.options[G.selectedIndex];J=true}var C=A.value;if(!promptengine_checkValue(C,I)){_safeSetFocus(G);return false}var E=document.getElementById(H+"ListBox");PE_clearSel(E);var F=promptengine_findOptionInList(E,C);if(F<0){F=E.length;E.options[F]=new Option(((A.text)?A.text:C),C,false,false)}E.options[F].selected=true;_safeSetFocus(G);if(G.select){G.select()}if(J&&G.selectedIndex<G.length-1){G.selectedIndex=G.selectedIndex+1}}function PE_clearSel(B){var A=0,C=B.length;if(B.type=="select-one"){A=B.selectedIndex;if(A<0){return }C=A+1}while(A<C){B.options[A++].selected=false}}function promptengine_addValueFromPickList(C,B,A){return PE_addValues(C,B,A,false)}function promptengine_addAllValues(C,B,A){return PE_addValues(C,B,A,true)}function PE_addValues(C,E,M,D){var O=document.getElementById(M+"AvailableList");var S=document.getElementById(M+"ListBox");var F=O.length;if(F==0){return false}var J=S.length;var G=O.options;var L=S.options;var N=new Array(F);var R=new Array(J);var P=false;var B=-1;for(var Q=0;Q<F;Q++){if(D||G[Q].selected){var K=G[Q].value;var T=promptengine_findOptionInList(S,K,G[Q].text);if(T<0){N[Q]=K}else{R[T]=K}P=true;if(!D){B=Q}}}if(!P){return false}var I=PE_getLB(S);for(var Q=0;Q<J;Q++){var A=L[Q];I.add(A.value,A.text,R[Q]!=null)}var H=false;for(var Q=0;Q<F;Q++){if(N[Q]){var A=G[Q];I.add(A.value,A.text,true);H=true}}I.update();if(!D&&B>=0&&B+1<F){PE_clearSel(O);G[B+1].selected=true}return H}function promptengine_addRangeValue(A,E,K){var C=document.getElementById(K+"SelectLowerRangeValue");var I=document.getElementById(K+"SelectUpperRangeValue");lowerBound=document.getElementById(K+"LowerBound");upperBound=document.getElementById(K+"UpperBound");if(lowerBound.type.toLowerCase()!="text"&&lowerBound.type.toLowerCase()!="hidden"&&lowerBound.type.toLowerCase()!="password"){lowerBound=lowerBound.options[lowerBound.selectedIndex];upperBound=upperBound.options[upperBound.selectedIndex]}lowerUnBounded=document.getElementById(K+"NoLBoundCheck").checked;upperUnBounded=document.getElementById(K+"NoUBoundCheck").checked;lvalue=uvalue="";if(!lowerUnBounded){if(!promptengine_checkRangeBoundValue(lowerBound.value,E)){if(lowerBound.focus&&lowerBound.type.toLowerCase()!="hidden"){lowerBound.focus()}return false}lvalue=lowerBound.value}if(!upperUnBounded){if(!promptengine_checkRangeBoundValue(upperBound.value,E)){if(upperBound.focus&&upperBound.type.toLowerCase()!="hidden"){upperBound.focus()}return false}uvalue=upperBound.value}var B="";var D="";var L=false;if(C!=null&&lvalue!=null&&lvalue.length>0){var H=C.length;for(var J=0;J<H;J++){var G=C.options[J].value;if(G!=null&&G.length>0&&G==lvalue){B=C.options[J].text;L=true;break}}}if(!L){B=(lowerBound.text&&!lowerUnBounded)?lowerBound.text:lvalue}L=false;if(I!=null&&uvalue!=null&&uvalue.length>0){var H=I.length;for(var J=0;J<H;J++){var G=I.options[J].value;if(G!=null&&G==uvalue){D=I.options[J].text;L=true;break}}}if(!L){D=(upperBound.text&&!upperUnBounded)?upperBound.text:uvalue}lowerChecked=document.getElementById(K+"LowerCheck").checked;upperChecked=document.getElementById(K+"UpperCheck").checked;G=(lowerChecked&&!lowerUnBounded)?"[":"(";if(!lowerUnBounded){G+=(lvalue)}G+="_crRANGE_";if(!upperUnBounded){G+=(uvalue)}G+=(upperChecked&&!upperUnBounded)?"]":")";display=(lowerChecked&&!lowerUnBounded)?"[":"(";display+=B;display+=" .. ";display+=D;display+=(upperChecked&&!upperUnBounded)?"]":")";promptEntry=new Option(display,G,false,false);theList=document.getElementById(K+"ListBox");var F=promptengine_findOptionInList(theList,G);if(F>-1){theList.selectedIndex=F}else{theList.options[theList.length]=promptEntry}return true}function promptengine_findOptionInList(E,C){if(E==null||C==null){return -1}var D=E.length,B=E.options;for(var A=0;A<D;A++){if(B[A].value==C){return A}}return -1}function promptengine_onNoBoundCheckClicked(B,A,C){if(C==0){if(document.getElementById(A+"NoLBoundCheck").checked){document.getElementById(A+"NoUBoundCheck").disabled=true;document.getElementById(A+"LowerCheck").disabled=true;document.getElementById(A+"LowerBound").disabled=true;if(document.getElementById(A+"SelectLowerRangeValue")!=null){document.getElementById(A+"SelectLowerRangeValue").disabled=true}}else{document.getElementById(A+"NoUBoundCheck").disabled=false;document.getElementById(A+"LowerCheck").disabled=false;document.getElementById(A+"LowerBound").disabled=false;if(document.getElementById(A+"SelectLowerRangeValue")!=null){document.getElementById(A+"SelectLowerRangeValue").disabled=false}}}else{if(C==1){if(document.getElementById(A+"NoUBoundCheck").checked){document.getElementById(A+"NoLBoundCheck").disabled=true;document.getElementById(A+"UpperCheck").disabled=true;document.getElementById(A+"UpperBound").disabled=true;if(document.getElementById(A+"SelectUpperRangeValue")!=null){document.getElementById(A+"SelectUpperRangeValue").disabled=true}}else{document.getElementById(A+"NoLBoundCheck").disabled=false;document.getElementById(A+"UpperCheck").disabled=false;document.getElementById(A+"UpperBound").disabled=false;if(document.getElementById(A+"SelectUpperRangeValue")!=null){document.getElementById(A+"SelectUpperRangeValue").disabled=false}}}}}function promptengine_onSetNullCheckClicked(B,A){if(document.getElementById(A+"NULL").checked){if(document.getElementById(A+"DiscreteValue")!=null){document.getElementById(A+"DiscreteValue").disabled=true}if(document.getElementById(A+"SelectValue")!=null){document.getElementById(A+"SelectValue").disabled=true}}else{if(document.getElementById(A+"DiscreteValue")!=null){document.getElementById(A+"DiscreteValue").disabled=false}if(document.getElementById(A+"SelectValue")!=null){document.getElementById(A+"SelectValue").disabled=false}}}function promptengine_selectValue(B,A,D){if(document.getElementById(A).selectedIndex<0){return false}selectedOption=document.getElementById(A).options[document.getElementById(A).selectedIndex];if(selectedOption.value==null&&document.getElementById(D).value==null){return false}var C=true;if(selectedOption.value==document.getElementById(D).value){C=false}document.getElementById(D).value=selectedOption.value;return C}function promptengine_hasValueInTextBox(B,A){if(document.getElementById(A).value==null){return false}return true}function promptengine_setCascadingPID(C,A,B){valueField=document.getElementById(A);curVal=valueField.value;if(curVal.length>0){curVal+="&"}curVal+="cascadingPID="+B;valueField.value=curVal;return true}function PE_removeValue(C,G,K){var F=document.getElementById(G+"ListBox");var A=F.options;var I=F.length;if(I==0){return false}var E=false;var J=-1;var H=PE_getLB(F);for(var D=0;D<I;D++){if(!K){var B=A[D];if(!B.selected){H.add(B.value,B.text);continue}J=D}E=true}if(!E){return false}H.update();if(J>=0){F=document.getElementById(G+"ListBox");if(J<F.length){F.options[J].selected=true}else{if(J==F.length&&J>0){F.options[J-1].selected=true}}}return true}function promptengine_removeValue(B,A){return PE_removeValue(B,A,false)}function promptengine_onRemoveValue(B,A){promptengine_removeValue(B,A)}function promptengine_removeAllValues(B,A){return PE_removeValue(B,A,true)}function promptengine_onRemoveAllValues(B,A){promptengine_removeAllValues(B,A)}function promptengine_updateValueField(C,A,B,D){valueField=document.getElementById(A);curVal=valueField.value;if(curVal.length>0){curVal+="&"}var E=promptengine_encodeValueField(D);curVal+=B+"="+E;valueField.value=curVal;return true}function promptengine_resetValueField(B,A){valueField=document.getElementById(A);valueField.value=""}function promptengine_updateDiscreteValue(G,A,B,E,D,F){var H="";if(document.getElementById(B+"NULL")!=null&&document.getElementById(B+"NULL").checked){H="_crNULL_"}else{valueField=document.getElementById(B+"DiscreteValue");if(valueField.type.toLowerCase()!="text"&&valueField.type.toLowerCase()!="hidden"&&valueField.type.toLowerCase()!="password"){H=valueField.options[valueField.selectedIndex].value}else{H=valueField.value}if(!F&&(H==null||H.length==0)){return promptengine_updateValueField(G,A,B,"")}if(D&&!promptengine_checkValue(H,E)){if(valueField.focus&&valueField.type.toLowerCase()!="hidden"){valueField.focus()}else{var C=document.getElementById(B+"SelectValue");if(C!=null&&C.focus){C.focus()}}return false}}return promptengine_updateValueField(G,A,B,H)}function promptengine_updateRangeValue(G,A,B,E,D,F){if(document.getElementById(B+"NULL")!=null&&document.getElementById(B+"NULL").checked){value="_crNULL_"}else{lowerBound=document.getElementById(B+"LowerBound");upperBound=document.getElementById(B+"UpperBound");if(lowerBound.type.toLowerCase()!="text"&&lowerBound.type.toLowerCase()!="hidden"&&lowerBound.type.toLowerCase()!="password"){lowerBound=lowerBound.options[lowerBound.selectedIndex];upperBound=upperBound.options[upperBound.selectedIndex]}lowerUnBounded=document.getElementById(B+"NoLBoundCheck").checked;upperUnBounded=document.getElementById(B+"NoUBoundCheck").checked;lowerChecked=document.getElementById(B+"LowerCheck").checked;upperChecked=document.getElementById(B+"UpperCheck").checked;uvalue=lvalue="";if(!F&&(lowerBound.value==null||lowerBound.value.length==0||lowerUnBounded)&&(upperBound.value==null||upperBound.value.length==0||upperUnBounded)){return promptengine_updateValueField(G,A,B,"")}if(!lowerUnBounded){if(D&&!promptengine_checkRangeBoundValue(lowerBound.value,E)){if(lowerBound.focus&&lowerBound.type.toLowerCase()!="hidden"){lowerBound.focus()}else{var C=document.getElementById(B+"SelectLowerRangeValue");if(C!=null&&C.focus){C.focus()}}return false}lvalue=lowerBound.value}if(!upperUnBounded){if(D&&!promptengine_checkRangeBoundValue(upperBound.value,E)){if(upperBound.focus&&upperBound.type.toLowerCase()!="hidden"){upperBound.focus()}else{var C=document.getElementById(B+"SelectUpperRangeValue");if(C!=null&&C.focus){C.focus()}}return false}uvalue=upperBound.value}value=(lowerChecked&&!lowerUnBounded)?"[":"(";if(!lowerUnBounded){value+=lvalue}value+="_crRANGE_";if(!upperUnBounded){value+=uvalue}value+=(upperChecked&&!upperUnBounded)?"]":")"}return promptengine_updateValueField(G,A,B,value)}function promptengine_updateMultiValue(G,A,B,E,D,F){values=document.getElementById(B+"ListBox").options;value="";if(document.getElementById(B+"NULL")!=null&&document.getElementById(B+"NULL").checked){value="_crNULL_"}else{if(values.length==0){if(D&&F){var C=document.getElementById(B+"ListBox");if(C!=null&&C.focus){C.focus()}return false}value="_crEMPTY_"}else{for(i=0;i<values.length;i++){if(i!=0){value+="_crMULT_"}value+=values[i].value}}}return promptengine_updateValueField(G,A,B,value)}var regNumber=/^(\+|-)?((\d+(\.|,|'| |\xA0)?\d*)+|(\d*(\.|,| |\xA0)?\d+)+)$/;var regCurrency=regNumber;var regDate=/^(D|d)(A|a)(T|t)(E|e) *\( *\d{4} *, *(0?[1-9]|1[0-2]) *, *((0?[1-9]|[1-2]\d)|3(0|1)) *\)$/;var regDateTime=/^(D|d)(A|a)(T|t)(E|e)(T|t)(I|i)(M|m)(E|e) *\( *\d{4} *, *(0?[1-9]|1[0-2]) *, *((0?[1-9]|[1-2]\d)|3(0|1)) *, *([0-1]?\d|2[0-3]) *, *[0-5]?\d *, *[0-5]?\d *\)$/;var regTime=/^(T|t)(I|i)(M|m)(E|e) *\( *([0-1]?\d|2[0-3]) *, *[0-5]?\d *, *[0-5]?\d *\)$/;var regDateTimeHTML=/^ *\d{4} *- *(0?[1-9]|1[0-2]) *- *((0?[1-9]|[1-2]\d)|3(0|1)) *  *([0-1]?\d|2[0-3]) *: *[0-5]?\d *: *[0-5]?\d *$/;var regDateHTML=/^ *\d{4} *- *(0?[1-9]|1[0-2]) *- *((0?[1-9]|[1-2]\d)|3(0|1)) *$/;var regTimeHTML=/^ *([0-1]?\d|2[0-3]) *: *[0-5]?\d *: *[0-5]?\d *$/;function promptengine_getDateSpec(){var A=promptengine_getDatePattern();A=A.replace("Y",L_YYYY);A=A.replace("M",L_MM);A=A.replace("D",L_DD);return A}function promptengine_checkValue(E,C){if(E==null){return false}if(E=="_crNULL_"){return true}if(C==_pe._nm&&!regNumber.test(E)){if(E.length>0){alert(L_BadNumber)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}else{if(C==_pe._cy&&!regCurrency.test(E)){if(E.length>0){alert(L_BadCurrency)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}else{if(C==_pe._da){var D=promptengine_getDateRegex();if((D==null||!D.test(E))&&!regDate.test(E)&&!regDateHTML.test(E)){if(E.length>0){var A=L_BadDate.replace("%1",promptengine_getDateSpec());alert(A)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}else{if(C==_pe._dt){var D=promptengine_getDateTimeRegex();if((D==null||!D.test(E))&&!regDateTime.test(E)&&!regDateTimeHTML.test(E)){if(E.length>0){var B=L_BadDateTime.replace("%1",promptengine_getDateSpec());alert(B)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}else{if(C==_pe._tm&&!regTime.test(E)&&!regTimeHTML.test(E)){if(E.length>0){alert(L_BadTime)}else{alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue)}return false}}}}}return true}function promptengine_checkRangeBoundValue(B,A){if(B==null||B.length==0){alert((typeof L_Empty)!="undefined"?L_Empty:L_NoValue);return false}return promptengine_checkValue(B,A)}function promptengine_isSubmitEvent(B){var A=false;if(isNetscape){if(B.which==ENTER_KEY&&(B.target.type=="text"||B.target.type=="password")){A=true}}else{if(window.event.keyCode==ENTER_KEY&&(window.event.srcElement.type=="text"||window.event.srcElement.type=="password")){A=true}}if(A){_eventCancelBubble(B)}return A}function promptengine_isEnterKey(B){var A=false;if(isNetscape){if(B.which==ENTER_KEY&&B.target.tagName.toLowerCase()!="a"){A=true}}else{if(window.event.keyCode==ENTER_KEY&&window.event.srcElement.tagName.toLowerCase()!="a"){A=true}}if(A){_eventCancelBubble(B)}return A}function promptengine_urlEncode(A){var C=new String("");for(var B=0;B<A.length;B++){var D=A.charAt(B);switch(D){case"%":C+="%25";break;case"+":C+="%2B";break;case" ":C+="%20";break;case"<":C+="%3C";break;case">":C+="%3E";break;case'"':C+="%22";break;case"'":C+="%27";break;case"#":C+="%23";break;case"{":C+="%7B";break;case"}":C+="%7D";break;case"|":C+="%7C";break;case"\\":C+="%5C";break;case"^":C+="%5E";break;case"~":C+="%7E";break;case"`":C+="%60";break;case"[":C+="%5B";break;case"]":C+="%5D";break;case";":C+="%3B";break;case"/":C+="%2F";break;case"?":C+="%3F";break;case":":C+="%3A";break;case"@":C+="%40";break;case"=":C+="%3D";break;case"&":C+="%26";break;default:C+=D;break}}return C}function promptengine_CancelRightClick(A){if(isNetscape){if(A.target.type!="text"&&A.target.type!="textarea"){A.preventDefault();A.cancelBubble=true;return true}}else{if(window.event.srcElement.type!="text"&&window.event.srcElement.type!="textarea"){window.event.cancelBubble=true;window.event.returnValue=false}}}function promptengine_showHidePromptByKey(B,D,G,F,A){var C=false;var E=document.getElementById(B);if(E==null){return }if(isNetscape){if((A.which==LEFT_ARROW_KEY&&E.style.display=="")||(A.which==RIGHT_ARROW_KEY&&E.style.display=="none")){C=true}}else{if((window.event.keyCode==LEFT_ARROW_KEY&&E.style.display=="")||(window.event.keyCode==RIGHT_ARROW_KEY&&E.style.display=="none")){C=true}}if(C==true){promptengine_showHidePrompt(B,D,G,F,A)}}function promptengine_showHidePrompt(B,C,G,F,A){var E;E=document.getElementById(C);if(E!=null&&B!=null){if(!E.origImage){E.origImage=E.src}var D=document.getElementById(B);if(D!=null){if(D.style.display==""){D.style.display="none"}else{D.style.display=""}if(!E.changed||E.changed==false){E.src=F;E.changed=true}else{E.src=G;E.changed=false}}}}function promptengine_scrollTo(D){if(!D){return }var A=_getScrollY(),C=_getScrollX();if(D.form){var E=D.form.offsetHeight,B=D.form.clientHeight,F=_getPos(D,D.form).y;D.form.scrollLeft=C;if(F<A){D.form.scrollTop=F}else{if(F+E>A+B){D.form.scrollTop=Math.max(F,F+E-B)}}}else{var E=D.offsetHeight,B=_winHeight(),F=_getPos(D).y;if(F<A){window.scrollTo(C,F)}else{if(F+E>A+B){window.scrollTo(C,Math.max(F,F+E-B))}}}}function doNothing(){}function promptengine_anchorOnKeyPress(C){var A=C?C:window.event;var B=A.srcElement?A.srcElement:A.target;if(A.keyCode==13&&B.onclick){B.onclick.apply(B,[C])}return true}function promptengine_encodeUTF8(B){var A=[];var E=B.length;for(var D=0;D<E;D++){var F=B.charCodeAt(D);if(F<128){A.push(F)}else{if(F<2048){A.push((F>>6)|192);A.push(F&63|128)}else{if(F<55296||F>=57344){A.push((F>>12)|224);A.push((F>>6)&63|128);A.push(F&63|128)}else{if(F<56320){var C=B.charCodeAt(D+1);if(isNaN(C)||C<56320||C>=57344){A.push(239,191,189);continue}D++;val=((F&1023)<<10)|(C&1023);val+=65536;A.push((val>>18)|240);A.push((val>>12)&63|128);A.push((val>>6)&63|128);A.push(val&63|128)}else{A.push(239,191,189)}}}}}return A}function promptengine_encodeBASE64(A){var B="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";var G=[];var F,D,C,L,J,I,H;var E=0,K=A.length;while(E<K){F=A[E++];D=A[E++];C=A[E++];L=F>>2;J=((F&3)<<4)|(D>>4);I=((D&15)<<2)|(C>>6);H=C&63;if(isNaN(D)){I=H=64}else{if(isNaN(C)){H=64}}G.push(B.charAt(L));G.push(B.charAt(J));G.push(B.charAt(I));G.push(B.charAt(H))}return G.join("")}function promptengine_encodeValueField(A){return promptengine_encodePrompt(promptengine_encodeBASE64(promptengine_encodeUTF8(A)))}if(typeof (bobj)=="undefined"){bobj={}}if(typeof (bobj.prompt)=="undefined"){bobj.prompt={}}bobj.prompt.Calendar=function(D,B,A,C){this.locale=A;this.crystalreportviewerPath=C+"/../";this.loadFiles();this.formName=D;this.dateFormat=B;this.dateTimeFormat=B+" H:mm:ss";this.isDateTime=false};bobj.prompt.Calendar.prototype={show:function(C,A){this.calendar=bobj.crv.Calendar.getInstance();this.input=document.getElementById(A);var B=C.target?C.target:C.srcElement;var D=this._getPosition(B);this._setValue(this.input.value);this._setSignals(true);this.calendar.setShowTime(this.isDateTime);this.calendar.show(true,D.x,D.y)},setIsDateTime:function(A){this.isDateTime=A},_getPosition:function(B){var A={x:0,y:0};var D={x:0,y:0};var C=B;while(C!=null){D.x+=C.offsetLeft;D.y+=C.offsetTop;C=C.offsetParent}var C=B;while(C!=null&&C.tagName.toLowerCase()!="body"){A.x+=C.scrollLeft;A.y+=C.scrollTop;C=C.parentNode}return{x:D.x-A.x,y:D.y-A.y}},_setValue:function(B){var A=this._getDateValue(B);if(!A){A=new Date()}this.calendar.setDate(A)},_onOkayClick:function(A){this._setFieldValue(A)},_setFieldValue:function(A){if(this.input){this.input.value=this._getStringValue(A)}},_onHide:function(){this._removeSignals()},_getStringValue:function(A){var B=this.isDateTime?this.dateTimeFormat:this.dateFormat;return bobj.external.date.formatDate(A,B)},_getDateValue:function(A){var B=this.isDateTime?this.dateTimeFormat:this.dateFormat;return bobj.external.date.getDateFromFormat(A,B)},_setSignals:function(B){var A=B?MochiKit.Signal.connect:MochiKit.Signal.disconnect;A(this.calendar,this.calendar.Signals.OK_CLICK,this,"_onOkayClick");A(this.calendar,this.calendar.Signals.ON_HIDE,this,"_onHide")},_removeSignals:function(){this._setSignals(false)},loadJsResources:function(){var B=["js/external/date.js","js/MochiKit/Base.js","js/MochiKit/DOM.js","js/MochiKit/Style.js","js/MochiKit/Signal.js","js/dhtmllib/dom.js","prompting/js/initDhtmlLib.js","js/dhtmllib/palette.js","js/dhtmllib/menu.js","js/crviewer/html.js","js/crviewer/common.js","js/crviewer/Calendar.js"];for(var A=0;A<B.length;A++){this.loadJsFile(B[A])}},loadJsFile:function(A){document.write('<script src="'+this.crystalreportviewerPath+A+'" language="javascript"><\/script>')},loadLocaleStrings:function(){var A=["js/dhtmllib/language/en/labels.js","js/crviewer/strings_en.js"];var D="_";if(this.locale.indexOf("-")>0){D="-"}var C=this.locale.split(D);if(C.length>=1){A.push("js/dhtmllib/language/"+C[0]+"/labels.js");A.push("js/crviewer/strings_"+C[0]+".js")}if(C.length>=2){A.push("js/dhtmllib/language/"+C[0]+"_"+C[1]+"/labels.js");A.push("js/crviewer/strings_"+C[0]+"_"+C[1]+".js")}for(var B=0;B<A.length;B++){this.loadJsFile(A[B])}},loadFiles:function(){if(typeof (bobj.crv)=="undefined"||typeof (bobj.crv.Calendar)=="undefined"){window.promptengine_skin=this.crystalreportviewerPath+"js/dhtmllib/images/skin_standard/";window.promptengine_style=this.crystalreportviewerPath+"js/crviewer/images/";window.promptengine_lang=this.locale;this.loadLocaleStrings();this.loadJsResources()}}};