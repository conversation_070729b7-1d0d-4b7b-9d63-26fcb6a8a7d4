﻿// <copyright file="ContianerDE.cs" company="TCIS">
// Copyright (c) .  All rights reserved.  Reproduction or transmission in 
// whole or in part, in any form or by any means, electronic, mechanical or 
// otherwise, is prohibited without the prior written consent of the copyright 
// owner.
// </copyright>
//
// <summary>
// </summary>
// <remarks>
// </remarks>
// <history>
// Date         Release         Task            Who         Summary
// ===================================================================================
// 24/09/2012   1.0.0.0                         LKTL        Created
// </history>

using System;
using System.Collections.Generic;
using System.Linq;
using Com.TCIS.TopoVn.ValueObjects;
using System.Data;
using Com.TCIS.TopoVn.Common;
using Oracle.DataAccess.Client;
using Com.TCIS.TopoVn;
using Com.TCIS.TopoVn.ValueObjects.Dto;
using System.Text;
using System.Threading;

namespace Com.TCIS.TopoVn.Dal
{
    public class ContainerDE : IContainerDE
    {

        #region ITEM & Sub ITEM methods

        /// <summary>
        /// 
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns></returns>
        public string GetCommentAggerate(int itemKey, bool isIgnoreItemComment = false, bool isIncludeCommentEipa = true)
        {
            try
            {
                string isIgnore = isIgnoreItemComment ? BooleanType.Yes : BooleanType.No;
                string isIncludeEipa = isIncludeCommentEipa ? BooleanType.Yes : BooleanType.No;

                string sql = $@"SELECT 
                                    GET_ITEM_REMARKS(iITEM_KEY  => {itemKey}, isIgnoreComment => '{isIgnore}, isIncludeCommentEipa => {isIncludeEipa}')
                                FROM DUAL";
                return OraDatabase.ExecuteScalar(sql).ToStringEx();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return string.Empty;
        }


        /// <summary>
        /// Concatenate damage data to a string. If displayOnlyCode equals true, return string with codes, instead of description
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="displayOnlyCode"></param>
        /// <param name="displayOnlyUnFixed"></param>
        /// <param name="isLocalLang"></param>
        /// <returns></returns>
        public string GetDamageAggerate(int itemKey, bool displayOnlyCode, bool displayOnlyUnFixed = true, bool isLocalLang = true)
        {
            try
            {
                string sql =
                    $@"SELECT GET_ITEM_DAMAGE_STRING({itemKey}, '{(displayOnlyCode ? 'Y' : ' ')}', '{(displayOnlyUnFixed ? 'Y' : ' ')}', '{(isLocalLang ? 'Y' : ' ')}') FROM DUAL";
                return OraDatabase.ExecuteScalar(sql).ToStringEx();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return string.Empty;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <param name="lineOper"></param>
        /// <param name="isImp"></param>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewReeferMonitor> FilterReeferMonitor(string tfcCode, string lineOper, bool isImp, DateTime fromDate, DateTime toDate)
        {
            List<ViewReeferMonitor> reeferMonitorList = new List<ViewReeferMonitor>();
            try
            {
                if (string.IsNullOrWhiteSpace(tfcCode))
                {

                }

                var sql = string.Format(@"
                                       
                        SELECT * FROM VIEW_REEFER_MONITOR t
                        WHERE 
	                        (nvl('{1}', ' ') = ' '  OR ('{0}' = 'Y' AND t.ARR_CAR = '{1}' AND t.ARR_BY = 'V') OR ('{0}' <> 'Y' AND t.DEP_CAR = '{1}' AND t.DEP_BY = 'V'))
	                        AND (nvl('{2}', ' ') = ' ' OR t.LINE_OPER = '{2}')
	                        AND (
                                    --(TO_CHAR({3}, 'YYYY') = '1900' OR TO_CHAR({4}, 'YYYY') = '1900')
                                    --OR 
                                    (t.INSPECT_TS >= {3} AND t.INSPECT_TS <= {4})
                                 )    
                                    
                                        ", isImp ? BooleanType.Yes : BooleanType.Blank, tfcCode, lineOper, fromDate.ToOracleDateString(), toDate.ToOracleDateString());

                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from row in queryResult.AsEnumerable()
                              select new ViewReeferMonitor(row));

                reeferMonitorList = result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return reeferMonitorList;
        }

        /// <summary>
        /// Get container data from ITEM table. 
        /// If the given container number is not found in system or it is no longer active in system, 
        /// a null value will be returned
        /// </summary>
        /// <param name="containerNo"></param>
        /// <returns>
        ///         - Return an Item object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public Item GetItem(string containerNo)
        {
            Item activeContainer = null;
            string sql = $@"
                            
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, 
                                                LL_DISCH_PORT, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT,
                                                EIR_ID, ACC_NO, CMID, EXPIRY_DATE, DOMESTIC,
                                                CUST_REG_NO, DELIVERY_METHOD, DIRECT_IMPORT_DELIVERY, DELIVERY_METHOD,
                                                INTER_MOVE_CODE, IS_CFS, ITEM_SIZE, ITEM_STATUS, ORIGIN_TERMINAL,
                                                SEAL_CHECK_REQ, SHIPPER, SPECIAL_GEAR, SPECIAL_HDL_CODE,
                                                SPECIAL_USED, TRUCK_BARGE_INTERNAL, TURNING_CHE, 
                                                BB_ID, COPRAR_DISCHARGE, CTR_OWNER, 
                                                PKG_UNIT, PKG_TYPE, RECEIVAL_METHOD, RA_KEY, EXIT_VES_CD, EXIT_VOYAGE, ORG_ARR_CAR, ORG_IN_VOYAGE
                                        FROM ITEM
                                        WHERE ITEM_NO = '{containerNo}'
                                              AND (HIST_FLG = ' ' OR HIST_FLG = 'N')

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new Item(item);

            if (result.Any())
            {
                activeContainer = result.First();
            }

            return activeContainer;
        }

        public List<Item> GetAllLastItemDelivery(string containerNo, int dayPeriods, string fel = "F")
        {
            try
            {
                var depTs = DateTime.Now.AddDays(-1 * dayPeriods);

                var sql =
                    $"SELECT * FROM ITEM WHERE ITEM_NO='{containerNo}' AND HIST_FLG='Y' AND DEP_TS >= {depTs.ToOracleDateString()} AND FEL = '{fel}' ORDER BY Dep_Ts DESC";

                if (fel.IsEmpty())
                {
                    sql =
                        $"SELECT * FROM ITEM WHERE ITEM_NO='{containerNo}' AND HIST_FLG='Y' AND DEP_TS >= {depTs.ToOracleDateString()} ORDER BY Dep_Ts DESC";

                }
                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from record in queryResult.AsEnumerable()
                             select new Item(record);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }
        /// <summary>
        /// Get container data from ITEM table. 
        /// by Item Key
        /// </summary>
        /// <history>
        /// thhung edit 25-07-2013, thêm option includeHistory
        /// </history>
        public Item GetItem(int itemKey, bool includeHistory = false)
        {
            Item activeContainer = null;
            string sql;
            if (includeHistory)//lấy cả cont bao gom History
            {
                sql = $@"
                            
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, CUST_RELEASE_MARK, EXPORT_CONFIRMED, ARR_CAR_MANIFEST, 
                                                LL_DISCH_PORT, MULTI_BL, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT, FIN_DEST,
                                                EIR_ID, ACC_NO, CMID, EXPIRY_DATE, DOMESTIC,
                                                CUSTOM_CHECK_FLG, CUST_PAY_DEMURRAGE, CUSTOM_CHECK_FLG,
                                                CUST_REG_NO, DELIVERY_METHOD, DIRECT_IMPORT_DELIVERY, DELIVERY_METHOD,
                                                FREIGHT_FORWARDER, INSPECT_BY_R, INSPECT_BY_D, INTER_MOVE_CODE,
                                                IS_CFS, ITEM_SIZE, ITEM_STATUS, ORIGIN_TERMINAL, ORIGIN_TERMINAL_TYPE,
                                                PAY_SERVICE_CD, PEB_CLR_YN, PEB_EXP_NO, PEB_CLR_YN, PIB_CLR_YN, PIB_IMP_NO,
                                                RECEIVER_ID, REF_NO, RESTOW_TYPE, RESTOW_ACC_TYPE, RESTOW_ACC,
                                                RESTRICT_FLG, SEAL_CHECK_REQ, SHIPPER, SPECIAL_GEAR, SPECIAL_HDL_CODE,
                                                SPECIAL_USED, TRUCK_BARGE_INTERNAL, TURNING_CHE, WHO_PAYS,
                                                BB_ID, COPRAR_DISCHARGE, CTR_OWNER, EXP_CGO_CLEARED,
                                                PKG_UNIT, PKG_TYPE, RECEIVAL_METHOD, RA_KEY, EXIT_VES_CD, EXIT_VOYAGE, ORG_ARR_CAR, ORG_IN_VOYAGE
                                        FROM ITEM
                                        WHERE ITEM_KEY = {itemKey}                                                ";
            }
            else
            {
                sql = $@"
                            
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, CUST_RELEASE_MARK, EXPORT_CONFIRMED, ARR_CAR_MANIFEST, 
                                                LL_DISCH_PORT, MULTI_BL, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT, FIN_DEST,
                                                EIR_ID, ACC_NO, CMID, EXPIRY_DATE, DOMESTIC,
                                                CUSTOM_CHECK_FLG, CUST_PAY_DEMURRAGE, CUSTOM_CHECK_FLG,
                                                CUST_REG_NO, DELIVERY_METHOD, DIRECT_IMPORT_DELIVERY, DELIVERY_METHOD,
                                                FREIGHT_FORWARDER, INSPECT_BY_R, INSPECT_BY_D, INTER_MOVE_CODE,
                                                IS_CFS, ITEM_SIZE, ITEM_STATUS, ORIGIN_TERMINAL, ORIGIN_TERMINAL_TYPE,
                                                PAY_SERVICE_CD, PEB_CLR_YN, PEB_EXP_NO, PEB_CLR_YN, PIB_CLR_YN, PIB_IMP_NO,
                                                RECEIVER_ID, REF_NO, RESTOW_TYPE, RESTOW_ACC_TYPE, RESTOW_ACC,
                                                RESTRICT_FLG, SEAL_CHECK_REQ, SHIPPER, SPECIAL_GEAR, SPECIAL_HDL_CODE,
                                                SPECIAL_USED, TRUCK_BARGE_INTERNAL, TURNING_CHE, WHO_PAYS,
                                                BB_ID, COPRAR_DISCHARGE, CTR_OWNER, EXP_CGO_CLEARED,
                                                PKG_UNIT, PKG_TYPE, RECEIVAL_METHOD, RA_KEY, EXIT_VES_CD, EXIT_VOYAGE, ORG_ARR_CAR, ORG_IN_VOYAGE
                                        FROM ITEM
                                        WHERE ITEM_KEY = {itemKey}
                                              AND HIST_FLG <> 'Y' ";
            }
            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new Item(item);

            if (result.Any())
            {
                activeContainer = result.First();
            }

            return activeContainer;
        }

        /// <summary>
        /// Get Container detail by given container digit character
        /// </summary>
        /// <returns></returns>
        public List<Item> GetItemByContainerDigit(string ctrNumber, bool? history)
        {
            List<Item> ctnrList = new List<Item>();
            try
            {
                if (ctrNumber.Length <= 0)
                {
                    return ctnrList;
                }
                string whereCtr = "";

                if (ctrNumber.CheckInputIsContainer())
                {
                    whereCtr = history.CheckBooleanEx()
                    ? $@"  ITEM_NO = '{ctrNumber}'  "
                        : $@" ITEM_NO = '{ctrNumber}' AND HIST_FLG <> '{BooleanType.Yes}'";
                }
                else
                {
                    whereCtr = history.CheckBooleanEx()
                    ? $@" ITEM_NO like '%{ctrNumber}%'  "
                        : $@" ITEM_NO like '%{ctrNumber}%' AND HIST_FLG <> '{BooleanType.Yes}'";
                }

                var sql = $@"
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, CUST_RELEASE_MARK, EXPORT_CONFIRMED, ARR_CAR_MANIFEST, 
                                                LL_DISCH_PORT, MULTI_BL, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT, FIN_DEST 
                                        FROM ITEM i                                        
                                        WHERE {whereCtr} AND ITEM_KEY = ( SELECT max(sub.ITEM_KEY) FROM ITEM sub WHERE sub.ITEM_NO = i.ITEM_NO)                                    
                                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from row in queryResult.AsEnumerable()
                              select new Item(row));

                ctnrList = result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return ctnrList;
        }


        /// <summary>
        /// Get Container detail by given container digit character
        /// </summary>
        /// <returns></returns>
        public List<Item> GetListItemByContainerByTime(string ctrNumber, DateTime fromDate, DateTime toDate, bool getExact = false)
        {
            List<Item> ctnrList = new List<Item>();

            try
            {
                if (ctrNumber.IsEmpty())
                {
                    return ctnrList;
                }
                string currDateFrom = fromDate.ToString(GlobalSettings.StrDMY) + " 00:00";
                string currDateTo = toDate.ToString(GlobalSettings.StrDMY) + " 23:59";

                string swhere = $@"  ITEM_NO like '%{ctrNumber}%'  ";

                if (getExact)
                {
                    swhere = $@"  ITEM_NO = '{ctrNumber}'  ";
                }
                swhere = swhere +
                         $@" and ARR_TS BETWEEN to_date('{currDateFrom}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{currDateTo}', 'dd/MM/yyyy hh24:mi:ss')";
                var sql = $@"
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, CUST_RELEASE_MARK, EXPORT_CONFIRMED, ARR_CAR_MANIFEST, 
                                                LL_DISCH_PORT, MULTI_BL, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT, FIN_DEST 
                                        FROM ITEM
                                        WHERE {swhere} ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from row in queryResult.AsEnumerable()
                              select new Item(row));

                ctnrList = result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return ctnrList;
        }
        /// <summary>
        /// Gets all container arrival or depart by vessel
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public List<ViewItem> GetItemThcByVessel(string vesId)
        {
            try
            {
                if (vesId.IsEmpty())
                {
                    return new List<ViewItem>();
                }

                var psSQL = string.Format($@"
                                SELECT i.ITEM_KEY, i.LINE_OPER, i.SLOT_CODE,
                                    i.ITEM_NO, i.LENGTH, i.CATEGORY, i.FEL, i.ISO, i.GROSS, 
                                    i.IS_REEFER, i.IS_OOG, i.IS_OV_WEIGHT, i.IS_DANGEROUS, i.IS_FL, i.BILL_TYPE

                                FROM VIEW_VESSEL_ACTIVITY i
                                WHERE i.VES_ID = '{vesId}' 
                                ORDER BY i.Slot_code, i.Length, i.Item_no	",
                                      vesId);

                var queryResult = OraDatabase.ExecuteSql(psSQL);

                return (from d in queryResult.AsEnumerable()
                        select new ViewItem(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewItem>();

        }

        /// <summary>
        /// Get container's comment from ITEM_COMMENTS table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return all ItemComments associated with the container order by comment sequence (Desc)
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemComments> GetItemComments(int itemKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                             ITEM_KEY, COMMENT_CD, COMMENTS, UPD_TS, INTERNAL_USE, COMMENT_SEQ, UPD_CNT 
                                        FROM ITEM_COMMENTS
                                        WHERE ITEM_KEY = {itemKey}
                                        ORDER BY COMMENT_SEQ DESC

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemComments(record);

            var comment = result.ToList();

            return comment;
        }

        /// <summary>
        /// Get container's damage from ITEM_DAMAGE table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemDamage array if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemDamage> GetItemDamage(int itemKey)
        {
            List<ItemDamage> damages = new List<ItemDamage>();
            string sql = $@"
                            
                                        SELECT 
                                                ITEM_KEY, DAMAGE_CD, DAMAGE_SEQ, DAMAGE_DESC, MAN_HOUR_REPAIRS, FIXEDUP_TS, FIXEDUP_OPER, CRT_TS, CHARGE_TO, 
                                                INSPECT_BY, DAMAGE_LOC, UPD_CNT, DAMAGE_BY, SERVICE_PROVIDER 
                                        FROM ITEM_DAMAGE
                                        WHERE ITEM_KEY = {itemKey}
                                            AND TO_CHAR(FIXEDUP_TS, 'YYYY') <> '1900' 

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemDamage(record);


            if (result.Any())
            {
                damages = result.ToList();
            }

            return damages;
        }

        /// <summary>
        /// Get container's dangerous from ITEM_DANGEROUS table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemDangerous array if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemDangerous> GetItemDangerous(int itemKey)
        {
            List<ItemDangerous> dangerous = new List<ItemDangerous>();
            string sql = $@"
                            
                                        SELECT 
                                                UPD_TS, DGS_SEQ, UPD_CNT, CHARGE_FLG, ACTIVITY, SITE_ID, ITEM_KEY, DGS_CLASS, UN_NO, TEMP_FLASHPOINT 
                                        FROM ITEM_DANGEROUS
                                        WHERE ITEM_KEY = {itemKey}
                                        AND DGS_CLASS != ' '
                                        ORDER BY DGS_CLASS

                                    ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new ItemDangerous(item);

            if (result.Any())
            {
                dangerous = result.ToList();
            }

            return dangerous;
        }

        /// <summary>
        /// Get container's OOG from ITEM_OOG table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemOog object if it is found
        ///     - Return null if it is not found
        /// </returns>
        public ItemOog GetItemOog(int itemKey)
        {
            ItemOog oog = null;
            string sql = $@"
                            
                                        SELECT 
                                                ITEM_KEY, OOG_BACK, OOG_FRONT, OOG_HEIGHT, OOG_LEFT, OOG_LENGTH, OOG_RIGHT, OOG_TOP, OOG_WIDTH, UPD_TS, 
                                                LENUNITS, UPD_CNT, CABLE_REQUIRED 
                                        FROM ITEM_OOG
                                        WHERE ITEM_KEY = {itemKey}

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemOog(record);

            if (result.Any())
            {
                oog = result.First();
            }

            return oog;
        }

        /// <summary>
        /// Get container's Reefer from ITEM_REEFER table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemReefer array if it is found
        ///     - Return null if it is not found
        /// </returns>
        public ItemReefer GetItemReefer(int itemKey)
        {
            ItemReefer reefer = null;
            string sql =
                $@"SELECT ITEM_KEY, CHILLED_TEMP, FLASHPOINT_TEMP, FLASHPOINT_TYPE, FROZEN_TEMP, RFR_TRANS_TEMP, RFR_TYPE, UPD_TS
                                                , CHILLED_TYPE, FROZEN_TYPE, INGATE_TEMP, IG_TEMP_TYPE, OUTGATE_TEMP, OG_TEMP_TYPE, PRETRIP_INSPECTION, PRE_COOLING, PTIDATE
                                                , NUMBER_PROBES, HUMIDITY, O2, CO2, PLUG_TS, UNPLUG_TS, RFR_CHART_COUNT, MIN_TEMP, MAX_TEMP, IS_CONNECTED, CONAIR, TEMP_UNIT, UPD_CNT
                                                , NO_PLUGIN_REQUIRED, TEMP_CONTROL_SETTING, NON_SNP_PTI_FLG, YEAR_MADE, MODEL_MADE, VENTILATION, SETTING_TEMP
                                        FROM ITEM_REEFER
                                        WHERE ITEM_KEY = {itemKey}
                                            AND ( MIN_TEMP <> 9999 OR FROZEN_TEMP <> 9999 OR SETTING_TEMP <> 9999)

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemReefer(record);


            if (result.Any())
            {
                reefer = result.First();
            }

            return reefer;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public DataTable GetItemPtiByDate(string fromDate, string toDate)
        {
            string sql = $@"                            
                                        SELECT s.ITEM_KEY, i.ITEM_NO , i.LINE_OPER, i.ISO
                                        FROM ITEM_PTI s join ITEM i on s.ITEM_KEY = i.ITEM_KEY                                        
                                        WHERE s.PTI_DATE BETWEEN to_date('{fromDate.GetBeginDate()}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{toDate.GetEndDate()}', 'dd/MM/yyyy hh24:mi:ss') and i.HIST_FLG !='Y'                                                                                   
                                        GROUP BY s.ITEM_KEY, i.ITEM_NO, i.LINE_OPER, i.ISO ORDER BY s.ITEM_KEY desc ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            //var result = from record in queryResult.AsEnumerable()
            //             select new ItemServices(record);
            return queryResult;
        }

        /// <summary>
        /// Get container's Seal from ITEM_SEAL table. By default, all valid seals will be return.
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="validSeal"> </param>
        /// <returns>
        ///     - Return an ItemSeal array if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemSeal> GetItemSeal(int itemKey, bool validSeal)
        {
            List<ItemSeal> seals = new List<ItemSeal>();

            var validSealCondition = validSeal ? " AND INVALID_SEAL <> 'Y'" : " AND INVALID_SEAL = 'Y'";

            var sql = $@"
                            
                                        SELECT *
                                        FROM ITEM_SEAL
                                        WHERE ITEM_KEY = {itemKey}
                                            {validSealCondition}
                                        ORDER BY CRT_TS DESC 

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemSeal(record);


            if (result.Any())
            {
                seals = result.ToList();
            }

            return seals;
        }

        /// <summary>
        /// Get container's special handling from ITEM_SPECIAL_HANDLING table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemSpecialHandling object if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemSpecialHandling> GetItemSpecialHandling(int itemKey)
        {
            List<ItemSpecialHandling> specialHandlings = new List<ItemSpecialHandling>();
            string sql = $@"
                            
                                        SELECT 
                                               ITEM_KEY, CODE, DESCRIPTION, CRT_TS, UPD_TS, SEQ_NO 
                                        FROM ITEM_SPECIAL_HANDLING
                                        WHERE ITEM_KEY = {itemKey}
                                             AND to_char(COMPL_TS,'dd/mm/yyyy') = '31/12/1900'
                                             AND CODE <> ' '
                                             ORDER BY CRT_TS DESC 

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemSpecialHandling(record);


            if (result.Any())
            {
                specialHandlings = result.ToList();
            }

            return specialHandlings;
        }

        /// <summary>
        /// Get container's sub from ITEM_SUBS table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemSubs object if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemSubs> GetItemSubs(int itemKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                ITEM_KEY, ITEM_ID, ISO, UPD_TS, RETURNED, EQUIPMENT_TYPE, GENSET_TYPE, GENSET_NO, ATT_ITEM_KEY, LINE_OPER, 
                                                SLAVE_POS, DAMAGED_FLG, RESTRICTION_FLG, TARE, TURN_IN_NO, LENGTH, ITEM_TYPE, BB_ID, BB_HEIGHT, BB_LENGTH, 
                                                BB_QTY, BB_TYPE, BB_WIDTH, ITEM_CLASS, NEW_ATT_KEY, UPD_CNT, SUB_CRT_TS, GROSS, LOADLIST_FLG, CURRENT_FLG, 
                                                BB_UNIT 
                                        FROM ITEM_SUBS
                                        WHERE ITEM_KEY = {itemKey}
                                             AND ITEM_ID != ' ' AND NEW_ATT_KEY = 0 

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemSubs(record);

            return result.ToList();

        }

        /// <summary>
        /// Get container's stops from ITEM_STOPS table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemStops array if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemStops> GetItemStops(int itemKey)
        {
            var sql = $@"
                            
                                        SELECT 
                                                ITEM_KEY, STOP_CD, STOP_DESC, CLR_BY, CLR_TS, CRT_TS, UPD_TS, UPD_CNT 
                                        FROM ITEM_STOPS
                                        WHERE ITEM_KEY = {itemKey}
                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemStops(record);

            return result.ToList();
        }


        /// <summary>
        /// Get container's stops from ITEM_STOPS table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns>
        ///     - Return an ItemServices list if it is found
        /// </returns>
        public List<ItemServices> GetItemServices(int itemKey)
        {
            List<ItemServices> services = new List<ItemServices>();

            string sql = $@"
                            
                                        SELECT ITEM_KEY, SERVICE_CD, SERVICE_DESC, COMPL_TS, CRT_TS, UPD_TS, UPD_CNT, CHARGEABLE, WHO_PAYS_TYPE, SERVICE_PROVIDER, PRICE_ANOUNCED_TS, PRICE_ACCEPTED_TS
                                        FROM TOPOVN.ITEM_SERVICES

                                        WHERE ITEM_KEY = {itemKey}
                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemServices(record);


            if (result.Any())
            {
                services = result.ToList();
            }

            return services;
        }

        public DataTable GetItemServicesByTime(string fromDate, string toDate)
        {
            string sql = $@"                            
                                        SELECT s.ITEM_KEY, i.ITEM_NO , i.LINE_OPER, i.ISO
                                        FROM ITEM_SERVICES s join ITEM i on s.ITEM_KEY = i.ITEM_KEY                                        
                                        WHERE s.CRT_TS BETWEEN to_date('{fromDate.GetBeginDate()}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{toDate.GetEndDate()}', 'dd/MM/yyyy hh24:mi:ss') and i.HIST_FLG !='Y'                                      
                                        GROUP BY s.ITEM_KEY, i.ITEM_NO, i.LINE_OPER, i.ISO
                                        ORDER BY s.ITEM_KEY";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            //var result = from record in queryResult.AsEnumerable()
            //             select new ItemServices(record);
            return queryResult;
        }

        /// <summary>
        /// Get all containers is restowed or discharged to yard and has plan load to vessel
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public List<Item> GetRestowedContainerByVesId(string vesId)
        {
            var sql = $@"
                    SELECT i.RESTOW_ACC_TYPE, i.RESTOW_ACC, i.RESTOW_TYPE, i.WHO_PAYS, i.LINE_OPER, i.DISCH_PORT,
	                    i.LENGTH, i.ITEM_NO, i.GROSS, i.ISO, i.FEL, i.ITEM_KEY, i.HIST_FLG, i.DEP_TS, i.DEP_CAR,
	                    i.ARR_TS, i.ARR_CAR, i.CATEGORY

                    FROM ITEM i, VESSEL_DETAILS v
                    WHERE i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_I 
                            AND i.DEP_BY = 'V' AND i.DEP_CAR = v.TFC_CODE_E
                        AND i.CATEGORY = 'R' AND v.VES_ID = '{vesId}'
                    ORDER BY i.ITEM_NO";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new Item(d)).ToList();
        }

        //--------------------------------------------------------------------------------------------------------------
        public void GetActivityPlanLocItemRestow(string itemNo, string vesId, ref string planLoc,
                                                 ref string restowedType)
        {
            var paras = vesId.Split('-');

            var vesCd = paras[0];
            var voyNo = paras[1];

            var sql = $@"
                                    Select * 
                                    From TG2VPT 
                                    Where TGCTNR = '{itemNo}' And TGVLCD = '{vesCd}' AND TGVYMX = '{voyNo}' 
                                        And (TGACTN = '13' OR TGACTN = '14' OR TGACTN = '15') 
                                    Order by TGACTN";

            var dt = OraDatabase.ExecuteSql(sql);

            if (dt.Rows.Count > 0)
            {
                int i;
                for (i = 0; i <= dt.Rows.Count - 1; i++)
                {
                    var tg2vpt = new Tg2vpt(dt.Rows[i]);

                    if (tg2vpt.Tgcpfg != BooleanType.Yes)
                    {
                        switch (tg2vpt.Tgactn)
                        {
                            case "13":
                                if (tg2vpt.Tglard == "V")
                                {
                                    restowedType = "QUAY";
                                    planLoc = "SSS";
                                }
                                else
                                {
                                    restowedType = "YARD";
                                    planLoc = "YARD";
                                }
                                break;
                            case "14":
                                restowedType = tg2vpt.Tglard == "V" ? "QUAY" : "YARD";
                                break;
                            case "15":
                                restowedType = "CELL";
                                planLoc = $"V {tg2vpt.Tglbyv} {tg2vpt.Tglrwv} {tg2vpt.Tgltrv}";

                                break;
                        }

                        break;
                    }

                }
            }
            else
            {
                planLoc = "";
            }
        }

        /// <summary>
        /// Get container's location from ITEM_LOCATION table. 
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="pch"> </param>
        /// <returns>
        ///     - Return an ItemLocation object if it is found
        ///     - Return null if it is not found
        /// </returns>
        public List<ItemLocation> GetItemLocation(int itemKey, string pch)
        {
            string pchCondition = "";

            if (pch != "")
            {
                pchCondition = $@" AND STK_PCH = '{pch}'";
            }

            string sql = $@"
                            
                                        SELECT ITEM_KEY, STK_PCH, STK_REF, STK_CLASS, STACK, X, Y, Z, CHE_ID, CHE_OPER, CAR_CHE_ID, CRT_TS, UPD_TS, 
                                                INTERNAL_MOVE_CD, EXEC_TS, MOVE_TYPE, PREV_LOC 
                                        FROM ITEM_LOCATION   
                                        WHERE ITEM_KEY = {itemKey}
                                               {pchCondition}

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ItemLocation(record);


            return result.ToList();

        }

        public DataTable GetRoundMoveLocationByStoreProceduce(string itemNo, DateTime fromDate, DateTime toDate, string filterCondition)
        {

            //Type type = this.GetType();
            //MethodInfo myMethodInfo = type.GetMethod("CheProdCal");
            //parameters = this.GenOraParams(pMethodInfo);            
            //var parameters = new List<OracleParameter>();
            //parameters.Insert(0, new OracleParameter("psItemNo", OracleDbType.Char) { Value = itemNo });
            //parameters.Insert(0, new OracleParameter("pdFrom", OracleDbType.Char) { Value = fromDate });
            //parameters.Insert(0, new OracleParameter("pdTo", OracleDbType.Char) { Value = toDate });
            //parameters.Insert(0, new OracleParameter(":refcur_questions", OracleDbType.RefCursor));
            //OracleParameter oraP = new OracleParameter();
            //oraP.OracleDbType = OracleDbType.RefCursor;
            //oraP.Direction = System.Data.ParameterDirection.Output;
            //parameters.Insert(0, oraP);
            // return OraDatabase.ExecuteSpQuery("ROUND_MOVE_CONTAINER", parameters);            
            DataTable dt = new DataTable();
            string sql = string.Empty;
            string swhere = string.Empty;
            try
            {
                sql = "SELECT * from VIEW_ROUND_MOVEMENT_CONTAINER " + " Where ITEM_NO = '" + itemNo + "'";
                if (fromDate.Year > 1901 || toDate.Year > 1901)
                {
                    string currDateFrom = fromDate.ToString(GlobalSettings.StrDMY) + " 00:00";
                    string currDateTo = toDate.ToString(GlobalSettings.StrDMY) + " 23:59";
                    sql += $@" and EXEC_TS BETWEEN to_date('{currDateFrom}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{currDateTo}', 'dd/MM/yyyy hh24:mi:ss')";
                }

                string[] arrFilter = filterCondition.Split(',');
                for (int i = 0; i < arrFilter.Count(); i++)
                {
                    if (arrFilter[i].Trim() == string.Empty)
                    {
                        continue;
                    }
                    if (i == 0)
                    {
                        swhere += " and ( OPER_METHOD = '" + arrFilter[i] + "'";
                    }
                    else
                    {
                        swhere += " or OPER_METHOD = '" + arrFilter[i] + "'";
                    }
                }
                swhere += swhere != string.Empty ? " ) " : string.Empty;
                //if (filterCondition.Contains("NTAU"))
                //{
                //    swhere += " and ( OPER_METHOD = 'NTAU' ";
                //}
                //if (filterCondition.Contains("XTAU"))
                //{
                //    if (swhere == string.Empty)
                //    {
                //        swhere += " and ( OPER_METHOD = 'XTAU' ";
                //    }
                //    else
                //    {
                //        sql += " or OPER_METHOD = 'XTAU' ";
                //    }
                //}
                //if (filterCondition.Contains("TBAI"))
                //{
                //    if (swhere == string.Empty)
                //    {
                //        swhere += " and ( OPER_METHOD = 'TBAI' ";
                //    }
                //    else
                //    {
                //        swhere += " or OPER_METHOD = 'TBAI' ";
                //    }
                //}
                //if (filterCondition.Contains("XNBAI"))
                //{
                //    if (swhere == string.Empty)
                //    {
                //        swhere += " and ( OPER_METHOD = 'XNBAI' ";
                //    }
                //    else
                //    {
                //        swhere += " or OPER_METHOD = 'XNBAI' ";
                //    }
                //}
                //swhere += swhere != string.Empty ? " ) " : string.Empty;
                sql = sql + swhere + " order by ITEM_KEY, EXEC_TS ";

                //sql = "SELECT * from VIEW_ROUND_MOVEMENT_CONTAINER Where ITEM_NO = '" + itemNo + "'";
                #region REMOVE CODE
                //NHAP TAU
                /* if (filterCondition == string.Empty || filterCondition.Contains("NTAU"))
                 {
                     sql += "Select 'NTAU' as OPER_METHOD, Case When Dl.CREATED_BY ='T00-RECV' Then 'NHAP-SLA' Else 'NHAP-TAU' End As OPERATION_METHOD , "
                         + "Dl.SLOT_CODE As SLOT, DL.AGENT AS AGENT, Dl.LINE_OPER AS LINER, locVessel.EXEC_TS AS ARR_TS, "
                         + "Dl.ITEM_NO AS ITEM_NO, Dl.BOOK_NO AS BOOK_NO, Dl.BILL_OF_LADING As BILL_NO, "
                         + "Case When Sub2.SEAL_NO Is Null Then ' ' Else Sub2.SEAL_NO End As SEAL_NO, "
                         + "Dl.CATEGORY As CAT, Dl.ISO AS ISO, Dl.FEL AS FEL, Dl.Grade AS GRADE, Dl.CGO_GROSS_WT AS CGO_WT, Dl.WEIGHT AS Gross_WT, "
                         + "Dl.PLACE_OF_DELIVERY AS FPOD, Dl.TERMINAL_ID AS TER_ID, "
                         + "CASE WHEN locTruck.MOVE_TYPE = '12' THEN LTrim(locTruck.STACK || RTRIm(locTruck.X)) ||LTrim(RTrim(locTruck.Y)) ||'0' ||LTrim(RTRIm(locTruck.Z))"
                                                  + "ELSE LTrim(locYard.STACK || RTRIm(locYard.X)) ||LTrim(RTrim(locYard.Y)) ||'0' ||LTrim(RTRIm(locYard.Z)) END AS YARD_LOC,"
                         + "locTruck.CHE_ID As LIFT_ON, locTruck.CHE_OPER As LIFT_ON_DR, locTruck.CAR_CHE_ID As CAR_ID, locTruck.CAR_CHE_OPER AS CAR_OPER,"
                         + "locYard.CHE_ID AS LIFT_OF, locYard.CHE_OPER As LIFT_OF_DR, Sub3.SETTING_TEMP AS Set_Temp, Sub5.DGS_CLASS AS DGS_CLASS,"
                         + "Sub5.UN_NO AS UN_NO, Dl.Ves_CD AS VES_CD, Dl.TFC_CODE As TFC_IMP, Dl.VES_ID AS VES_ID, Dl.STOWLOC AS STOWLOC, "                       
                        + "Dl.ITEM_KEY AS ITEM_KEY, Dl.CREATED_BY AS CREATED_BY, locVessel.EXEC_TS , Dl.HIST_FLG AS HIST_FLG "
                        + "From DISCHARGE_LIST DL "
                                + "Left Outer JOIN (SELECT t.MANIFEST_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') Damage "
                                                      + "FROM DISCH_SPECIAL_HANDLING t WHERE t.MANIFEST_KEY = 629653 GROUP BY t.MANIFEST_KEY ) Sub1 On DL.MANIFEST_KEY = Sub1.MANIFEST_KEY AND Sub1.MANIFEST_KEY > 0 "
                                + "Left Outer Join DISCH_SPECIAL_HANDLING Sub1 On Dl.MANIFEST_KEY =Sub1.MANIFEST_KEY "
                                + "Left Outer Join DISCH_SEAL Sub2 On Dl.MANIFEST_KEY =Sub2.MANIFEST_KEY AND Sub2.CURRENT_FLG = 'Y' "
                                + "Left Outer Join DISCH_Reefer Sub3 On Dl.MANIFEST_KEY =Sub3.MANIFEST_KEY "
                                + "Left Outer Join DISCH_OOG Sub4 On Dl.MANIFEST_KEY =Sub4.MANIFEST_KEY "
                                + "Left Outer Join DISCH_DANGEROUS Sub5 On Dl.MANIFEST_KEY =Sub5.MANIFEST_KEY "
                                + "Left Outer Join ITEM_LOCATION locTruck ON locTruck.ITEM_KEY = dl.ITEM_KEY AND (locTruck.MOVE_TYPE = '03' OR locTruck.MOVE_TYPE = '12') "
                                + "Left Outer Join ITEM_LOCATION locVessel On locTruck.PREV_ID = locVessel.ID "
                                + "Left Outer Join ITEM_LOCATION locYard On locTruck.ID = locYard.PREV_ID "
                        + "Where DL.ITEM_NO = '" + itemNo + "' "
                                + string.Format(@" and locVessel.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);
                     flagUnion = true;
                 }
            
                 if (filterCondition == string.Empty || filterCondition.Contains("XTAU"))
                 {
                     if (flagUnion == true)
                     {
                         sql += "\n UNION \n";
                     }
                    //XUAT TAU
                     sql += " Select 'XTAU' as OPER_METHOD, Case When iPreAdv.CREATED_BY ='T00-DELV' Then 'XUAT-SLA' Else 'XUAT-TAU' End As OPERATION_METHOD,"
                            + " iPreAdv.SLOT As SLOT, "
                            + " iPreAdv.AGENT AS AGENT,"
                            + "iPreAdv.LINER_CODE AS LINER,"
                            + "locVessel.EXEC_TS AS ARR_TS,"
                            + "iPreAdv.ITEM_NO AS ITEM_NO, "
                            + "iPreAdv.BOOK_NO AS BOOK_NO,"
                            + "t.BILL_OF_LADING As BILL_NO,"
                            + "iPreAdv.SEAL_NO AS SEAL_NO, "
                            + "iPreAdv.CATEGORY As CAT,"
                            + "iPreAdv.ISO, "
                            + "iPreAdv.FEL,"
                            + "iPreAdv.GRADE,"
                            + "iPreAdv.CARGO_WGT AS CGO_WT, "
                            + " iPreAdv.Gross AS GROSS_WT,"
                            + "iPreAdv.POD AS FPOD, "
                            + "iPreAdv.TERMINAL_ID AS TER_ID,  "
                            + "LTrim(RTRIm(locYard.STACK) || RTRIm(locYard.X)) ||LTrim(RTrim(locYard.Y)) ||'0' ||LTrim(RTRIm(locYard.Z)) AS YARD_LOC,"
                            + "locTruck.CHE_ID As LIFT_ON, "
                            + "locTruck.CHE_OPER As LIFT_ON_DR, "
                            + "locTruck.CAR_CHE_ID As CAR_ID, "
                            + "locTruck.CAR_CHE_OPER AS CAR_OPER,"
                            + "locVessel.CHE_ID AS LIFT_OF, "
                            + "locVessel.CHE_OPER As LIFT_OF_DR, "
                            + "Sub3.SETTING_TEMP AS SET_TEMP, "
                            + "Sub5.DGS_CLASS, "
                            + "Sub5.UN_NO,"
                            + "iPreAdv.VES_CD, "
                            + "iPreAdv.TFC_CODE As TFC_IMP, "
                            + "iPreAdv.VES_ID, "
                            + "LTrim( RTRIm(locVessel.X)) ||LTrim(RTrim(locVessel.Y)) ||'0' ||LTrim(RTRIm(locVessel.Z)) AS STOWLOC, "                        
                            + "iPreAdv.ITEM_KEY, "
                            + "iPreAdv.CREATED_BY, locVessel.EXEC_TS, "
                            + "t.HIST_FLG  "
                        + "FROM EDI_ITEM_PRE_ADVICE iPreAdv left JOIN ITEM t ON iPreAdv.ITEM_KEY = t.ITEM_KEY "
                                + "Left Outer JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') Damage  "
                                                      + "FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY = 629653 GROUP BY t.ITEM_KEY ) Sub1 On iPreAdv.ITEM_KEY = Sub1.ITEM_KEY AND Sub1.ITEM_KEY > 0 "
                                + "Left Outer Join ITEM_SEAL Sub2 On iPreAdv.ITEM_KEY =Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' "
                                + "Left Outer Join ITEM_REEFER Sub3 On iPreAdv.ITEM_KEY =Sub3.ITEM_KEY "
                                + "Left Outer Join ITEM_OOG Sub4 On iPreAdv.ITEM_KEY = Sub4.ITEM_KEY "
                                + "Left Outer Join ITEM_DANGEROUS Sub5 On iPreAdv.ITEM_KEY =Sub5.ITEM_KEY "
                                + "Left Outer Join ITEM_LOCATION locTruck ON locTruck.ITEM_KEY = iPreAdv.ITEM_KEY AND (locTruck.MOVE_TYPE = '05' OR locTruck.MOVE_TYPE = '13') "
                                + "Left Outer Join ITEM_LOCATION locYard On locYard.ID = locTruck.PREV_ID AND locYard.STK_CLASS = 'Y' "
                                + "Left Outer Join ITEM_LOCATION locVessel On locVessel.PREV_ID = locTruck.ID "
                        + "Where iPreAdv.ITEM_NO = '" + itemNo + "' "
	                            + string.Format(@" and locVessel.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);
                      flagUnion = true;
                 }
                if (filterCondition == string.Empty || filterCondition.Contains("XNBAI"))
                 {
                     if (flagUnion == true)
                     {
                         sql += "\n UNION \n";
                     }
	                //NHAP BAI                
                     sql += " SELECT'NBAI' As OPER_METHOD , preTran.OPERATION_METHOD, "
                            + " t.SLOT_CODE As SLot,  "
                            + " t.AGENT,  "
                            + " preTran.LINE_OPER AS Liner,  "
                            + " locYard.EXEC_TS AS Arr_TS,  "
                            + " preTran.ITEM_NO,  "
                            + " t.BILL_OF_LADING As Bill_no,  "
                            + " preTran.BOOK_NO,  "
                            + " Case When Sub2.SEAL_NO Is Null Then preTran.SEAL_NO Else Sub2.SEAL_NO End As Seal_No,  "
                            + " preTran.CATEGORY As Cat,  "
                            + " preTran.ISO_CODE,  "
                            + " preTran.FEL, "
                            + " t.Grade,  "
                            + " t.CGO_GROSS_WT AS CGO_WT,  "
                            + " preTran.Gross AS Gross_WT, "
                            + " preTran.FPOD AS FPOD,  "
                            + " t.TERMINAL_ID AS Ter_ID,   "
                            + " LTrim(RTRIm(locYard.STACK) || RTRIm(locYard.X)) ||LTrim(RTrim(locYard.Y)) ||'0' ||LTrim(RTRIm(locYard.Z)) AS Yard_Loc, "
                            + " locYard.CHE_ID As Lift_ON,  "
                            + " locYard.CHE_OPER As Lift_on_Dr,  "
                            + " locTruck.CAR_CHE_ID As Car_ID,  "
                            + " locTruck.CAR_CHE_OPER AS Car_Oper, "
                            + " locYard.CHE_ID AS Lift_Of,  "
                            + " locYard.CHE_OPER As Lift_of_Dr,  "
                            + " Sub3.SETTING_TEMP AS Set_Temp,  "
                            + " Sub5.DGS_CLASS,  "
                            + " Sub5.UN_NO, "
                            + " preTran.Ves_CD,  "
                            + " preTran.TFC_CODE As TFC_Imp,  "
                            + " preTran.VES_ID,  "
                            + " ' ' AS STOWLOC,  "                           
                            + " preTran.ITEM_KEY,  "
                            + " preTran.CREATED_BY, locYard.EXEC_TS ,  "
                            + " preTran.HIST_FLG  "
                        + " FROM  PREGATE_TRANSACT preTran LEFT OUTER JOIN TRK_TRANSACT trkTran ON preTran.EIR_ID = trkTran.EIR_ID "
                                + " LEFT OUTER JOIN ITEM t ON preTran.ITEM_KEY = t.ITEM_KEY  "
                                + " Left Outer JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') Damage   "
                                                      + " FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY = 629653 GROUP BY t.ITEM_KEY ) Sub1 On preTran.ITEM_KEY = Sub1.ITEM_KEY AND Sub1.ITEM_KEY > 0 "
                                + " Left Outer Join ITEM_SEAL Sub2 On preTran.ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y'  "
                                + " Left Outer Join ITEM_REEFER Sub3 On preTran.ITEM_KEY = Sub3.ITEM_KEY "
                                + " Left Outer Join ITEM_OOG Sub4 On preTran.ITEM_KEY = Sub4.ITEM_KEY "
                                + " Left Outer Join ITEM_DANGEROUS Sub5 On preTran.ITEM_KEY = Sub5.ITEM_KEY "
                                + " Left Outer Join ITEM_LOCATION locYard On preTran.ITEM_KEY =locYard.ITEM_KEY And locYard.Move_type ='01' "
                                + " Left Outer Join ITEM_LOCATION locTruck ON locTruck.ID = locYard.PREV_ID AND locTruck.STK_CLASS = 'T' "
                        + " WHERE preTran.R_D = 'R' AND preTran.ITEM_NO = '" + itemNo + "' "
                                + string.Format(@" and locYard.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);

                    //XUAT BAI
                    sql += "\n UNION \n";
                    sql += " SELECT 'XBAI' As OPER_METHOD , preTran.OPERATION_METHOD, "
                            + " t.SLOT_CODE As SLot,  "
                            + " t.AGENT,  "
                            + " preTran.LINE_OPER AS Liner,  "
                            + " locTruck.EXEC_TS AS Arr_TS,  "
                            + " preTran.ITEM_NO,  "
                            + " t.BILL_OF_LADING As Bill_no,  "
                            + " preTran.BOOK_NO,  "
                            + " Case When Sub2.SEAL_NO Is Null Then preTran.SEAL_NO Else Sub2.SEAL_NO End As Seal_No,  "
                            + " preTran.CATEGORY As Cat,  "
                            + " preTran.ISO_CODE,  "
                            + " preTran.FEL, "
                            + " t.Grade,  "
                            + " t.CGO_GROSS_WT AS CGO_WT,  "
                            + " preTran.Gross AS Gross_WT, "
                            + " preTran.FPOD AS FPOD,  "
                            + " t.TERMINAL_ID AS Ter_ID,   "
                            + " LTrim(RTRIm(locYard.STACK) || RTRIm(locYard.X)) ||LTrim(RTrim(locYard.Y)) ||'0' ||LTrim(RTRIm(locYard.Z)) AS Yard_Loc, "
                            + " locYard.CHE_ID As Lift_ON,  "
                            + " locYard.CHE_OPER As Lift_on_Dr,  "
                            + " locTruck.CAR_CHE_ID As Car_ID,  "
                            + " locTruck.CAR_CHE_OPER AS Car_Oper, "
                            + " locYard.CHE_ID AS Lift_Of,  "
                            + " locYard.CHE_OPER As Lift_of_Dr,  "
                            + " Sub3.SETTING_TEMP AS Set_Temp,  "
                            + " Sub5.DGS_CLASS,  "
                            + " Sub5.UN_NO, "
                            + " preTran.Ves_CD,  "
                            + " preTran.TFC_CODE As TFC_Imp,  "
                            + " preTran.VES_ID,  "
                            + " ' ' AS STOWLOC,  "
                        //--' ' as MANIFEST_KEY, 
                            + " preTran.ITEM_KEY,  "
                            + " preTran.CREATED_BY, locTruck.EXEC_TS ,  "
                            + " preTran.HIST_FLG  "
                        + " FROM  PREGATE_TRANSACT preTran LEFT OUTER JOIN TRK_TRANSACT trkTran ON preTran.EIR_ID = trkTran.EIR_ID "
                                + " JOIN ITEM t ON preTran.ITEM_KEY = t.ITEM_KEY  "
                                + " Left Outer JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') Damage   "
                                                      + " FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY = 629653 GROUP BY t.ITEM_KEY ) Sub1 On preTran.ITEM_KEY = Sub1.ITEM_KEY AND Sub1.ITEM_KEY > 0 "
                                + " Left Outer Join ITEM_SEAL Sub2 On preTran.ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' "
                                + " Left Outer Join ITEM_REEFER Sub3 On preTran.ITEM_KEY = Sub3.ITEM_KEY "
                                + " Left Outer Join ITEM_OOG Sub4 On preTran.ITEM_KEY = Sub4.ITEM_KEY "
                                + " Left Outer Join ITEM_DANGEROUS Sub5 On preTran.ITEM_KEY = Sub5.ITEM_KEY "
                                + " Left Outer Join ITEM_LOCATION locYard On preTran.ITEM_KEY =locYard.ITEM_KEY And locYard.Move_type ='02' "
                                + " Left Outer Join ITEM_LOCATION locTruck ON locTruck.ID = locYard.PREV_ID AND locTruck.STK_CLASS = 'T' "
                        + " WHERE preTran.R_D = 'D' AND preTran.ITEM_NO = '" + itemNo + "' "
                                + string.Format(@" and locTruck.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);
                    flagUnion = true;
                }
                if (filterCondition == string.Empty || filterCondition.Contains("TBAI"))
                 {
                     if (flagUnion == true)
                     {
                         sql += "\n UNION \n";
                     }                         
                     //CHUYEN DON BAI              
                     sql += " SELECT 'TBAI' As OPER_METHOD, ' ' as OPERATION_METHOD, "
                            + " t.SLOT_CODE As SLot,  "
                            + " t.AGENT,  "
                            + " t.LINE_OPER AS Liner,  "
                            + " loc.EXEC_TS  AS Arr_TS,  "
                            + " t.ITEM_NO,  "
                            + " t.BILL_OF_LADING As Bill_no,  "
                            + " t.BOOK_NO,  "
                            + " Case When Sub2.SEAL_NO Is Null Then t.SEAL_NO_CURRENT Else Sub2.SEAL_NO End As Seal_No,  "
                            + " t.CATEGORY As Cat,  "
                            + " t.ISO,  "
                            + " t.FEL, "
                            + " t.Grade,  "
                            + " t.CGO_GROSS_WT AS CGO_WT,  "
                            + " t.Gross AS Gross_WT, "
                            + " t.FIN_DEST AS FPOD,  "
                            + " t.TERMINAL_ID AS Ter_ID,   "
                            + " case when loc.STK_CLASS = 'T' and loc.MOVE_TYPE = '02' THEN ' ' ELSE LTrim(RTRIm(loc.STACK) || RTRIm(loc.X)) ||LTrim(RTrim(loc.Y)) ||'0' ||LTrim(RTRIm(loc.Z)) END AS Yard_Loc, "
                            + " loc.CHE_ID As Lift_ON,  "
                            + " loc.CHE_OPER As Lift_on_Dr,  "
                            + " case when loc.STK_CLASS = 'T' and loc.MOVE_TYPE = '02' THEN loc.STACK ELSE loc.CAR_CHE_ID END As Car_ID,  "
                            + " loc.CAR_CHE_OPER AS Car_Oper, "
                            + " loc.CHE_ID AS Lift_Of,  "
                            + " loc.CHE_OPER As Lift_of_Dr,  "
                            + " Sub3.SETTING_TEMP AS Set_Temp,  "
                            + " Sub5.DGS_CLASS,  "
                            + " Sub5.UN_NO, "
                            + " t.Ves_CD,  "
                            + " v.TFC_CODE_I As TFC_Imp,  "
                            + " v.VES_ID,  "
                            + " ' ' AS STOWLOC,  "
                        //--' ' as MANIFEST_KEY, 
                            + " t.ITEM_KEY,  "
                            + " ' ' AS Creat_By, loc.EXEC_TS, "
                            + " t.HIST_FLG  "
                        + " From ITEM t Join ITEM_LOCATION loc On t.ITEM_KEY =loc.ITEM_KEY And ( loc.MOVE_TYPE = '02' OR loc.MOVE_TYPE = '11') "
                                + " LEFT OUTER JOIN PREGATE_TRANSACT p on p.EIR_ID = t.EIR_ID "
                                + " LEFT OUTER JOIN VESSEL_DETAILS v ON t.VES_ID = v.VES_ID "
                                + " Left Outer Join ITEM_SEAL Sub2 On t .ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' "
                                + " Left Outer Join ITEM_REEFER Sub3 On t .ITEM_KEY = Sub3.ITEM_KEY "
                                + " Left Outer Join ITEM_OOG Sub4 On t .ITEM_KEY = Sub4.ITEM_KEY "
                                + " Left Outer Join ITEM_DANGEROUS Sub5 On t .ITEM_KEY =Sub5.ITEM_KEY "
                        + " Where t.ITEM_NO = '" + itemNo + "' "
                                + string.Format(@" and loc.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);
                    //DONG RUT
                    sql += "\n UNION \n";
                    sql += " SELECT 'TBAI' as OPER_METHOD , 'DONG-RUT' As OPERATION_METHOD,  "
                            + " t.SLOT_CODE As SLot,  "
                            + " t.AGENT,  "
                            + " t.LINE_OPER AS Liner,  "
                            + " loc.EXEC_TS  AS Arr_TS,  "
                            + " t.ITEM_NO,  "
                            + " t.BILL_OF_LADING As Bill_no,  "
                            + " iss.BOOK_NO,  "
                            + " Case When Sub2.SEAL_NO Is Null Then iss.STUFF_SEAL_NO Else Sub2.SEAL_NO End As Seal_No,  "
                            + " iss.CATEGORY As Cat,  "
                            + " t.ISO,  "
                            + " t.FEL, "
                            + " t.Grade,  "
                            + " t.CGO_GROSS_WT AS CGO_WT,  "
                            + " t.Gross AS Gross_WT, "
                            + " iss.STUFF_FPOD AS FPOD,  "
                            + " t.TERMINAL_ID AS Ter_ID,   "
                            + " case when loc.STK_CLASS = 'T' and loc.MOVE_TYPE = '02' THEN ' ' ELSE LTrim(RTRIm(loc.STACK) || RTRIm(loc.X)) ||LTrim(RTrim(loc.Y)) ||'0' ||LTrim(RTRIm(loc.Z)) END AS Yard_Loc, "
                            + " loc.CHE_ID As Lift_ON,  "
                            + " loc.CHE_OPER As Lift_on_Dr,  "
                            + " case when loc.STK_CLASS = 'T' and loc.MOVE_TYPE = '02' THEN loc.STACK ELSE loc.CAR_CHE_ID END As Car_ID,  "
                            + " loc.CAR_CHE_OPER AS Car_Oper, "
                            + " loc.CHE_ID AS Lift_Of,  "
                            + " loc.CHE_OPER As Lift_of_Dr,  "
                            + " Sub3.SETTING_TEMP AS Set_Temp,  "
                            + " Sub5.DGS_CLASS,  "
                            + " Sub5.UN_NO, "
                            + " t.Ves_CD,  "
                            + " v.TFC_CODE_I As TFC_Imp,  "
                            + " v.VES_ID,  "
                            + " ' ' AS STOWLOC,  "
                        //--' ' as MANIFEST_KEY,  "
                            + " t.ITEM_KEY,  "
                            + " ' ' AS Creat_By, loc.EXEC_TS , "
                            + " t.HIST_FLG  "
                        + " From ITEM t Join ITEM_LOCATION loc On t.ITEM_KEY =loc.ITEM_KEY  "
                                + " JOIN ITEM_STRIP_STUFF iss ON loc.ITEM_KEY = iss.ITEM_KEY "
                                + " LEFT OUTER JOIN VESSEL_DETAILS v ON t.VES_ID = v.VES_ID "
                                + " Left Outer Join ITEM_SEAL Sub2 On t .ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' "
                                + " Left Outer Join ITEM_REEFER Sub3 On t .ITEM_KEY =Sub3.ITEM_KEY "
                                + " Left Outer Join ITEM_OOG Sub4 On t .ITEM_KEY = Sub4.ITEM_KEY "
                                + " Left Outer Join ITEM_DANGEROUS Sub5 On t .ITEM_KEY =Sub5.ITEM_KEY	 "
                        + " Where t.ITEM_NO = '" + itemNo + "' "
                                + string.Format(@" and loc.EXEC_TS BETWEEN to_date('{0}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{1}', 'dd/MM/yyyy hh24:mi:ss')", currDateFrom, currDateTo);
                }
                sql += " ) order by ITEM_KEY, EXEC_TS";*/

                #endregion

                dt = OraDatabase.ExecuteSql(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return dt;
        }

        public List<ViewRoundMovementContainer> GetListRoundMovementContainer(string itemNo, DateTime fromDate, DateTime toDate, string filterCondition)
        {
            List<ViewRoundMovementContainer> result = new List<ViewRoundMovementContainer>();
            string sql = string.Empty;
            string swhere = string.Empty;
            try
            {

                sql = "SELECT * from VIEW_ROUND_MOVEMENT_CONTAINER " + " Where ITEM_NO = '" + itemNo + "'";
                if (fromDate.Year > 1901 || toDate.Year > 1901)
                {
                    string currDateFrom = fromDate.ToString(GlobalSettings.StrDMY) + " 00:00";
                    string currDateTo = toDate.ToString(GlobalSettings.StrDMY) + " 23:59";
                    sql += $@" and EXEC_TS BETWEEN to_date('{currDateFrom}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{currDateTo}', 'dd/MM/yyyy hh24:mi:ss')";
                }

                string[] arrFilter = filterCondition.Split(',');
                for (int i = 0; i < arrFilter.Count(); i++)
                {
                    if (arrFilter[i].Trim() == string.Empty)
                    {
                        continue;
                    }
                    if (i == 0)
                    {
                        swhere += " and ( OPER_METHOD = '" + arrFilter[i] + "'";
                    }
                    else
                    {
                        swhere += " or OPER_METHOD = '" + arrFilter[i] + "'";
                    }
                }
                swhere += swhere != string.Empty ? " ) " : string.Empty;
                sql = sql + swhere + " order by ITEM_KEY, EXEC_TS ";

                DataTable queryResult = OraDatabase.ExecuteSql(sql);

                result = queryResult.AsEnumerable().Select(x => new ViewRoundMovementContainer(x)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return result;
        }

        public DataTable GetRoundMoveLocation(string itemNo, DateTime fromDate, DateTime toDate)
        {
            DataTable dt = new DataTable();
            try
            {

                string sWhere = " WHERE il.STK_PCH != 'P' and  i.ITEM_NO = '" + itemNo + "'";
                if (fromDate.Year > 1901 || toDate.Year > 1901)
                {
                    string currDateFrom = fromDate.ToString(GlobalSettings.StrDMY) + " 00:00";
                    string currDateTo = toDate.ToString(GlobalSettings.StrDMY) + " 23:59";
                    sWhere = sWhere +
                             $@" and il.EXEC_TS BETWEEN to_date('{currDateFrom}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{currDateTo}', 'dd/MM/yyyy hh24:mi:ss')";

                }

                string lsQuery = string.Empty;
                lsQuery = " SELECT il.STK_PCH as PCH , "
                                    + "CASE WHEN il.STK_CLASS = 'V' AND vd.VES_TYPE = 'B' and vd.INTERNAL_BARGE_FLG = 'I' THEN 'Sà lan cảng'"
                                    + " WHEN il.STK_CLASS = 'V' AND vd.VES_TYPE = 'B' and vd.INTERNAL_BARGE_FLG = 'E' THEN 'Sà lan ngoài'"
                                    + " WHEN il.STK_CLASS = 'V' AND vd.VES_TYPE != 'B'THEN 'Tàu'"
                                    + " WHEN il.STK_CLASS = 'T' AND il.STK_REF = 'TRAILER' THEN 'Xe nội bộ'"
                                    + " WHEN il.STK_CLASS = 'T' AND il.STK_REF != 'TRAILER' THEN 'Xe ngoài'"
                                    + " WHEN il.STK_CLASS = 'Y'THEN 'Bãi' END AS STK_REF_DESC, "
                                    + " CASE WHEN il.STK_CLASS = 'V' THEN vd.VES_CD"
                                    + " WHEN il.STK_CLASS = 'T' AND il.STK_REF = 'TRAILER' THEN il.STACK "
                                    + " WHEN il.STK_CLASS = 'T' AND il.STK_REF != 'TRAILER' THEN il.STK_REF END AS EQUIPMENT_ID ,"
                                    + "CASE WHEN il.STK_CLASS != 'Y' AND il.STK_CLASS != 'V' THEN ' ' ELSE (il.STACK || ' ' || il.X || il.Y || il.Z) END as STACK , il.STK_CLASS , il.STK_REF"
                                    + " ,il.CHE_ID, trim(il.CHE_OPER) as CHE_OPER, il.CAR_CHE_ID, trim(il.CAR_CHE_OPER) as CAR_CHE_OPER, vd.TFC_CODE_I,vd.TFC_CODE_E, il.ITEM_KEY, i.HIST_FLG"
                                    + " , vd.VES_CD, il.EXEC_TS"
                               + " FROM VIEW_ITEM i join ITEM_LOCATION il on i.ITEM_KEY = il.ITEM_KEY LEFT JOIN VESSEL_DETAILS vd ON il.STK_REF = vd.VES_ID ";
                lsQuery = lsQuery + sWhere;
                lsQuery += " ORDER BY il.ITEM_KEY , il.EXEC_TS ASC";
                dt = OraDatabase.ExecuteSql(lsQuery);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return dt;
        }

        public DataTable GetViewHistoryModifyData(string itemNo, DateTime fromDate, DateTime toDate, string filterCondition)
        {
            DataTable dt = new DataTable();
            string sql = string.Empty;
            string swhere = string.Empty;
            try
            {
                sql = "SELECT * from VIEW_HISTORY_MODIFY_DATA Where SOCONT = '" + itemNo + "'";
                if (fromDate.Year > 1901 || toDate.Year > 1901)
                {
                    string currDateFrom = fromDate.ToString(GlobalSettings.StrDMY) + " 00:00";
                    string currDateTo = toDate.ToString(GlobalSettings.StrDMY) + " 23:59";
                    sql += $@" and NGAY_THAY_DOI BETWEEN to_date('{currDateFrom}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{currDateTo}', 'dd/MM/yyyy hh24:mi:ss')";
                }

                if (filterCondition.Contains("NTAU"))
                {
                    swhere += " and ( PHUONG_AN = 'NHAP_TAU' ";
                }
                if (filterCondition.Contains("XTAU"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " and ( PHUONG_AN = 'XUAT_TAU' ";
                    }
                    else
                    {
                        sql += " or PHUONG_AN = 'XTAU' ";
                    }
                }
                if (filterCondition.Contains("TBAI"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " and ( PHUONG_AN = 'TON_BAI' ";
                    }
                    else
                    {
                        swhere += " or PHUONG_AN = 'TON_BAI' ";
                    }
                }
                if (filterCondition.Contains("XNBAI"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " and ( PHUONG_AN = 'XUAT_NHAP_BAI' ";
                    }
                    else
                    {
                        swhere += " or PHUONG_AN = 'XUAT_NHAP_BAI' ";
                    }
                }
                swhere += swhere != string.Empty ? " ) " : string.Empty;
                sql = sql + swhere + " order by NGAY_THAY_DOI ";

                #region REMOVE CODE
                //NHAP TAU
                /*if (filterCondition == string.Empty || filterCondition.Contains("NTAU"))
                {
                    sql += "SELECT 'NHAP_TAU' AS PHUONG_AN, "
                              + " a.OPER_NAME AS USER_NAME, "
                              + " o.FULL_NAME AS HO_TEN, "
                              + " o.OPER_CLASS_1 AS PHONG_BAN, "
                              + "a.MENU_OPTION AS FORM_THUC_HIEN, "
                              + " CASE WHEN a.ACTION_CODE = 'INSERT' THEN 'THEM'  "
                                    + " WHEN a.ACTION_CODE = 'UPDATE' THEN 'SUA' "
                                    + " WHEN a.ACTION_CODE = 'DELETE' THEN 'XOA' "
                                    + "ELSE a.ACTION_CODE END AS TYPE_WORK, "
                               + " a.CRT_TS AS NGAY_THUC_HIEN, 	   "
                               + " a.TABLE_NAME AS TABLE_NAME , "
                               + " a.ITEM_NAME AS COT_THAY_DOI, "
                               + " a.OLD_VAL AS GIA_TRI_CU, "
                               + " a.NEW_VAL AS GIA_TRI_MOI, "
                              + " d.LINE_OPER AS HANG_TAU, "
                              + " d.ITEM_NO AS SOCONT, "
                              + " d.ISO,  "
                              + " d.SEAL_NO_CURRENT AS SO_SEAL, "
                              + " d.GRADE AS GRADE, "
                              + " d.FEL AS TRANG_THAI, "
                              + " d.ITEM_SIZE AS KICHCO, "
                              + " d.WEIGHT AS TRONG_LUONG, "
                              + " Sub3.SETTING_TEMP AS SET_TEMP, "
                              + " d.UN_CODE AS UN_NO, "
                              + " Sub5.DGS_CLASS AS DGS_CLASS, "
                              + " d.DAMAGE_REMARK as HU_HONG , " 
                              + " d.AGENT AS CHUKT, "
                              + " d.VES_NAME AS TAU_NHAP, "
                              + " d.TFC_CODE AS CHUYEN_NHAP, "
                              + "  ' ' AS TAU_XUAT, "
                              + " ' ' AS CHUYEN_XUAT, "
                              + " d.DISCH_PORT AS CANG_CT, "
                              + " d.PLACE_OF_RECEIPT AS NHAN, "
                              + " d.PLACE_OF_DELIVERY AS GIAO	 "                              
                         + " FROM AUDIT_TRAIL a JOIN OPERATOR o on o.OPER_NAME = a.OPER_NAME JOIN DISCHARGE_LIST d ON a.ITEM_NO = d.ITEM_NO "
                                        + " Left Outer JOIN (SELECT t.MANIFEST_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') SPECIAL_DESCR "
                                                + " FROM DISCH_SPECIAL_HANDLING t WHERE t.MANIFEST_KEY > 0  GROUP BY t.MANIFEST_KEY ) Sub1 On d.MANIFEST_KEY = Sub1.MANIFEST_KEY "
                                        + " Left Outer Join DISCH_SEAL Sub2 On d.MANIFEST_KEY = Sub2.MANIFEST_KEY AND Sub2.CURRENT_FLG = 'Y' and Sub2.MANIFEST_KEY > 0 "
                                        + " Left Outer Join DISCH_Reefer Sub3 On d.MANIFEST_KEY = Sub3.MANIFEST_KEY and Sub3.MANIFEST_KEY > 0 "
                                        + " Left Outer Join DISCH_OOG Sub4 On d.MANIFEST_KEY = Sub4.MANIFEST_KEY and Sub4.MANIFEST_KEY > 0 "
                                        + " Left Outer Join DISCH_DANGEROUS Sub5 On d.MANIFEST_KEY = Sub5.MANIFEST_KEY and Sub5.MANIFEST_KEY > 0 "
                                        + " JOIN VESSEL_DETAILS v ON d.VES_ID = v.VES_ID AND d.VES_ID != ' ' "
                         + " WHERE a.TABLE_NAME IN ( 'DISCH_SEAL','DISCHARGE_LIST_COMMENTS', "
                                                + " 'DISCH_SPECIAL_HANDLING','DISCH_REEFER' , 'DISCH_DANGEROUS', 'DISCH_OOG','DISCHARGE_LIST','VESSEL_DETAILS') "
                                   + " AND a.ACTION_CODE in ('INSERT','UPDATE','DELETE') " 
                                   + " AND a.ITEM_NO = '" + itemNo + "'";
                    flagUnion = true;
                }
                 
                if (filterCondition == string.Empty || filterCondition.Contains("XTAU"))
                {
                    if (flagUnion == true)
                    {
                        sql += "\n UNION \n";
                    }
                    sql += "SELECT 'XUAT_TAU' AS PHUONG_AN, "
                               + " a.OPER_NAME AS USER_NAME, "
                               + " o.FULL_NAME AS HO_TEN, "
                               + " o.OPER_CLASS_1 AS PHONG_BAN, "
                               + " a.MENU_OPTION AS FORM_THUC_HIEN, "
                               + " CASE WHEN a.ACTION_CODE = 'INSERT' THEN 'THEM'  "
                                    + " WHEN a.ACTION_CODE = 'UPDATE' THEN 'SUA' "
                                    + " WHEN a.ACTION_CODE = 'DELETE' THEN 'XOA' "
                                    + " ELSE a.ACTION_CODE END AS TYPE_WORK, "
                               + " a.CRT_TS AS NGAY_THUC_HIEN, "
                               + " a.TABLE_NAME AS TABLE_NAME , "
                               + " a.ITEM_NAME AS COT_THAY_DOI, "
                               + " a.OLD_VAL AS GIA_TRI_CU, "
                               + " a.NEW_VAL AS GIA_TRI_MOI, "
                               + " d.LINER_CODE AS HANG_TAU, "
                               + " d.ITEM_NO AS SOCONT, "
                               + " d.ISO, "
                               + " d.SEAL_NO AS SO_SEAL, "
                               + " d.GRADE AS GRADE, "
                               + " d.FEL AS TRANG_THAI, "
                               + " d.LENGTH AS KICHCO, "
                               + " d.GROSS AS TRONG_LUONG, "
                               + " Sub3.SETTING_TEMP AS SET_TEMP, "
                               + " Sub5.UN_NO AS UN_NO, "
                               + " Sub5.DGS_CLASS AS DGS_CLASS, "
                               + " Sub6.DAMAGESUB as HU_HONG , " 
                               + " d.AGENT AS CHUKT, "
                               + " ' ' AS TAU_NHAP, "
                               + " ' ' AS CHUYEN_NHAP, "
                               + " v.VES_NAME AS TAU_XUAT, "
                               + " d.TFC_CODE AS CHUYEN_XUAT, "
                               + " d.POD AS CANG_CT, "
                               + " ' ' AS NHAN, "
                               + " 'CTL' AS GIAO "
                         + " FROM AUDIT_TRAIL a JOIN OPERATOR o on o.OPER_NAME = a.OPER_NAME JOIN EDI_ITEM_PRE_ADVICE d ON a.ITEM_NO = d.ITEM_NO  "
                                            + " LEFT OUTER JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') SPECIAL_DESCR  "
                                                             + " FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY > 0 GROUP BY t.ITEM_KEY ) Sub1 On d.ITEM_KEY = Sub1.ITEM_KEY "
                                            + " LEFT OUTER JOIN ITEM_SEAL Sub2 On d.ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' and Sub2.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_REEFER Sub3 On d.ITEM_KEY = Sub3.ITEM_KEY and Sub3.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_OOG Sub4 On d.ITEM_KEY = Sub4.ITEM_KEY and Sub4.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_DANGEROUS Sub5 On d.ITEM_KEY = Sub5.ITEM_KEY and Sub5.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ( SELECT idmg.ITEM_KEY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) AS DAMAGE_BY , sdsv.LOCAL_DESCR SERVICE_DESCR "
                                                                       + " , RTRIM( LISTAGG (nvl(trim(dc.DESCR_VI), trim(idmg.DAMAGE_CD) ) "
                                                                       + " || ' ' || nvl(trim(sdml.LOCAL_DESCR), trim(idmg.DAMAGE_LOC) ) || ' ' || trim(idmg.DAMAGE_DIMENSION) || '-') "
                                                                       + " WITHIN GROUP (ORDER BY idmg.DAMAGE_SEQ DESC), '-') DamageSub "
                                                                       + " FROM ITEM_DAMAGE idmg "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdml ON idmg.DAMAGE_LOC = sdml.CODE_REF AND sdml.CODE_TP = 'DAMAGELOC' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdmb ON idmg.DAMAGE_BY = sdmb.CODE_REF AND sdmb.CODE_TP = 'DAMAGEBY' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdsv ON idmg.SERVICE_CD = sdsv.CODE_REF AND sdsv.CODE_TP = 'VSSCSERVICE' "
                                                                       + " LEFT OUTER JOIN DAMAGE_CODE dc ON idmg.DAMAGE_CD = dc.DAMAGE_CD "
                                                                       + " WHERE to_char(idmg.FIXEDUP_TS, 'yyyy') = '1900' "
                                                                       + " GROUP BY idmg.ITEM_KEY, idmg.DAMAGE_BY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) , sdsv.CODE_REF, sdsv.LOCAL_DESCR ) Sub6 on Sub6.ITEM_KEY = d.ITEM_KEY and Sub6.ITEM_KEY > 0 "
                                            + " JOIN VESSEL_DETAILS v ON d.VES_ID = v.VES_ID AND d.VES_ID != ' ' "
                         + " WHERE a.TABLE_NAME IN ( 'ITEM_OOG', 'ITEM_SPECIAL_HANDLING','ITEM_SEAL','ITEM_DANGEROUS','ITEM_DAMAGE','ITEM_SERVICES', "
                                                   + " 'ITEM_COMMENTS','ITEM_REEFER' , 'ITEM_STOPS', 'ITEM','EDI_ITEM_PRE_ADVICE','VESSEL_DETAILS') "
                                  + " AND a.ACTION_CODE in ('INSERT','UPDATE','DELETE') " 
                                  + " AND a.ITEM_NO = '" + itemNo + "'";
                    flagUnion = true;
                }                
                if (filterCondition == string.Empty || filterCondition.Contains("TBAI"))
                {
                    if (flagUnion)
                    {
                        sql += "\n UNION \n";
                    }
                    sql += "SELECT 'TON_BAI' AS PHUONG_AN, "
                               + " a.OPER_NAME AS USER_NAME, "
                               + " o.FULL_NAME AS HO_TEN, "
                               + " o.OPER_CLASS_1 AS PHONG_BAN, "
                               + " a.MENU_OPTION AS FORM_THUC_HIEN, "
                               + " CASE WHEN a.ACTION_CODE = 'INSERT' THEN 'THEM'  "
                                    + " WHEN a.ACTION_CODE = 'UPDATE' THEN 'SUA' "
                                    + " WHEN a.ACTION_CODE = 'DELETE' THEN 'XOA' "
                                    + " ELSE a.ACTION_CODE END AS TYPE_WORK, "
                               + " a.CRT_TS AS NGAY_THUC_HIEN, "
                               + " a.TABLE_NAME AS TABLE_NAME , "
                               + " a.ITEM_NAME AS COT_THAY_DOI, "
                               + " a.OLD_VAL AS GIA_TRI_CU, "
                               + " a.NEW_VAL AS GIA_TRI_MOI, "
                               + " i.LINE_OPER  AS HANG_TAU, "
                               + " i.ITEM_NO AS SOCONT, "
                               + " i.ISO, "
                               + " i.SEAL_NO_CURRENT AS SO_SEAL, "
                               + " i.GRADE AS GRADE, "
                               + " i.FEL AS TRANG_THAI, "
                               + " i.ITEM_SIZE AS KICHCO, "
                               + " i.GROSS AS TRONG_LUONG, "
                               + " Sub3.SETTING_TEMP AS SET_TEMP, "
                               + " Sub5.UN_NO AS UN_NO,"
                               + " Sub5.DGS_CLASS AS DGS_CLASS, "
                               + " Sub6.DAMAGESUB  as HU_HONG, " 
                               + " i.AGENT AS CHUKT, "
                               + " v.VES_NAME AS TAU_NHAP,  "
                               + " v.TFC_CODE_I AS CHUYEN_NHAP, "
                               + " v.VES_NAME AS TAU_XUAT, "
                               + " v.TFC_CODE_E AS CHUYEN_XUAT, "
                               + " i.FDISCH_PORT AS CANG_CT, "
                               + " i.PLACE_OF_RECEIPT, "
                               + "  i.PLACE_OF_DELIVERY "
                              
                         + " FROM AUDIT_TRAIL a JOIN OPERATOR o on o.OPER_NAME = a.OPER_NAME JOIN ITEM i ON a.ITEM_NO = i.ITEM_NO  "
                                            + " LEFT OUTER JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') SPECIAL_DESCR  "
                                                       + " FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY > 0 GROUP BY t.ITEM_KEY ) Sub1 On i.ITEM_KEY = Sub1.ITEM_KEY "
                                            + " LEFT OUTER JOIN ITEM_SEAL Sub2 On i.ITEM_KEY =Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' and Sub2.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_REEFER Sub3 On i.ITEM_KEY =Sub3.ITEM_KEY and Sub3.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_OOG Sub4 On i.ITEM_KEY = Sub4.ITEM_KEY and Sub4.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_DANGEROUS Sub5 On i.ITEM_KEY = Sub5.ITEM_KEY and Sub5.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ( SELECT idmg.ITEM_KEY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) AS DAMAGE_BY , sdsv.LOCAL_DESCR SERVICE_DESCR "
                                                                       + " , RTRIM( LISTAGG (nvl(trim(dc.DESCR_VI), trim(idmg.DAMAGE_CD) ) "
                                                                       + " || ' ' || nvl(trim(sdml.LOCAL_DESCR), trim(idmg.DAMAGE_LOC) ) || ' ' || trim(idmg.DAMAGE_DIMENSION) || '-') "
                                                                       + " WITHIN GROUP (ORDER BY idmg.DAMAGE_SEQ DESC), '-') DamageSub "
                                                                       + " FROM ITEM_DAMAGE idmg "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdml ON idmg.DAMAGE_LOC = sdml.CODE_REF AND sdml.CODE_TP = 'DAMAGELOC' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdmb ON idmg.DAMAGE_BY = sdmb.CODE_REF AND sdmb.CODE_TP = 'DAMAGEBY' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdsv ON idmg.SERVICE_CD = sdsv.CODE_REF AND sdsv.CODE_TP = 'VSSCSERVICE' "
                                                                       + " LEFT OUTER JOIN DAMAGE_CODE dc ON idmg.DAMAGE_CD = dc.DAMAGE_CD "
                                                                       + " WHERE to_char(idmg.FIXEDUP_TS, 'yyyy') = '1900' "
                                                                       + " GROUP BY idmg.ITEM_KEY, idmg.DAMAGE_BY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) , sdsv.CODE_REF, sdsv.LOCAL_DESCR ) Sub6 on Sub6.ITEM_KEY = i.ITEM_KEY and Sub6.ITEM_KEY > 0 "
                                            + " LEFT JOIN VESSEL_DETAILS v ON i.VES_ID = v.VES_ID AND i.VES_ID != ' ' "
                         + " WHERE i.HIST_FLG != 'Y' and a.TABLE_NAME IN ('ITEM_OOG', 'ITEM_SPECIAL_HANDLING','ITEM_SEAL','ITEM_DANGEROUS','ITEM_DAMAGE','ITEM_SERVICES',"
                                                                      + " 'ITEM_COMMENTS','ITEM_REEFER' , 'ITEM_STOPS', 'ITEM','VESSEL_DETAILS')"
                                  + " AND a.ACTION_CODE in ('INSERT','UPDATE','DELETE') " 
                                  + " AND a.ITEM_NO = '" + itemNo + "' ";
                    flagUnion = true;
                }

                if (filterCondition == string.Empty || filterCondition.Contains("XNBAI"))
                {
                    if (flagUnion)
                    {
                        sql += "\n UNION \n";
                    }
                    sql += "SELECT 'XUAT_NHAP_BAI' AS PHUONG_AN, "
                               + " a.OPER_NAME AS USER_NAME, "
                               + " o.FULL_NAME AS HO_TEN, "
                               + " o.OPER_CLASS_1 AS PHONG_BAN, "
                               + " a.MENU_OPTION AS FORM_THUC_HIEN, "
                               + " CASE WHEN a.ACTION_CODE = 'INSERT' THEN 'THEM'  "
                                    + " WHEN a.ACTION_CODE = 'UPDATE' THEN 'SUA' "
                                    + " WHEN a.ACTION_CODE = 'DELETE' THEN 'XOA' "
                                    + " ELSE a.ACTION_CODE END AS TYPE_WORK, "
                               + " a.CRT_TS AS NGAY_THUC_HIEN, "
                               + " a.TABLE_NAME AS TABLE_NAME , "
                               + " a.ITEM_NAME AS COT_THAY_DOI, "
                               + " a.OLD_VAL AS GIA_TRI_CU, "
                               + " a.NEW_VAL AS GIA_TRI_MOI, "
                               + " d.LINE_OPER AS HANG_TAU, "
                               + " d.ITEM_NO AS SOCONT, "
                               + " d.ISO_CODE, "
                               + " d.SEAL_NO AS SO_SEAL, "
                               + " i.GRADE AS GRADE, "
                               + " d.FEL AS TRANG_THAI, "
                               + " d.CTR_LENGTH AS KICHCO, "
                               + " d.GROSS AS TRONG_LUONG, "
                               + " Sub3.SETTING_TEMP AS SET_TEMP, "
                               + " Sub5.UN_NO AS UN_NO,"
                               + " Sub5.DGS_CLASS AS DGS_CLASS, "
                               + " Sub6.DAMAGESUB  as HU_HONG, " 
                               + " i.AGENT AS CHUKT, "
                               + " ' ' AS TAU_NHAP, "
                               + " ' ' AS CHUYEN_NHAP, "
                               + " v.VES_NAME AS TAU_XUAT, "
                               + " d.TFC_CODE AS CHUYEN_XUAT, "
                               + " d.FPOD AS CANG_CT, "
                               + " d.RECEIVAL_PLACE AS NHAN, "
                               + " d.PLACE_OF_DELIVERY AS GIAO "                                
                         + " FROM AUDIT_TRAIL a JOIN OPERATOR o on o.OPER_NAME = a.OPER_NAME JOIN PREGATE_TRANSACT d ON a.ITEM_NO = d.ITEM_NO "                                            
                                            + " LEFT JOIN ITEM i ON d.ITEM_KEY = i.ITEM_KEY "
                                            + " LEFT OUTER JOIN (SELECT t.ITEM_KEY , RTRIM(LISTAGG ( t.DESCRIPTION || ' , ') WITHIN GROUP (ORDER BY t.CRT_TS DESC), ' , ') SPECIAL_DESCR  "
                                                       + " FROM ITEM_SPECIAL_HANDLING t WHERE t.ITEM_KEY > 0 GROUP BY t.ITEM_KEY ) Sub1 On i.ITEM_KEY = Sub1.ITEM_KEY "
                                            + " LEFT OUTER JOIN ITEM_SEAL Sub2 On i.ITEM_KEY = Sub2.ITEM_KEY and Sub2.INVALID_SEAL != 'Y' and Sub2.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_REEFER Sub3 On i.ITEM_KEY = Sub3.ITEM_KEY and Sub3.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_OOG Sub4 On i.ITEM_KEY = Sub4.ITEM_KEY and Sub4.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ITEM_DANGEROUS Sub5 On i.ITEM_KEY = Sub5.ITEM_KEY and Sub5.ITEM_KEY > 0 "
                                            + " LEFT OUTER JOIN ( SELECT idmg.ITEM_KEY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) AS DAMAGE_BY , sdsv.LOCAL_DESCR SERVICE_DESCR "
                                                                       + " , RTRIM( LISTAGG (nvl(trim(dc.DESCR_VI), trim(idmg.DAMAGE_CD) ) "
                                                                       + " || ' ' || nvl(trim(sdml.LOCAL_DESCR), trim(idmg.DAMAGE_LOC) ) || ' ' || trim(idmg.DAMAGE_DIMENSION) || '-') "
                                                                       + " WITHIN GROUP (ORDER BY idmg.DAMAGE_SEQ DESC), '-') DamageSub "
                                                                       + " FROM ITEM_DAMAGE idmg "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdml ON idmg.DAMAGE_LOC = sdml.CODE_REF AND sdml.CODE_TP = 'DAMAGELOC' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdmb ON idmg.DAMAGE_BY = sdmb.CODE_REF AND sdmb.CODE_TP = 'DAMAGEBY' "
                                                                       + " LEFT OUTER JOIN SYS_CODES sdsv ON idmg.SERVICE_CD = sdsv.CODE_REF AND sdsv.CODE_TP = 'VSSCSERVICE' "
                                                                       + " LEFT OUTER JOIN DAMAGE_CODE dc ON idmg.DAMAGE_CD = dc.DAMAGE_CD "
                                                                       + " WHERE to_char(idmg.FIXEDUP_TS, 'yyyy') = '1900' "
                                                                       + " GROUP BY idmg.ITEM_KEY, idmg.DAMAGE_BY, nvl(trim(sdmb.LOCAL_DESCR), trim(idmg.DAMAGE_BY)) , sdsv.CODE_REF, sdsv.LOCAL_DESCR ) Sub6 on Sub6.ITEM_KEY = d.ITEM_KEY and Sub6.ITEM_KEY > 0 "
                                            + " LEFT JOIN VESSEL_DETAILS v ON i.VES_ID = v.VES_ID AND i.VES_ID != ' ' "
                         + " WHERE a.TABLE_NAME IN ( 'ITEM_OOG', 'ITEM_SPECIAL_HANDLING','ITEM_SEAL','ITEM_DANGEROUS','ITEM_DAMAGE','ITEM_SERVICES', 'TRK_TRANSACT', "
                                                    + " 'ITEM_COMMENTS','ITEM_REEFER' , 'ITEM_STOPS', 'ITEM','PREGATE_REEFER','PREGATE','VESSEL_DETAILS', 'PREGATE_TRANSACT') "
                                  + " AND a.ACTION_CODE in ('INSERT','UPDATE','DELETE') " 
                                  + " AND a.ITEM_NO = '" + itemNo + "'";
                }
                sql += " ) order by NGAY_THUC_HIEN";
                */
                #endregion

                dt = OraDatabase.ExecuteSql(sql);
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return dt;
        }


        //        public DataTable GetHistoryModifyData(string itemNo, DateTime fromDate, DateTime toDate, string filterCondition)
        //        {
        //            DataTable dt = new DataTable();
        //            string sql = string.Empty;
        //            string swhere = string.Empty;
        //            try
        //            {
        //                if (toDate.IsNullDateTime())
        //                {
        //                    toDate = DateTime.Now.ToShortDateString().CheckDateEx();
        //                }

        //                var sWhereDateTime = string.Format(@"AND b.CRT_TS BETWEEN {0} AND {1} ", fromDate.ToOracleDateString(), toDate.AddDays(1).ToOracleDateString());

        //                sql = string.Format(@"                                
        //                                 SELECT DISTINCT 
        //                                    b.MENU_DESC FORM_NHAP_LIEU,
        //                                    CASE WHEN b.AUDIT_DATA_TYPE IN ('DISCHARGE LIST', 'VESSEL')  THEN 'NHAP_TAU' 
        //                                    WHEN b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') THEN 'XUAT_TAU'
        //                                    WHEN b.AUDIT_DATA_TYPE IN ( 'YARD' ) THEN 'TON_BAI'
        //                                    WHEN b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) THEN 'XUAT_NHAP_BAI' 
        //                                    WHEN b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') THEN 'TLHQ'
        //                                    END
        //                                    AS PHUONG_AN,
        //                                    b.OPER_NAME AS USER_NAME,  
        //                                    o.FULL_NAME AS HO_TEN,  
        //                                    o.OPER_CLASS_1 AS PHONG_BAN,    
        //                                    CASE WHEN b.ACTION_CODE = 'INSERT' THEN 'THEM'   
        //                                        WHEN b.ACTION_CODE = 'UPDATE' THEN 'SUA'  
        //                                        WHEN b.ACTION_CODE = 'DELETE' THEN 'XOA' 
        //                                        ELSE b.ACTION_CODE END AS TYPE_WORK,  
        //                                    b.CRT_TS AS NGAY_THAY_DOI,  
        //                                    b.ITEM_NAME AS COT_THAY_DOI,  
        //                                    b.OLD_VAL AS GIA_TRI_CU,  
        //                                    b.NEW_VAL AS GIA_TRI_MOI,     
        //                                    b.TABLE_NAME AS TABLE_NAME, 
        //                                    b.ITEM_NO AS SOCONT, 
        //                                    b.KEY_VALUE, b.KEY_NAME,
        //
        //                                    b.KEY_AUDIT_VALUE KEY_THAM_CHIEU 
        //                                    FROM 
        //                                    (
        //		                                SELECT * FROM 
        //		                                (
        //			                                SELECT a.* FROM AUDIT_TRAIL a
        //			                                INNER JOIN 
        //			
        //			                                (SELECT DISTINCT 
        //				                                t.KEY_NAME, t.KEY_VALUE
        //			                                FROM AUDIT_TRAIL t 
        //			                                WHERE t.SITE_ID = 'CTL' AND t.ITEM_NO = '{0}') i ON i.KEY_NAME = a.KEY_NAME AND i.KEY_VALUE = a.KEY_VALUE
        //		
        //			                                UNION
        //			
        //			                                SELECT a.* FROM AUDIT_TRAIL a
        //			                                INNER JOIN 
        //			
        //			                                (SELECT DISTINCT 
        //				                                t.KEY_NAME, t.KEY_VALUE
        //			                                FROM AUDIT_TRAIL t 
        //				                                INNER JOIN ITEM_CHANGE_CTRNO x ON x.NEW_ITEM_NO = '{0}'
        //			                                WHERE t.SITE_ID = 'CTL' AND t.ITEM_NO = x.OLD_ITEM_NO --AND t.CRT_TS < x.CRT_TS
        //			                                ) i ON i.KEY_NAME = a.KEY_NAME AND i.KEY_VALUE = a.KEY_VALUE
        //			
        //			                                UNION
        //			
        //			                                SELECT a.* FROM AUDIT_TRAIL a
        //			                                INNER JOIN 
        //			
        //			                                (SELECT DISTINCT 
        //				                                t.KEY_NAME, t.KEY_VALUE
        //			                                FROM AUDIT_TRAIL t 
        //				                                INNER JOIN ITEM_CHANGE_CTRNO x ON x.OLD_ITEM_NO = '{0}'
        //			                                WHERE t.SITE_ID = 'CTL' AND t.ITEM_NO = x.NEW_ITEM_NO --AND t.CRT_TS > x.CRT_TS
        //			                                ) i ON i.KEY_NAME = a.KEY_NAME AND i.KEY_VALUE = a.KEY_VALUE
        //		                                )
        //                                    ) b
        //
        //                                    LEFT JOIN OPERATOR o on trim(o.OPER_NAME) = trim(b.OPER_NAME)
        //
        //                                    WHERE b.AUDIT_DATA_TYPE NOT IN (' ' , 'PRODUCTIVITY') AND 
        //                                    B.OLD_VAL != ( CASE WHEN b.ACTION_CODE = 'UPDATE' THEN b.NEW_VAL ELSE b.TABLE_NAME END )
        //                                    {1}
        //                                ", itemNo, sWhereDateTime);

        //                if (filterCondition.Contains("NTAU"))
        //                {
        //                    swhere += " b.AUDIT_DATA_TYPE IN ('DISCHARGE LIST', 'VESSEL') ";
        //                }
        //                if (filterCondition.Contains("XTAU"))
        //                {
        //                    if (swhere == string.Empty)
        //                    {
        //                        swhere += " b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') ";
        //                    }
        //                    else
        //                    {
        //                        sql += " or b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') ";
        //                    }
        //                }
        //                if (filterCondition.Contains("TBAI"))
        //                {
        //                    if (swhere == string.Empty)
        //                    {
        //                        swhere += " b.AUDIT_DATA_TYPE IN ( 'YARD' ) ";
        //                    }
        //                    else
        //                    {
        //                        swhere += " or b.AUDIT_DATA_TYPE IN ( 'YARD' ) ";
        //                    }
        //                }
        //                if (filterCondition.Contains("XNBAI"))
        //                {
        //                    if (swhere == string.Empty)
        //                    {
        //                        swhere += " b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) ";
        //                    }
        //                    else
        //                    {
        //                        swhere += " or b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) ";
        //                    }
        //                }
        //                if (filterCondition.Contains("TLHQ"))
        //                {
        //                    if (swhere == string.Empty)
        //                    {
        //                        swhere += " b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') ";
        //                    }
        //                    else
        //                    {
        //                        swhere += " or b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') ";
        //                    }
        //                }                
        //                if (!swhere.IsEmpty())
        //                {
        //                    swhere = "AND (" + swhere + ")";
        //                }

        //                sql = sql + swhere + " order by NGAY_THAY_DOI DESC ";

        //                dt = OraDatabase.ExecuteSql(sql);
        //            }
        //            catch (Exception ex)
        //            {
        //                DaExceptionHandler.HandleException(ref ex);
        //            }
        //            return dt;
        //        }


        public DataTable GetHistoryModifyData(string itemNo, DateTime fromDate, DateTime toDate, string filterCondition)
        {
            DataTable dt = new DataTable();
            string sql = string.Empty;
            string swhere = string.Empty;
            try
            {
                if (toDate.IsNullDateTime())
                {
                    toDate = DateTime.Now.ToShortDateString().CheckDateEx();
                }

                var sWhereDateTime = "";
                if (fromDate.Year > 1901 || toDate.Year > 1901)
                {
                    sWhereDateTime = $@"AND b.CRT_TS BETWEEN {fromDate.ToOracleDateString()} AND {toDate.AddDays(1).ToOracleDateString()} ";
                }

                sql = string.Format(@"                                
                 SELECT DISTINCT 
                    b.MENU_DESC FORM_NHAP_LIEU,
                    CASE WHEN b.AUDIT_DATA_TYPE IN ('DISCHARGE LIST', 'VESSEL')  THEN 'NHAP_TAU' 
                    WHEN b.AUDIT_DATA_TYPE IN ('BAPLIE')  THEN 'DAO_TAU'
                    WHEN b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') THEN 'XUAT_TAU'
                    WHEN b.AUDIT_DATA_TYPE IN ( 'YARD' ) THEN 'TON_BAI'
                    WHEN b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) THEN 'XUAT_NHAP_BAI' 
                    WHEN b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') THEN 'TLHQ'
                    END
                    AS PHUONG_AN,
                    b.OPER_NAME AS USER_NAME,  
                    o.FULL_NAME AS HO_TEN,  
                    o.OPER_CLASS_1 AS PHONG_BAN,    
                    CASE WHEN b.ACTION_CODE = 'INSERT' THEN 'THEM'   
                        WHEN b.ACTION_CODE = 'UPDATE' THEN 'SUA'  
                        WHEN b.ACTION_CODE = 'DELETE' THEN 'XOA' 
                        ELSE b.ACTION_CODE END AS TYPE_WORK,  
                    b.CRT_TS AS NGAY_THAY_DOI,  
                    b.ITEM_NAME AS COT_THAY_DOI,  
                    b.OLD_VAL AS GIA_TRI_CU,  
                    b.NEW_VAL AS GIA_TRI_MOI,     
                    b.TABLE_NAME AS TABLE_NAME, 
                    b.ITEM_NO AS SOCONT, 
                    b.KEY_VALUE, b.KEY_NAME,

                    b.KEY_AUDIT_VALUE KEY_THAM_CHIEU 
                    FROM 
                    (
                        SELECT a.* FROM AUDIT_TRAIL a WHERE a.ITEM_NO =  '{0}' 

                        UNION

                        SELECT a.* FROM AUDIT_TRAIL a
	                    INNER JOIN 
	                    (   SELECT DISTINCT 
		                        t.KEY_NAME, t.KEY_VALUE
	                        FROM AUDIT_TRAIL t 
	                        WHERE t.SITE_ID = '{2}' AND t.ITEM_NO = '{0}' 
                            AND decode(trim(t.KEY_VALUE), '0' ,NULL,'-2147483648', NULL , t.KEY_VALUE) IS NOT NULL
                        ) i ON i.KEY_NAME = a.KEY_NAME AND i.KEY_VALUE = a.KEY_VALUE
	                    
		                --SELECT * FROM AUDIT_TRAIL atl WHERE atl.ITEM_NO = '{0}'
		                UNION
		                SELECT * FROM AUDIT_TRAIL atl
		                WHERE (atl.KEY_VALUE, atl.key_name) 
				                IN 
				                (
				                SELECT to_char(ITEM_KEY), CAST('ITEM_KEY' AS CHAR(25) )  FROM ITEM WHERE ITEM_NO = '{0}'
				                UNION
				                SELECT to_char(MANIFEST_KEY), CAST('MANIFEST_KEY' AS CHAR(25) )  FROM DISCHARGE_LIST WHERE ITEM_NO = '{0}'
				                UNION
				                SELECT to_char(BAPLIE_KEY), CAST('BAPLIE_KEY' AS CHAR(25))  FROM BAPLIE WHERE CTR_NO = '{0}'  
				                UNION
				                SELECT to_char(NEW_ITEM_KEY), CAST('ITEM_KEY' AS CHAR(25))  FROM ITEM_CHANGE_CTRNO WHERE OLD_ITEM_NO = '{0}' 
                                UNION
				                SELECT to_char(OLD_ITEM_KEY), CAST('ITEM_KEY' AS CHAR(25))  FROM ITEM_CHANGE_CTRNO WHERE NEW_ITEM_NO = '{0}'   
				                )
                    ) b

                    LEFT JOIN OPERATOR o on trim(o.OPER_NAME) = trim(b.OPER_NAME)

                    WHERE b.AUDIT_DATA_TYPE NOT IN (' ' , 'PRODUCTIVITY') AND 
                    B.OLD_VAL != ( CASE WHEN b.ACTION_CODE = 'UPDATE' THEN b.NEW_VAL ELSE b.TABLE_NAME END )
                    {1}
                ", itemNo, sWhereDateTime, GlobalSettings.SiteId);

                if (filterCondition.Contains("NTAU"))
                {
                    swhere += " b.AUDIT_DATA_TYPE IN ('DISCHARGE LIST', 'VESSEL') ";
                }
                if (filterCondition.Contains("XTAU"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') ";
                    }
                    else
                    {
                        sql += " or b.AUDIT_DATA_TYPE IN ( 'LOAD LIST') ";
                    }
                }
                if (filterCondition.Contains("TBAI"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " b.AUDIT_DATA_TYPE IN ( 'YARD' ) ";
                    }
                    else
                    {
                        swhere += " or b.AUDIT_DATA_TYPE IN ( 'YARD' ) ";
                    }
                }
                if (filterCondition.Contains("XNBAI"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) ";
                    }
                    else
                    {
                        swhere += " or b.AUDIT_DATA_TYPE IN ( 'PREGATE' ) ";
                    }
                }
                if (filterCondition.Contains("TLHQ"))
                {
                    if (swhere == string.Empty)
                    {
                        swhere += " b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') ";
                    }
                    else
                    {
                        swhere += " or b.AUDIT_DATA_TYPE IN ('CUSTOM CLEARANC') ";
                    }
                }
                if (!swhere.IsEmpty())
                {
                    swhere = "AND (" + swhere + ")";
                }

                sql = sql + swhere + " order by NGAY_THAY_DOI DESC ";

                dt = OraDatabase.ExecuteSql(sql);
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return dt;
        }


        /// <summary>
        /// Lấy thông tin bãi từ block - bay
        /// </summary>
        /// <param name="block"></param>
        /// <param name="bay"></param>
        /// <returns></returns>
        public string GetYardArea(string block, string bay)
        {
            try
            {
                //var sqlSelect = new YardArea().GetSelectByAssignedFields();
                var sql = string.Format("SELECT AREA FROM YARD_AREA WHERE STACK = '{0}' AND X1 <= '{1}' AND '{1}' <= X2", block, bay);

                if (!string.IsNullOrEmpty(sql))
                {
                    var queryResults = OraDatabase.ExecuteSql(sql);
                    if (queryResults != null && queryResults.Rows.Count > 0)
                    {
                        return queryResults.Rows[0][0].ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return "";

        }

        public YardArea GetYardArea(string block, string bay, DateTime execTs)
        {
            try
            {

                var sql = string.Format("SELECT * FROM YARD_AREA WHERE STACK = '{0}' AND X1 <= '{1}' AND '{1}' <= X2 AND FR_DATE <= {2} AND {2} <= TO_DATE ", block, bay, execTs.ToOracleDateString());
                if (bay.IsEmpty())
                {
                    sql = string.Format("SELECT * FROM YARD_AREA WHERE STACK = '{0}' AND FR_DATE <= {1} AND {1} <= TO_DATE ", block, execTs.ToOracleDateString());
                }

                if (!string.IsNullOrEmpty(sql))
                {
                    var queryResults = OraDatabase.ExecuteSql(sql);
                    if (queryResults != null && queryResults.Rows.Count > 0)
                    {
                        return (from d in queryResults.AsEnumerable()
                                select new YardArea(d)).ToList().LastOrDefault();
                    }
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }
        /// <summary>
        /// Get list container arrival by vessel
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public int CountContainerArrivalByVessel(string tfcCodeI)
        {
            var sql = $@"
                            SELECT COUNT(*) FROM ITEM WHERE ARR_BY= 'V' AND ARR_CAR = '{tfcCodeI}'
                        ";

            var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();
            return count;
        }

        /// <summary>
        /// Check to see if the given container No is currently in yard
        /// </summary>
        /// <param name="containerNo"></param>
        /// <returns></returns>
        public bool IsContainerCurrentInYard(string containerNo)
        {
            var sql = $@"
                            SELECT 1 FROM ITEM i, ITEM_LOCATION il 
                                    WHERE i.ITEM_KEY = il.ITEM_KEY 
                                        AND i.HIST_FLG <> 'Y' 
                                        AND il.STK_PCH = 'C' 
                                        AND il.STK_CLASS = 'Y'
                                        AND i.ITEM_NO = '{containerNo}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null;
        }

        /// <summary>
        /// Check to see if the given container No is gate in and pick to yard location already : for custom clearance
        /// </summary>
        /// <param name="containerNo"></param>
        /// <returns></returns>
        public bool IsContainerGateInAndInYard(string containerNo)
        {
            var sql = $@"
                            SELECT count(1) FROM ITEM i, ITEM_LOCATION il 
                                    WHERE i.ITEM_KEY = il.ITEM_KEY 
                                        AND (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ' )
                                        AND (il.STK_PCH = 'H' OR il.STK_PCH = 'C' )
                                        AND il.STK_CLASS = 'Y'
                                        AND i.ITEM_NO = '{containerNo}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql).CheckIntEx();
            return sqlResult > 0;
        }


        /// <summary>
        /// Check to see if the given container No is currently in yard
        /// </summary>
        /// <param name="containerNo"></param>
        /// <returns></returns>
        public DataTable GetContainerCurrentInYard(string containerNo)
        {
            var sql = $@"
                            SELECT 1 FROM ITEM i, ITEM_LOCATION il 
                                    WHERE i.ITEM_KEY = il.ITEM_KEY 
                                        AND i.HIST_FLG <> 'Y' 
                                        AND il.STK_PCH = 'C' 
                                        AND il.STK_CLASS = 'Y'
                                        AND i.ITEM_NO = '{containerNo}'
                        ";

            var sqlResult = OraDatabase.ExecuteSql(sql);
            return sqlResult;
        }

        /// <summary>
        /// The function returns the max value of seq no of special handling code of the given item key
        /// </summary>
        /// <param name="itemKey"></param>
        public int GetMaxItemSpecHdlCodeSeqNo(int itemKey)
        {
            var sql = $@"
                            SELECT max(seq_no) FROM ITEM_SPECIAL_HANDLING WHERE ITEM_KEY = '{itemKey}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// The function returns the max value of seq no of special handling code of the given manifest key key
        /// </summary>
        public int GetMaxDischSpecHdlCodeSeqNo(int manifestKey)
        {
            var sql = $@"
                            SELECT max(seq_no) FROM DISCH_SPECIAL_HANDLING WHERE MANIFEST_KEY = '{manifestKey}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// The function returns the max value of seq no of comments of the given item key
        /// </summary>
        /// <param name="itemKey"></param>
        public int GetMaxItemCommentsSeqNo(int itemKey)
        {
            var sql = $@"
                            SELECT max(COMMENT_SEQ) FROM ITEM_COMMENTS WHERE ITEM_KEY = '{itemKey}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// The function returns the max value of seq no of item seal of the given item key
        /// </summary>
        /// <param name="itemKey"></param>
        public int GetMaxItemSeal(int itemKey)
        {
            var sql = $@"
                            SELECT max(seq_no) FROM ITEM_SEAL WHERE ITEM_KEY = '{itemKey}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// Determines whether the given container has gated in or not
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns></returns>
        public bool IsContainerGatedIn(int itemKey)
        {
            var sql = $@"
                            SELECT 1 FROM ITEM WHERE ITEM_KEY = '{itemKey}' AND ARR_TS <> {DateTimeUtilities.GetDbStringNullDate()} AND HIST_FLG <> 'Y'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null;
        }


        /// <summary>
        /// Determines whether the given container has gated in or not
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <returns></returns>
        public bool IsContainerGatedIn(string ctnrNo)
        {
            var sql = $@"
                            SELECT 1 FROM ITEM WHERE ITEM_NO = '{ctnrNo}' AND ARR_TS <> {DateTimeUtilities.GetDbStringNullDate()} AND HIST_FLG <> 'Y'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null;
        }

        /// <summary>
        /// Check to see if container has been registered in another pregate transaction
        /// </summary>
        /// <param name="eirId"></param>
        /// <param name="itemKey"></param>
        /// <param name="operMethod"></param>
        /// <returns></returns>
        public bool IsContainerInOtherPregate(string eirId, int itemKey, string operMethod)
        {

            var sql = string.Format(@"
                            SELECT 1 FROM PREGATE_TRANSACT WHERE ITEM_KEY = {0} AND EIR_ID <> {1} AND (OPERATION_METHOD = '{2}' || {2} = ' ' ) AND HIST_FLG <> 'Y'
                        ", itemKey, eirId.CheckBlankEx(), operMethod.CheckBlankEx());

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null;
        }

        public bool SyncDataFromDischargeListToBaplie(int manifestKey)
        {
            var sql = string.Format(@"");

            return OraDatabase.ExecuteSingleNonQuery(sql) > 0;
        }


        /// <summary>
        /// Get container in yard which has departure detail by DepCar
        /// Condition: Dep Car
        /// ARR_TS > 31/12/1900
        /// </summary>
        /// <param name="depCar"></param>
        /// <returns></returns>
        public List<Item> GetInYardItemByDepCar(string depCar)
        {
            try
            {
                var sqlSelect = new Item().GetSelectByAssignedFields();
                var sqlWhere = $"WHERE DEP_BY = 'V' AND DEP_CAR = '{depCar}' "; //AND extract (YEAR FROM ARR_TS) > 1900

                if (!string.IsNullOrEmpty(sqlSelect))
                {
                    sqlSelect += sqlWhere;

                    var queryResults = OraDatabase.ExecuteSql(sqlSelect);
                    return (from d in queryResults.AsEnumerable()
                            select new Item(d)).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get container in yard which has line operator by lineOper
        /// Condition: LINE_OPER = lineOper
        /// ARR_TS > 31/12/1900
        /// </summary>
        /// <param name="lineOper"></param>
        /// <returns></returns>
        public List<Item> GetInYardItemsByLineOper(string lineOper)
        {
            try
            {
                var sqlSelect = new Item().GetSelectByAssignedFields();
                var sqlWhere =
                    $"WHERE LINE_OPER = '{lineOper}' AND extract(YEAR FROM ARR_TS) > 1900 AND extract(YEAR FROM DEP_TS) = 1900 ";

                if (!string.IsNullOrEmpty(sqlSelect))
                {
                    sqlSelect += sqlWhere;

                    var queryResults = OraDatabase.ExecuteSql(sqlSelect);
                    return (from d in queryResults.AsEnumerable()
                            select new Item(d)).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get current location all container, which already in yard or load to vessel
        /// condition: container exit by the vessel: tfcCode
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ItemLocation> GetCurrentLocationContainerInLoadList(string tfcCode)
        {
            string sql = $@"
                                Select il.ITEM_KEY, il.STK_REF, il.STK_CLASS, il.STACK, il.X, il.Y, il.Z
                                FROM ITEM i
	                                INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
                                Where i.DEP_BY = 'V' AND i.DEP_CAR = '{tfcCode}' ";

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ItemLocation(d)).ToList();
        }

        /// <summary>
        /// Get current stop all container, which in load list
        /// condition: container exit by the vessel: tfcCode
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ItemStops> GetCurrentStopContainersInLoadList(string tfcCode)
        {
            string sql = $@"
                                Select i.ITEM_KEY, i.STOP_CD
                                FROM EDI_ITEM_PRE_ADVICE eipa
	                                INNER JOIN ITEM_STOPS i ON i.ITEM_KEY = eipa.ITEM_KEY AND i.CLR_BY = ' '
                                Where eipa.TFC_CODE = '{tfcCode}' ";

            var queryresult = OraDatabase.ExecuteSql(sql);

            return (from d in queryresult.AsEnumerable()
                    select new ItemStops(d)).ToList();
        }

        /// <summary>
        /// Get current seal no containers, which in load list
        /// condition: contianer exit by the vessel: tfcCode
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ItemSeal> GetCurrentSealContainerInLoadList(string tfcCode)
        {
            string sql = $@"
                                Select i.ITEM_KEY, i.SEAL_TYPE, i.SEAL_NO, i.INVALID_SEAL
                                FROM EDI_ITEM_PRE_ADVICE eipa
	                                INNER JOIN ITEM_SEAL i ON i.ITEM_KEY = eipa.ITEM_KEY AND (i.INVALID_SEAL = 'N' OR i.INVALID_SEAL = ' ')
                                Where eipa.TFC_CODE = '{tfcCode}' ";

            var queryresult = OraDatabase.ExecuteSql(sql);

            return (from d in queryresult.AsEnumerable()
                    select new ItemSeal(d)).ToList();
        }

        /// <summary>
        /// Update container's cargo
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="cargo"> </param>
        public void UpdateItemCargo(int itemKey, double cargo)
        {
            try
            {
                var sql = string.Format(@"                            
                                        UPDATE ITEM
                                        SET 
	                                        CGO_GROSS_WT = CGO_GROSS_WT + {0},
                                            GROSS = TARE + CGO_GROSS_WT + {0}
                                        WHERE ITEM_KEY = {1} 
                        ", cargo, itemKey);

                OraDatabase.ExecuteNonQuery(sql);
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);

            }

        }

        /// <summary>
        /// Update field Seal_No_Current in ITEM by the newest seal in ITEM_SEAL
        /// </summary>
        /// <param name="itemKey"></param>
        public void UpdateItemSealNoCurrent(int itemKey)
        {
            try
            {
                string sql = $@"
                                SELECT SEAL_NO FROM ITEM_SEAL WHERE ITEM_KEY  = '{itemKey}' AND INVALID_SEAL <> 'Y' ORDER BY CRT_TS DESC
                                            ";

                var latestSealNo = OraDatabase.ExecuteScalar(sql);
                if (latestSealNo != null)
                {
                    OraDatabase.UpdateDbValueObjectOnCondition(new Item { SealNoCurrent = latestSealNo.ToString() },
                                                               new Item { ItemKey = itemKey });
                }

            }
            catch (Exception ex)
            {

                DaExceptionHandler.HandleException(ref ex);
            }
        }



        /// <summary>
        /// Set the last history location of container to be the current location
        /// </summary>
        public void SetLastHistLocToCurrent(int itemKey)
        {
            try
            {
                var sql = string.Format(@"                            
                                        UPDATE ITEM_LOCATION
                                        SET STK_PCH = 'C'
                                        WHERE ITEM_KEY = {0} 
	                                        AND EXEC_TS = (SELECT max(EXEC_TS) FROM ITEM_LOCATION WHERE ITEM_KEY = {0})

                        ", itemKey);

                OraDatabase.ExecuteNonQuery(sql);
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);

            }

        }

        /// <summary>
        /// Get planned location of the given container. 
        /// If container is in ITEM, then check planned loc in ITEM_LOCATION, else checking it in TG2VPT
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <param name="isNotCountY75AsPlan"></param>
        public bool IsContainerHasPlanLoc(string ctnrNo, bool isNotCountY75AsPlan)
        {
            try
            {
                //9Z: NO PLAN (Y75)
                var sql = $@"
                            SELECT 1 FROM TG2VPT 
                                    WHERE TGCTNR = '{ctnrNo}' AND TGCPFG <> 'Y'
                        ";

                if (isNotCountY75AsPlan)
                {
                    sql += " AND (trim(TGLYDD) || trim(TGLARD)) <> '9Z' ";
                }


                //--AND trim(TGLYDD) || trim(TGLARD) <> '9Z'
                //            --UNION
                //            --SELECT 1 FROM ITEM i, ITEM_LOCATION il 
                //            --        WHERE i.ITEM_KEY = il.ITEM_KEY AND i.ITEM_NO = '{0}' AND il.STK_PCH = 'P' AND (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ')

                var sqlResult = OraDatabase.ExecuteScalar(sql);
                return sqlResult != null;
            }
            catch (Exception ex)
            {

                DaExceptionHandler.HandleException(ref ex);
            }

            return false;
        }

        /// <summary>
        /// Check if container has plan vessel 
        /// action code = 11 - Load or 17 - Truck Delivery
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <param name="refVessel"></param>
        /// <param name="planLoc"></param>
        public bool IsContainerHasDeliveryPlan(string ctnrNo, string refVessel, ref string planLoc)
        {
            try
            {

                var sql = $@"
                            SELECT (trim(TGVLCD)|| '-' ||trim(TGVYMX)) FROM TG2VPT 
                                    WHERE TGCTNR = '{ctnrNo}' AND TGCPFG <> 'Y' AND (tgactn = '17' OR tgactn = '11')
                        ";

                var sqlResult = OraDatabase.ExecuteScalar(sql);

                if (sqlResult.TrimEx().IsEmpty())
                {
                    planLoc = "";
                    return false;
                }

                planLoc = sqlResult.TrimEx();

                if (refVessel.IsEmpty())
                {
                    return !planLoc.IsEmpty();
                }

                return planLoc != refVessel;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return false;
        }

        public string GetVesselDeliveryPlan(string ctnrNo)
        {
            try
            {

                var sql = $@"
                            SELECT (trim(TGVLCD)|| '-' ||trim(TGVYMX)) FROM TG2VPT 
                                    WHERE TGCTNR = '{ctnrNo}' AND TGCPFG <> 'Y' AND (tgactn = '17' OR tgactn = '11')
                        ";

                var sqlResult = OraDatabase.ExecuteScalar(sql).TrimEx();

                return sqlResult;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return "";
        }


        /// <summary>
        /// check if container is pregate receival
        /// </summary>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public bool IsContainerRecceivalPregate(string itemNo)
        {
            var sql =
                $@"SELECT COUNT(*) FROM PREGATE_TRANSACT WHERE ITEM_NO = '{itemNo}' AND R_D = 'R' AND (HIST_FLG = ' ' OR HIST_FLG = 'N') ";

            return OraDatabase.ExecuteScalar(sql).CheckIntEx() > 0;
        }

        /// <summary>
        /// check if container is pregate delivery
        /// </summary>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public bool IsContainerDeliveryPregate(string itemNo)
        {
            var sql =
                $@"SELECT COUNT(*) FROM PREGATE_TRANSACT WHERE ITEM_NO = '{itemNo}' AND R_D = 'D' AND (HIST_FLG = ' ' OR HIST_FLG = 'N') ";

            return OraDatabase.ExecuteScalar(sql).CheckIntEx() > 0;
        }


        /// <summary>
        /// Get list container currently on vessel by checking ITEM_LOCATION
        /// </summary>
        /// <param name="vesId"> </param>
        public List<Item> GetContainerCurrentlyOnVessel(string vesId)
        {
            try
            {
                var sql = $@"
                        SELECT i.ITEM_KEY, i.ITEM_NO, i.ARR_BY, i.ARR_CAR, i.ARR_TS, i.DEP_BY, i.DEP_CAR, i.DEP_TS 
                        FROM ITEM i 
	                        INNER JOIN VESSEL_DETAILS v ON v.VES_ID = '{vesId}' AND ((i.DEP_BY = 'V' AND i.DEP_CAR = v.TFC_CODE_E) OR (i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_E))
	                        INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C' AND il.STK_CLASS = 'V' AND il.STK_REF = v.VES_ID
                        WHERE (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ')
                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        /// <summary>
        /// Get list container departure by Vessel but not move history
        /// </summary>
        /// <param name="vesId"> </param>
        public List<Item> GetContainerLoadedOnVesselNotMoveHistory(string vesId)
        {
            try
            {
                var sql = $@"
                        SELECT i.ITEM_KEY, i.ITEM_NO, i.ARR_BY, i.ARR_CAR, i.ARR_TS, i.DEP_BY, i.DEP_CAR, i.DEP_TS 
                        FROM ITEM i 
	                        INNER JOIN VESSEL_DETAILS v ON v.VES_ID = '{vesId}'
                        WHERE i.DEP_BY = 'V' AND i.DEP_CAR = v.TFC_CODE_E AND (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ') AND extract(Year from i.DEP_TS) > 1900
                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        /// <summary>
        /// Get list container currently on vessel by checking ITEM_LOCATION
        /// </summary>
        /// <param name="vesId"> </param>
        public List<Item> GetContainerDepartByVesselMissingDepTs(string vesId)
        {
            try
            {
                var sql = $@"
                        SELECT i.ITEM_KEY, i.ITEM_NO, i.ARR_BY, i.ARR_CAR, i.ARR_TS, i.DEP_BY, i.DEP_CAR, i.DEP_TS, i.HIST_FLG
                        FROM VESSEL_DETAILS v 
                            INNER JOIN ITEM i ON i.DEP_BY= 'V' AND i.DEP_CAR = v.TFC_CODE_E
                            INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C' AND il.STK_CLASS = 'V' AND il.STK_REF = v.VES_ID
                        WHERE v.VES_ID = '{vesId}' AND (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ') AND extract(YEAR FROM i.DEP_TS) = 1900 AND i.CATEGORY NOT IN ('R','S')
                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        /// <summary>
        /// Get list container currently on vessel by checking ITEM_LOCATION
        /// </summary>
        /// <param name="vesId"> </param>
        public List<Item> GetContainerDepartByVesselMissingDepCar(string vesId)
        {
            try
            {
                var sql = $@"
                        SELECT i.ITEM_KEY, i.ITEM_NO, i.ARR_BY, i.ARR_CAR, i.ARR_TS, i.DEP_BY, i.DEP_CAR, i.DEP_TS, i.HIST_FLG
                        FROM VESSEL_DETAILS v 
                            INNER JOIN EDI_ITEM_PRE_ADVICE e ON e.TFC_CODE = v.TFC_CODE_E 
                            INNER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND i.DEP_CAR = ' ' --AND (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ')
                            INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C' AND il.STK_CLASS = 'V' AND il.STK_REF = v.VES_ID
                        WHERE v.VES_ID = '{vesId}'  
                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        /// <summary>
        /// Update Missing arrival car for discharge container
        /// </summary>
        /// <param name="vesId"> </param>
        public void UpdateDischargedItemMissingArrCar(string vesId)
        {
            try
            {
                var sql = $@"
                        UPDATE ITEM i
                        SET (i.ARR_BY, i.ARR_CAR) = 
                        (
	                        SELECT 'V', d.TFC_CODE FROM DISCHARGE_LIST d WHERE d.ITEM_KEY = i.ITEM_KEY 
                        )
                        WHERE i.ARR_CAR = ' ' AND i.HIST_FLG <> 'Y' AND EXISTS (SELECT 1 FROM DISCHARGE_LIST WHERE ITEM_KEY = i.ITEM_KEY AND VES_ID = '{vesId}')
                        ";

                OraDatabase.ExecuteNonQuery(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
        }

        /// <summary>
        /// Update missing arrival time for discharge container
        /// </summary>
        public void UpdateDischargedItemMissingArrTime(string vesId, string tfcCodeI)
        {
            try
            {
                var sql = string.Format(@"
                        
                        UPDATE ITEM i
                        SET i.ARR_TS =  
                        (
                            SELECT il.EXEC_TS  FROM ITEM_LOCATION il 
                            WHERE il.ITEM_KEY = i.ITEM_KEY AND il.STK_REF <> '{0}' AND il.PREV_LOC <> ' '
    	                        AND NOT EXISTS (SELECT 1 FROM ITEM_LOCATION t WHERE t.ITEM_KEY = il.ITEM_KEY AND t.STK_REF <> '{0}' AND t.PREV_LOC <> ' ' AND t.EXEC_TS < il.EXEC_TS)
                        )
                        WHERE i.ARR_BY = 'V' AND i.ARR_CAR = '{1}' AND  extract(YEAR FROM i.ARR_TS) = 1900 
                            AND EXISTS 
                            (
                                SELECT 1 FROM DISCHARGE_LIST d 
                                    INNER JOIN ITEM_LOCATION x ON x.ITEM_KEY = d.ITEM_KEY AND x.STK_REF <> d.VES_ID AND x.PREV_LOC <> ' '
                                WHERE d.ITEM_KEY = i.ITEM_KEY AND d.TFC_CODE = '{1}'
                            )

                        ", vesId, tfcCodeI);

                OraDatabase.ExecuteNonQuery(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
        }


        /// <summary>
        /// Get item key of master container of the given attached container
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <returns></returns>
        public int GetMasterItemKey(string ctnrNo)
        {
            try
            {
                var sql = $@"
                        SELECT i.ITEM_KEY FROM ITEM i, ITEM_SUBS isub WHERE i.ITEM_KEY = isub.ITEM_KEY AND isub.ITEM_ID = '{ctnrNo}' AND isub.NEW_ATT_KEY = 0
                        ";

                var masterItemKey = OraDatabase.ExecuteScalar(sql);

                return masterItemKey.CheckIntEx();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return 0;
        }


        /// <summary>
        /// Get tare of the given container. This method gets tare of the latest created container that its tare is greater than 0
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <returns></returns>
        public double GetContainerTare(string ctnrNo)
        {
            try
            {
                var sql = $@"
                        SELECT TARE FROM ITEM WHERE ITEM_NO = '{ctnrNo}' AND TARE > 0 AND ROWNUM = 1
                        ";

                var ctnrTare = OraDatabase.ExecuteScalar(sql);

                return ctnrTare.CheckDoubleEx();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return 0;
        }



        /// <summary>
        /// Get container's yard area
        /// Return null if it is not found.
        /// </summary>
        /// <param name="itemKey"></param>
        public YardArea GetCtnrYardArea(int itemKey)
        {
            YardArea yardArea = null;
            string sql = $@"
                            
                                          SELECT yad.* FROM YARD_AREA yad, ITEM_LOCATION il
                                          WHERE yad.STACK = il.STACK --AND (nvl(trim(il.X), ' ') = ' ' OR il.X  >= yad.X1) AND (nvl(trim(il.X), ' ') = ' ' OR il.X <= yad.X2) 
                                          AND il.STK_PCH = 'C' AND il.ITEM_KEY = '{itemKey}' 
                                          ORDER BY yad.FR_DATE desc


                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new YardArea(record);

            if (result.Any())
            {
                yardArea = result.First();
            }

            return yardArea;
        }

        /// <summary>
        /// Get list of warning special handling codes 
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <param name="screenId"></param>
        /// <returns></returns>
        public List<ItemSpecialHandling> GetWarningSpecialHandling(string ctnrNo, string screenId = "")
        {
            try
            {
                var sql = $@"
                        SELECT ish.* FROM ITEM i
		                        INNER JOIN  ITEM_SPECIAL_HANDLING ish ON i.ITEM_KEY = ish.ITEM_KEY
		                        INNER JOIN SPECIAL_HDL_CODE shc ON ish.CODE = shc.SPECIAL_HDL_CODE AND shc.WARNING_FLG = 'Y'
                        WHERE i.ITEM_NO = '{ctnrNo}' AND i.HIST_FLG <> 'Y'	 AND (nvl(trim(shc.SCREEN_IDS), ' ') = ' '  OR shc.SCREEN_IDS LIKE '%{screenId}%')	

                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new ItemSpecialHandling(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ItemSpecialHandling>();

        }

        /// <summary>
        /// Get list of warning special handling codes 
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <param name="screenId"></param>
        /// <returns></returns>
        public List<ItemStops> GetWarningStopCodes(string ctnrNo, string screenId)
        {
            try
            {
                var sql = $@"
                       SELECT ist.* FROM ITEM i
		                        INNER JOIN ITEM_STOPS ist ON i.ITEM_KEY = ist.ITEM_KEY AND nvl(ist.CLR_BY, ' ') = ' '
		                        INNER JOIN CONFIG_STOPCODE cs ON cs.STOP_CODE = ist.STOP_CD AND cs.WARNING_FLG = 'Y' 
                        WHERE i.ITEM_NO = '{ctnrNo}' AND i.HIST_FLG <> 'Y'  AND (nvl(trim(cs.SCREEN_IDS), ' ') = ' '  OR cs.SCREEN_IDS LIKE '%{screenId}%')		

                        ";

                var queryResult = OraDatabase.ExecuteSql(sql);
                return (from d in queryResult.AsEnumerable()
                        select new ItemStops(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ItemStops>();

        }


        /// <summary>
        /// Get history change departure car by user
        /// </summary>
        /// <returns></returns>
        public DataTable GetHistoryChangeDeparture(string username, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var whereOper = username.IsEmpty() ? "" : $" AND d.OPER_NAME = '{username}' ";
                var whereDate = "";

                if (fromDate.IsNotNull())
                {
                    whereDate += " AND d.CRT_TS >= " + fromDate.ToOracleDateString();
                }

                if (toDate.IsNotNull())
                {
                    whereDate += " AND d.CRT_TS < " + fromDate.AddDays(1).ToOracleDateString();
                }

                var sql = $@"
                        
                        SELECT i.ITEM_NO,  d.FROM_DEP_CAR, d.TO_DEP_CAR, d.FROM_POD, d.TO_POD, d.CUST_REG_NO, d.PAY_METHOD, d.CRT_TS, d.OPER_NAME
	                        FROM Item_Change_Depature d
	                        INNER JOIN ITEM i ON i.ITEM_KEY	 = d.ITEM_KEY
                        WHERE 1= 1 {whereOper} {whereDate}
                        ORDER BY d.CRT_TS DESC
                        ";

                return OraDatabase.ExecuteSql(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new DataTable();
        }

        /// <summary>
        /// Get PTI info from DateTime - to DateTime
        /// </summary>
        /// <returns></returns>
        public List<ItemPti> GetItemPtiList(DateTime fromDate, DateTime toDate)
        {
            List<ItemPti> listPti = new List<ItemPti>();
            try
            {
                var sql = $@"SELECT ip.* FROM ITEM_PTI ip
                                        WHERE ip.CRT_TS BETWEEN to_date('{fromDate.DateTimeToString()}', 'dd/MM/yyyy hh24:mi:ss') AND to_date('{toDate.DateTimeToString()}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from d in queryResult.AsEnumerable()
                              select new ItemPti(d)).ToList();

                if (result.Any())
                {
                    listPti = result.OrderByDescending(x => x.CrtTs).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return listPti;
        }


        /// <summary>
        /// Get import vessel of the given container
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="isImp"></param>
        /// <returns></returns>
        public VesselDetails GetImpExpVessel(int itemKey, bool isImp)
        {
            VesselDetails imptVessel = null;
            try
            {
                var sql = string.Empty;

                if (isImp)
                {
                    sql = $@"

                                    SELECT vd.* FROM ITEM i
		                                     INNER JOIN VESSEL_DETAILS vd ON i.ARR_CAR = vd.TFC_CODE_I AND i.ARR_BY = 'V'
                                    WHERE i.ITEM_KEY = {itemKey}	
                                        
                                        ";
                }
                else
                {
                    sql = $@"                                 
                                    SELECT vd.* FROM ITEM i
		                                     INNER JOIN VESSEL_DETAILS vd ON i.ARR_CAR = vd.TFC_CODE_E AND i.DEP_BY = 'V'
                                    WHERE i.ITEM_KEY = {itemKey}                                        
                                        ";
                }


                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from d in queryResult.AsEnumerable()
                              select new VesselDetails(d));

                if (result.Any())
                {
                    imptVessel = result.OrderByDescending(x => x.ActBerthTs).FirstOrDefault();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return imptVessel;
        }


        public void ClearItemStopLinkSphd(int itemKey)
        {
            if (itemKey <= 0)
            {
                return;
            }

            var sql = $@"
                                DELETE ITEM_STOPS its
                                WHERE its.ROWID IN
                                (
                                SELECT s.ROWID FROM ITEM_STOPS s
                                    INNER JOIN SPECIAL_HDL_CODE c ON c.STOP_CODE = s.STOP_CD AND c.SPECIAL_HDL_CODE = s.SPECIAL_HDL_CODE
                                WHERE s.ITEM_KEY = {itemKey} AND NOT EXISTS (SELECT 1 FROM ITEM_SPECIAL_HANDLING WHERE CODE = s.SPECIAL_HDL_CODE)
                                )";

            OraDatabase.ExecuteNonQuery(sql);
        }


        /// <summary>
        /// Check Iso from history system
        /// </summary>
        /// <returns></returns>
        public List<ViewSearchIsoSystem> GetMatchIsoFromSystem(string findStr, bool historyOnly = true)
        {
            if (findStr.IsEmpty())
            {
                return new List<ViewSearchIsoSystem>();
            }

            string sql = "";
            // find in item history
            if (historyOnly)
            {
                sql = $@"
                                SELECT ISO , count(*) AS COUNT FROM ITEM WHERE ITEM_NO like '%{findStr}%' AND HIST_FLG = 'Y'
                                GROUP BY ISO
                                ";
            }
            else
            {
                sql = $@"
                                SELECT ISO , count(*) AS COUNT FROM ITEM WHERE ITEM_NO like '%{findStr}%'
                                GROUP BY ISO
                                ";
            }
            var data = OraDatabase.ExecuteSql(sql);

            var result = from d in data.AsEnumerable()
                         select new ViewSearchIsoSystem(d);

            return result.ToList();

        }


        /// <summary>
        /// Get All container is currently in CFS area
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllContainerInCFS()
        {
            var sql = string.Format(@"
    
                                SELECT (il.STACK ||' '|| il.X ||' '|| il.Y ||' '|| il.Z) as location, i.ISO, i.ITEM_NO, i.FEL, i.GROSS 
                                FROM ITEM i
	                                INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
	                                INNER JOIN YARD_AREA r ON r.STACK = il.STACK AND r.CFS_AREA = 'Y'
                                WHERE i.HIST_FLG <> 'Y' 
                                ");

            var result = OraDatabase.ExecuteSql(sql);

            return result;
        }

        /// <summary>
        /// Get All container is currently in CFS area
        /// </summary>
        /// <returns></returns>
        public DataTable GetAllContainerInDRH()
        {
            var sql = string.Format(@"
    
                                SELECT (il.STACK ||' '|| il.X ||' '|| il.Y ||' '|| il.Z) as location, i.ISO, i.ITEM_NO, i.FEL, i.GROSS 
                                FROM ITEM i
	                                INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
	                                INNER JOIN YARD_AREA r ON r.STACK = il.STACK AND r.STUFF_STRIP_AREA = 'Y'
                                WHERE i.HIST_FLG <> 'Y' 
                                ");

            var result = OraDatabase.ExecuteSql(sql);

            return result;
        }



        /// <summary>
        /// Check if container is currently in CFS area
        /// </summary>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public bool IsContainerInCFS(string itemNo)
        {
            var sql = $@"
    
                                SELECT count(1) FROM ITEM i
	                                INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
	                                INNER JOIN YARD_AREA r ON r.STACK = il.STACK AND r.CFS_AREA = 'Y'
                                WHERE i.HIST_FLG <> 'Y' AND i.ITEM_NO = '{itemNo}'
                                ";

            var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return count > 0;
        }

        public bool IsContainerIssuedEirStrip(string itemNo)
        {
            if (!string.IsNullOrEmpty(itemNo))
            {
                var sql = $@"
                            SELECT count(1) FROM PREGATE_TRANSACT p, ITEM i 
                            WHERE i.ITEM_KEY = p.ITEM_KEY AND (i.HIST_FLG = ' ' OR i.HIST_FLG = 'N') 
		                            AND p.ITEM_NO = '{itemNo}' AND p.OPERATION_TYPE = 'STRIP' AND (p.HIST_FLG = ' ' OR p.HIST_FLG = 'N') 
                                ";

                var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

                return count > 0;
            }

            return false;
        }

        public bool IsContainerIssuedEirStrip(int itemKey)
        {
            if (itemKey > 0)
            {
                var sql = $@"
                            SELECT count(1) FROM PREGATE_TRANSACT WHERE ITEM_KEY = {itemKey} AND OPERATION_TYPE = 'STRIP' AND HIST_FLG <> 'Y'
                                ";

                var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

                return count > 0;
            }

            return false;
        }

        public bool IsContainerIssuedEirStuff(int itemKey)
        {
            if (itemKey > 0)
            {
                var sql = $@"
                            SELECT count(1) FROM PREGATE_TRANSACT WHERE ITEM_KEY = {itemKey} AND OPERATION_TYPE IN ('PSTUSTR','PSTUFF','STUFF','STRIP','PSTRIP')
                        AND HIST_FLG <> 'Y'
                                ";

                var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

                return count > 0;
            }

            return false;
        }
        public bool ChangeContainerTHCSlot(List<VesselItemCharges> lstCharges, string toSlot, VesselDetails vesselDetails, bool forceOverwrite = false)
        {
            try
            {
                if (lstCharges == null || !lstCharges.Any())
                {
                    return false;
                }

                var lstMultiCharges = lstCharges.Where(a => forceOverwrite || a.InvoiceNo.IsEmpty()).ChunkBy(100);

                OraDatabase.BeginTransaction();

                //Change Slot in THC

                foreach (var subList in lstMultiCharges)
                {
                    var lstChargeId = subList.Select(a => a.Id.TrimEx()).ToCombineString(",");

                    var sqlVic = $@"
                            UPDATE VESSEL_ITEM_CHARGES 
                                SET SLOT_CODE = '{toSlot}', RATE = 999, BILLED_AMOUNT = 999, CALCULATED_AMOUNT = 999, DISCOUNT_AMOUNT = 0, RATE_ID = -1,
                                IS_COMPLETE = ' ', INVOICE_NO = ' ', INTERNAL_BOOK_NUMBER = ' ', INVDETAIL_ID = ' ', INVOICE_DT = {DateTimeUtilities.GetNullDate().ToOracleDateString()}, 
                                REMARKS = INVOICE_NO
                            WHERE ID IN ({lstChargeId}) ".TrimEx();

                    OraDatabase.ExecuteNonQuery(sqlVic);

                    var lstLoadItemKey = subList
                        .Where(a => (a.ChargeCd == VesselActivityCode.LD || a.ChargeCd == VesselActivityCode.TL || a.ChargeCd == VesselActivityCode.TS) &&
                                    a.ItemKey > 0).Select(a => a.ItemKey.TrimEx());
                    if (lstLoadItemKey.Any())
                    {
                        var sqlUpdEdiItem = $@"
                            UPDATE EDI_ITEM_PRE_ADVICE
                                SET SLOT = '{toSlot}'
                            WHERE ITEM_KEY IN ({lstLoadItemKey.ToCombineString(",")}) ";

                        OraDatabase.ExecuteNonQuery(sqlUpdEdiItem);
                    }

                    var lstDischargeItemKey = subList
                        .Where(a => (a.ChargeCd != VesselActivityCode.LD && a.ChargeCd != VesselActivityCode.TL || a.ChargeCd != VesselActivityCode.TS) &&
                                    a.ItemKey > 0).Select(a => a.ItemKey.TrimEx());
                    if (lstDischargeItemKey.Any())
                    {
                        var sqlUpdItem = $@"
                            UPDATE ITEM
                                SET SLOT_CODE = '{toSlot}'
                            WHERE ITEM_KEY IN ({lstDischargeItemKey.ToCombineString(",")}) ";

                        var sqlUpdDischargeList = $@"
                            UPDATE DISCHARGE_LIST
                                SET SLOT_CODE = '{toSlot}'
                            WHERE TFC_CODE= '{vesselDetails.TfcCodeI}' AND ITEM_KEY IN ({lstDischargeItemKey.ToCombineString(",")}) ";

                        OraDatabase.ExecuteNonQuery(sqlUpdItem);
                        OraDatabase.ExecuteNonQuery(sqlUpdDischargeList);
                    }
                }

                OraDatabase.EndTransaction();

                return true;
            }
            catch (Exception ex)
            {
                OraDatabase.Error();
                DaExceptionHandler.HandleException(ref ex);
            }

            return false;
        }

        #endregion

        #region EIPA Methods

        /// <summary>
        /// Get list Epl report in view_epl_report by tfc code export
        /// </summary>
        /// <param name="tfcCodeExp"></param>
        /// <returns></returns>
        public List<ViewEplReport> GetEplReport(string tfcCodeExp)
        {
            var sql = string.Format(@"
                SELECT  
	                i.FILE_KEY, i.FILE_SEQ, i.TFC_CODE, i.CUST_TFC_CODE, i.Category, i1.Category Yard_Category,  i.AGENT, i.LINER_CODE LINE_OPER, i.SLOT, i.Item_Key, i.Item_No, substr(i.Item_No,0,4) PREFIX, 
	                CASE WHEN i1.ITEM_KEY IS NOT NULL AND i1.ISO <> i.ISO THEN 'Y' ELSE 'N' END VIOLATE_WISO, i.Iso, i1.Iso Inyard_ISO,  i.FEL, i1.FEL YARD_FEL, 
	                CASE WHEN (dangerous.ITEM_KEY IS NULL AND i.HAZ_CLASS <> ' ') OR (dangerous.DGS_CLASS <> i.HAZ_CLASS) THEN 'Y' ELSE 'N' END VIOLATE_WHAZ, 
	
	                i.HAZ_CLASS, dangerous.DGS_CLASS AS YARD_HAZ_CLASS, 
	                i.Length, i.Height, i.SEAL_NO, 
	                i.GROSS, i.CARGO_WGT, i.POD, i.CUST_POD, i.POL, i.FIN_DISCH_PORT, i.Book_No, i.TRANSPORT_TYPE, i.GRADE, i.POD AS LL_POD, i.CTR_TYPE AS ITEM_TYPE,
	                nvl(cc.ECN_NO, ' ') ECN_NO,  
	                CASE WHEN vi.ITEM_KEY IS NULL THEN ' ' ELSE vi.LL_DISCH_PORT END LL_DISCH_PORT,  
	                i1.ARR_TS, 
	                y.AREA, il.STACK, il.X, il.Y, il.Z,
	
	                CASE WHEN i.FEL = 'E' THEN ' ' 
		                 WHEN cc.ITEM_KEY IS NULL OR its1.Item_Key IS NOT NULL
		                 THEN 'N' ELSE 'Y' END IS_TLHQ , 

	                CASE WHEN its1.Item_Key IS NOT NULL  THEN 'N' ELSE 'Y' END IS_TLHQ_CHECKSUM , 
	                CASE WHEN vi.ITEM_KEY IS NULL THEN 'N' 
		                 WHEN extract(YEAR FROM vi.ARR_TS) > 1900 THEN 'Y' ELSE 'N' END IS_INYARD, 
 
	                CASE WHEN EXISTS ( SELECT its1.ITEM_KEY FROM ITEM_STOPS its2 
				                 WHERE its2.ITEM_KEY = i.ITEM_KEY  AND its2.STOP_CD = 'NAVA' AND extract(YEAR FROM its2.CLR_TS) = 1900)
		                THEN 'N' ELSE 'Y' END IS_INYARD_CHECKSUM , 

	                CASE WHEN refer.ITEM_KEY IS NULL THEN 'N' ELSE 'Y' END IS_REEFER,
	                CASE WHEN oog.ITEM_KEY IS NOT NULL THEN 'Y' ELSE 'N' END IS_OOG,
	                CASE WHEN s.CODE_REF IS NULL THEN 'N' ELSE 'Y' END IS_OV_WEIGHT,
	                CASE WHEN dangerous.ITEM_KEY IS NULL THEN 'N' ELSE 'Y' END IS_DANGEROUS,
	                CASE WHEN (i.CTR_TYPE = 'FL' OR i.CTR_TYPE = 'PC' OR i.CTR_TYPE = 'PF' OR i.CTR_TYPE = 'PL') THEN 'Y' ELSE 'N' END IS_FL,

	                i.BILL_TYPE AS ITEM_CLASS,
	                dangerous.DGS_CLASS, dangerous.UN_NO , refer.Setting_Temp, 

	                CASE WHEN i.CATEGORY = 'D' THEN 'Y' ELSE ' ' END DOMESTIC_FLG,
	                CASE WHEN its1.ITEM_KEY IS NOT NULL THEN 'Y'
		                WHEN i.FEL = 'F' AND i.CATEGORY <> 'D' AND cc.ITEM_KEY IS NULL THEN 'Y' ELSE 'N' END VIOLATE_TLHQ , 

	                CASE WHEN EXISTS ( SELECT its1.ITEM_KEY FROM ITEM_STOPS its3 
				                WHERE its3.ITEM_KEY = i.ITEM_KEY  AND its3.STOP_CD = 'VCTS' AND extract(YEAR FROM its3.CLR_TS) = 1900)
	 	                THEN 'Y' ELSE 'N' END VIOLATE_CLOSINGTIME , 

	                CASE WHEN vi.ITEM_KEY IS NULL THEN 'Y' 
	                   WHEN extract(YEAR FROM vi.ARR_TS) > 1900  THEN 'N' ELSE 'Y' END VIOLATE_INGATE, 
   
	                CASE WHEN (vi.DEP_CAR IS NOT NULL AND vi.DEP_CAR != i.TFC_CODE) OR  EXISTS ( SELECT its1.ITEM_KEY FROM ITEM_STOPS its4 
				                 WHERE its4.ITEM_KEY = i.ITEM_KEY 
				                AND its4.STOP_CD = 'WDEP' AND extract(YEAR FROM its4.CLR_TS) = 1900)
		                THEN 'Y' 
		                ELSE 'N' END VIOLATE_WDEP , 
	
	                CASE WHEN (vi.LL_DISCH_PORT IS NOT NULL AND vi.LL_DISCH_PORT != i.POD) OR EXISTS ( SELECT its1.ITEM_KEY FROM ITEM_STOPS its5 
				                 WHERE its5.ITEM_KEY = i.ITEM_KEY AND its5.STOP_CD = 'WPOD' AND extract(YEAR FROM its5.CLR_TS) = 1900)
		                THEN 'Y' 
		                ELSE 'N' END VIOLATE_WPOD , 
		
	                CASE WHEN its6.Item_Key IS NULL 
		                THEN 'N' 
		                ELSE 'Y' END STOP,
	
	                its6.STOP_CD,

	                CASE WHEN i1.ITEM_KEY IS NOT NULL AND i1.FEL <> i.FEL THEN 'Y' ELSE 'N' END VIOLATE_WFEL,  
	  
	                CASE WHEN i1.ITEM_KEY IS NOT NULL AND ((i1.CATEGORY = 'D' AND i.CATEGORY <> 'D') OR (i1.CATEGORY <> 'D' AND i.CATEGORY = 'D')) THEN 'Y' ELSE 'N' END VIOLATE_DOMESTIC,
	
	                CASE WHEN (oog.ITEM_KEY IS NULL AND i.IS_OOG = 'Y') OR (oog.ITEM_KEY IS NOT NULL AND i.IS_OOG <> 'Y') THEN 'Y' ELSE 'N' END VIOLATE_WOOG,
	
	                CASE WHEN oog.ITEM_KEY IS NULL THEN ' ' ELSE 'Y' END AS YARD_OOG,

	
	                ' ' VIOLATE_CONDITION,  'Y' AVAILABLE_FLG,  'N' DOUBLE_FLG,  
	                i.COMMENTS,
	                nvl(vi.DEP_CAR, ' ' ) DEP_CAR,
	                CASE WHEN DL.ITEM_KEY IS NULL THEN 'N' ELSE 'Y' END IS_IN_BARGE,
	                CASE WHEN DL.ITEM_KEY IS NOT NULL AND DL.FEL<>i.FEL THEN 'Y' ELSE 'N' END VIOLATE_WFEL_BARGE, 
	                CASE WHEN DL.ITEM_KEY IS NOT NULL AND DL.LL_POD<>i.POD THEN 'Y' ELSE 'N' END VIOLATE_WPOD_BARGE, 
	                CASE WHEN cc.ITEM_KEY IS NOT NULL AND cc.VES_ID<>i.VES_ID AND i.VES_ID <> ' ' THEN 'Y' ELSE 'N' END VIOLATE_WDEP_CUS, 
	                CASE WHEN DL.ITEM_KEY IS NULL THEN ' ' ELSE DL.TFC_CODE END BARGE_VOYAGE,
	
	                CASE WHEN extract(YEAR FROM i.ARR_TS) > 1900 AND i.STK_REF = vd.VES_ID	THEN 'Y' ELSE 'N' END ON_VESSEL 

                FROM VIEW_EIPA_ITEM i 
	                INNER JOIN VESSEL_DETAILS vd ON vd.TFC_CODE_E = i.TFC_CODE
	                LEFT OUTER JOIN ITEM_REEFER refer ON refer.ITEM_KEY = i.ITEM_KEY
	                LEFT OUTER JOIN ITEM_OOG oog ON oog.ITEM_KEY = i.ITEM_KEY --AND GETOOG(oog.ITEM_KEY) > 0
	                LEFT OUTER JOIN SYS_CODES  s ON s.CODE_TP = 'MAXGROSS' AND i.GROSS > to_number(s.CODE_REF)
	                LEFT OUTER JOIN ITEM_DANGEROUS dangerous ON dangerous.ITEM_KEY = i.ITEM_KEY AND ((i.HAZ_CLASS <> ' ' AND dangerous.DGS_CLASS = i.HAZ_CLASS) OR dangerous.DGS_SEQ = 0)
	                LEFT OUTER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
	                LEFT OUTER JOIN	YARD_AREA y ON y.STACK = il.STACK AND y.X1 <= il.X AND il.X <= X2 AND SYSDATE BETWEEN y.FR_DATE AND y.TO_DATE
 
	                LEFT OUTER JOIN CUSTOM_CLEARANCE cc ON cc.ITEM_KEY = i.ITEM_KEY   
	                LEFT OUTER JOIN ITEM vi ON vi.ITEM_KEY = i.ITEM_KEY --AND vi.HIST_FLG != 'Y'
	                LEFT OUTER JOIN ITEM i1 ON (i1.HIST_FLG = 'N' OR i1.HIST_FLG = ' ') AND i1.ITEM_NO = i.ITEM_NO 	
	
	                LEFT OUTER JOIN ITEM_STOPS its1 ON its1.ITEM_KEY = i.ITEM_KEY AND its1.STOP_CD = 'CUST' AND extract(YEAR FROM its1.CLR_TS) = 1900
	                LEFT OUTER JOIN ITEM_STOPS its6 ON its6.ITEM_KEY = i.ITEM_KEY AND (its6.STOP_CD = 'CHB' OR its6.STOP_CD = 'STP') AND extract(YEAR FROM its6.CLR_TS) = 1900
		
	                LEFT OUTER JOIN (SELECT j.* FROM DISCHARGE_LIST j, VESSEL_DETAILS v 
               			                WHERE j.HIST_FLG <> 'Y' AND j.EXIT_TFC_CODE = '{0}'
	                                    AND j.TFC_CODE = v.TFC_CODE_I 
	                                    AND	v.INTERNAL_BARGE_FLG = 'I' 
	                                    AND	NOT EXISTS (SELECT ITEM_KEY FROM ITEM it WHERE it.ITEM_KEY=j.ITEM_KEY AND extract(YEAR FROM it.ARR_TS) != 1900
	                                                    AND	it.HIST_FLG<>'Y' ) 
	                        ) DL ON i.ITEM_NO = DL.ITEM_NO 
                WHERE  i.IS_COPRAR_LOAD = 'Y' AND i.TFC_CODE = '{0}'
                        ", tfcCodeExp);

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from d in queryResult.AsEnumerable()
                         select new ViewEplReport(d);

            return result.ToList();

        }

        /// <summary>
        /// Get container data from EDI_ITEM_PRE_ADVICE table. 
        /// If the given container number is not found in system or it is no longer active in system, 
        /// a null value will be returned
        /// </summary>
        /// <param name="containerNo">Container number</param>
        /// <param name="fel">Container status condition. By default, this argument is empty and the function will perform regardless container status</param>
        /// <param name="historyFlg">If this flag is "Y", the function will find container in history. By default, the function just find active containers</param>
        /// <returns>
        ///         - Return an EdiItemPreAdvice object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public EdiItemPreAdvice GetEdiItemPreAdvice(string containerNo, string fel, string historyFlg)
        {
            string felCondition = string.Empty;

            if (fel != "")
            {
                felCondition = $" AND FEL = '{fel}'";
            }

            string historyConditon = historyFlg != "Y" ? @" AND (HIST_FLG = ' ' OR HIST_FLG = 'N')" : @" AND (HIST_FLG = 'Y')";

            //IS_OOG, IS_DAMAGE, IS_COMMENT, IS_COPRAR_LOAD, VES_CD, CREATED_BY, DISCH_VES, FIN_DISCH_PORT, TRANSPORT_TYPE, 
            //                                    SPECIAL_HDL_CD, ENTRY_VES_NAME, ENTRY_VES_TYPE, ENTRY_VES_CALL_SIGN, SITE_ID, IG_FLG, HIST_FLG, CRT_TS, 
            //                                    UPD_TS, IS_ENTRY_FORBIDDEN, IS_RESTRICTED, FPD_NAME, EXIT_CALL_SIGN, LINER_NAME, UPD_CNT, ORIG_ISO, ITEM_KEY, 
            //                                    ITEM_NO, TFC_CODE, LINER_CODE, BOOK_NO, CTR_OWNER, CTR_TYPE, CATEGORY, FEL, ISO, LENGTH, HEIGHT, TARE, 
            //                                    GROSS, CARGO_WGT, POD, POL, PLD, VES_ID, VES_NAME, VOYAGE, IS_CTR, IS_DANGEROUS, IS_REEFER, TFC_CODE_I, 
            //                                    LL_POD, POD_NAME, SLOT, DIRECT_LOAD_FLG, AGENT, CUST_TFC_CODE, CUST_POD, LOAD_LIST_FLG

            string sql = $@"

                                        SELECT *
                                        FROM EDI_ITEM_PRE_ADVICE
                                        WHERE ITEM_NO = '{containerNo}'
                                              {felCondition}
                                              {historyConditon}

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new EdiItemPreAdvice(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        public ViewEipaItem GetViewEdiItemPreAdvice(string containerNo, string fel = "", string historyFlg = "")
        {
            string felCondition = string.Empty;

            if (fel != "")
            {
                felCondition = $" AND FEL = '{fel}'";
            }

            var historyConditon = historyFlg != "Y" ? @" AND (HIST_FLG = ' ' OR HIST_FLG = 'N')" : @" AND (HIST_FLG = 'Y')";

            string sql = $@" Select * FROM VIEW_EIPA_ITEM 
                                   WHERE ITEM_NO = '{containerNo}' {felCondition} {historyConditon}
                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ViewEipaItem(record);

            return result.FirstOrDefault();
        }

        public ViewDischargeList GetViewDischargeListItem(string containerNo, string fel = "", string historyFlg = "")
        {
            string felCondition = string.Empty;

            if (fel != "")
            {
                felCondition = $" AND FEL = '{fel}'";
            }

            var historyConditon = historyFlg != "Y" ? @" AND (HIST_FLG = ' ' OR HIST_FLG = 'N')" : @" AND (HIST_FLG = 'Y')";

            string sql = $@" Select * FROM VIEW_DISCHARGE_LIST 
                                   WHERE ITEM_NO = '{containerNo}' {felCondition} {historyConditon}
                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ViewDischargeList(record);

            return result.FirstOrDefault();
        }

        public void SyncSubItemToSubDisch(DischargeList dischItem)
        {
            var parameters = new List<OracleParameter>();

            parameters.Insert(0, new OracleParameter("iITEM_KEY", OracleDbType.Int32) { Value = dischItem.ItemKey });
            parameters.Insert(1, new OracleParameter("iMANIFEST_KEY", OracleDbType.Int32) { Value = dischItem.ManifestKey });

            OraDatabase.ExecuteSpNonQuery("SYNC_SUB_ITEM_TO_SUB_DISCH", parameters);
        }


        /// <summary>
        /// Get container data from EDI_ITEM_PRE_ADVICE table. 
        /// given by itemKey
        /// </summary>
        /// <returns>
        ///         - Return an EdiItemPreAdvice object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public EdiItemPreAdvice GetEdiItemPreAdvice(int itemKey)
        {
            var sql = $@"

                                        SELECT *
                                        FROM EDI_ITEM_PRE_ADVICE
                                        WHERE ITEM_KEY = {itemKey}

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new EdiItemPreAdvice(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// Check to see if pre-adviced container has stop code. 
        /// </summary>
        /// <param name="containerNo"></param>
        /// <param name="notInGateFilter">
        /// If notInGateFilter is true, the method will only check on containers that is marked as not-in-gate.
        /// By default, notInGateFilter is false. This case, the method checks on container regardless in-gate status
        /// </param>
        /// <returns></returns>
        public bool CheckPreAdvicedContHasStop(string containerNo, bool notInGateFilter)
        {
            string notInGateCondition = notInGateFilter ? @"AND eipa.IG_FLG <> 'Y'" : "";

            string sql = $@"
                            
                                        SELECT count(ist.ITEM_KEY )
                                        FROM EDI_ITEM_PRE_ADVICE eipa, ITEM_STOPS ist
                                        WHERE eipa.ITEM_KEY = ist.ITEM_KEY
                                            AND eipa.ITEM_NO = '{containerNo}'                                            
                                            AND eipa.HIST_FLG <> 'Y'
                                            {notInGateCondition}
                                            AND ist.CLR_BY = ' '

                            ";


            int count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return count > 0;
        }

        /// <summary>
        /// Get list container in load list not current in yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<EdiItemPreAdvice> GetListEipaNotInYardByVessel(string tfcCode)
        {
            //AND (eipa.hist_flg = 'N' OR eipa.hist_flg = ' ') 

            var sql = $@"
                            Select eipa.*
                            FROM EDI_ITEM_PRE_ADVICE eipa
	                            LEFT JOIN ITEM i ON i.ITEM_KEY = eipa.ITEM_KEY
                            Where eipa.TFC_CODE = '{tfcCode}' 
	                            AND i.ITEM_KEY IS NULL
                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();
        }


        /// <summary>
        /// Get list container in Load list has discrepancy status in Item
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<EdiItemPreAdvice> GetEipaStatusDiscrepancyWithItem(string tfcCode)
        {
            var sql = $@"
                            Select i.ITEM_NO, i.ITEM_KEY 
                            FROM EDI_ITEM_PRE_ADVICE eipa
	                            INNER JOIN ITEM i ON i.ITEM_NO = eipa.ITEM_NO AND (i.hist_flg = 'N' OR i.hist_flg = ' ')
                            Where eipa.TFC_CODE = '{tfcCode}'
	                            AND (eipa.hist_flg = 'N' OR eipa.hist_flg = ' ') 
	                            AND eipa.FEL != i.FEL 
                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();

        }

        /// <summary>
        /// Get list container in Load list has discrepancy status in Discharge list
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<EdiItemPreAdvice> GetEipaStatusDiscrepancyWithDischargeList(string tfcCode)
        {
            var sql = $@"
                            Select i.ITEM_NO, i.ITEM_KEY 
                            FROM EDI_ITEM_PRE_ADVICE eipa
	                            INNER JOIN DISCHARGE_LIST i ON i.ITEM_NO = eipa.ITEM_NO AND (i.hist_flg = 'N' OR i.hist_flg = ' ')
                            Where eipa.TFC_CODE = '{tfcCode}'
	                            AND (eipa.hist_flg = 'N' OR eipa.hist_flg = ' ') 
	                            AND eipa.FEL != i.FEL 
                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();
        }

        /// <summary>
        /// Get list container in Load list has difference departure car detail in Item 
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<EdiItemPreAdvice> GetEipaMissmatchTfcWithItem(string tfcCode)
        {
            var sql = $@"
                            Select i.ITEM_NO, i.ITEM_KEY 
                            FROM EDI_ITEM_PRE_ADVICE eipa
	                            INNER JOIN ITEM i ON i.ITEM_KEY = eipa.ITEM_KEY AND i.DEP_BY = 'V' and i.DEP_CAR != ' '
                            Where eipa.TFC_CODE = '{tfcCode}'
	                            AND eipa.TFC_CODE != i.DEP_CAR
                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();
        }

        /// <summary>
        /// Get list container in Load list has data in item_ref_detail
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ItemRefDetails> GetEipaHasItemRefDetails(string tfcCode)
        {
            var sql = $@"
                            Select i.ITEM_KEY, i.EUG_REF_NO, i.BHT_REF_NO 
                            FROM EDI_ITEM_PRE_ADVICE eipa
	                            INNER JOIN ITEM_REF_DETAILS i ON i.ITEM_KEY = eipa.ITEM_KEY
                            Where eipa.TFC_CODE = '{tfcCode}'
                        ";
            string s = $"asdasdad {GlobalSettings.NewLine}";

            var queryResult = OraDatabase.ExecuteSql(sql);
            return (from d in queryResult.AsEnumerable()
                    select new ItemRefDetails(d)).ToList();
        }

        /// <summary>
        /// get list container curretn in yard byt not in load list (include not confirmed)
        /// </summary>
        /// <param name="tfcCodeExp"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerInYardNotInLoadList(string tfcCodeExp)
        {
            var sql = string.Format(@"
                            SELECT i.*
                            FROM VIEW_ITEM i
	                            LEFT JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_NO = i.ITEM_NO AND e.TFC_CODE = '{0}' --AND e.LOAD_LIST_FLG = 'Y'
                            WHERE i.HIST_FLG <> 'Y' AND extract(YEAR FROM i.ARR_TS) > 1900  
                                AND i.DEP_CAR = '{0}' AND i.CATEGORY not in ('R', 'S')
                                AND e.ITEM_KEY IS NULL ", tfcCodeExp).Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewItem(d)).ToList();
        }

        /// <summary>
        /// get list container curretn in discharge byt not in load list (include not confirmed)
        /// </summary>
        /// <param name="tfcCodeExp"></param>
        /// <returns></returns>
        public List<ViewDischargeList> GetContainerInDischargedNotInLoadList(string tfcCodeExp)
        {
            var sql = string.Format(@"
                            SELECT d.*
                            FROM DISCHARGE_LIST d 
                            LEFT JOIN ITEM  i ON d.ITEM_NO = i.ITEM_NO  AND i.DEP_CAR = '{0}'                     
                            LEFT JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_NO = d.ITEM_NO AND e.TFC_CODE = '{0}'                          
                            WHERE i.ITEM_KEY IS NULL                             
                             AND d.EXIT_TFC_CODE='{0}'                              
                             AND d.CATEGORY not in ('R', 'S')                                
                             AND e.ITEM_KEY IS NULL
                             AND d.HIST_FLG <> 'Y'
                             ", tfcCodeExp).Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewDischargeList(d)).ToList();
        }

        public List<ViewItem> GetContainerInYardInLoadList(string tfcCodeExp)
        {
            var sql = string.Format(@"
                            SELECT i.*
                            FROM VIEW_ITEM i
	                            INNER JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_NO = i.ITEM_NO AND e.TFC_CODE = '{0}' --AND e.LOAD_LIST_FLG = 'Y'
                            WHERE i.HIST_FLG <> 'Y' AND extract(YEAR FROM i.ARR_TS) > 1900  
                                AND i.DEP_CAR = '{0}' AND i.CATEGORY not in ('R', 'S')
                                ", tfcCodeExp).Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewItem(d)).ToList();
        }

        /// <summary>
        ///     Get containers departure by vessel and not in yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerCommunityNotInLoadList(string tfcCode)
        {
            var sql = string.Format($@"
                            SELECT i.*
                            FROM VIEW_ITEM i
	                            LEFT JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_NO = i.ITEM_NO AND e.TFC_CODE = '{tfcCode}' --AND e.LOAD_LIST_FLG = 'Y'
                            WHERE i.HIST_FLG <> 'Y' AND extract(YEAR FROM i.ARR_TS) = 1900  
                                AND i.DEP_CAR = '{tfcCode}' AND i.CATEGORY not in ('R', 'S')
                                AND e.ITEM_KEY IS NULL ");

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewItem(d)).ToList();
        }

        /// <summary>
        /// Get containers departure by vessel and current in discharge list
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewDischargeList> GetContainerInDischargeNotInLoadList(string tfcCode)
        {
            var sql = string.Format(@"
                            SELECT i.*
                            FROM VIEW_DISCHARGE_LIST i
                                LEFT JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_NO = i.ITEM_NO AND e.TFC_CODE = '{0}' AND e.HIST_FLG <> 'Y' --AND e.LOAD_LIST_FLG = 'Y'
                            WHERE i.HIST_FLG <> 'Y' 
                                AND i.EXIT_TFC_CODE = '{0}'
                                AND e.ITEM_KEY IS NULL ", tfcCode).Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewDischargeList(d)).ToList();
        }

        public List<ViewDischargeList> GetContainerInDischargeByBillNo(string billNo, DateTime fromDate, DateTime toDate)
        {

            var sql = $@"
                            SELECT i.*
                            FROM VIEW_DISCHARGE_LIST i
                                WHERE i.BILL_OF_LADING = '{billNo}'
                                AND i.CRT_TS BETWEEN {fromDate.ToOracleDateString()} AND {toDate.ToOracleDateString()} "
                .Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewDischargeList(d)).ToList();
        }

        /// <summary>
        /// Get list container departure by depcar = tfcCodeE from Item, indicate by current location
        /// </summary>
        /// <returns></returns>
        public List<Item> GetAllDepartContainerByVessel(string depCar)
        {
            var sWhere = "";
            if (!string.IsNullOrEmpty(depCar))
            {
                sWhere += $" AND DEP_CAR = '{depCar}'";
            }


            var sql = $@"
                                SELECT i.ITEM_NO, i.LINE_OPER, i.BOOK_NO, i.ARR_TS, i.ARR_BY, i.ARR_CAR, i.DEP_CAR, i.DEP_BY,i.EIR_ID,
	                                i.ITEM_STATUS, i.ITEM_TYPE, i.LENGTH, i.FEL, i.ISO, i.LL_DISCH_PORT, i.DISCH_PORT, i.FDISCH_PORT, 
                                    i.ITEM_KEY, i.RELEASE_NO, i.RA_KEY, i.CATEGORY, i.COPRAR_LOAD
                                FROM ITEM i
	                                INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C' AND il.STK_CLASS <> 'V'
                                WHERE (i.HIST_FLG = 'N' OR i.HIST_FLG = ' ') AND extract(YEAR FROM i.ARR_TS) > 1900 {sWhere}
                                ORDER BY i.ITEM_NO ";


            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = (from d in queryResult.AsEnumerable()
                          select new Item(d)).ToList();

            return result;

        }


        /// <summary>
        /// Delete all stop code of outbound container
        /// </summary>
        /// <param name="itemKey"></param>
        public void DeleteOutboundStop(int itemKey)
        {
            var sql = $@"
                        DELETE ITEM_STOPS
                        WHERE ROWID IN 
                        (
	                        SELECT i.ROWID FROM ITEM_STOPS i 
		                        INNER JOIN CONFIG_STOPCODE s ON s.STOP_CODE = i.STOP_CD
	                        WHERE i.ITEM_KEY = {itemKey} AND s.STOP_TYPE = '{StopTypes.Outbound}'
                        ) ";

            OraDatabase.ExecuteNonQuery(sql);
        }

        /// <summary>
        /// Delete all stop code of outbound container cause by special handling code
        /// </summary>
        /// <param name="itemKey"></param>
        public void DeleteOutboundSphdStop(int itemKey)
        {
            //Thêm vào để chỉ xóa những chỉ định tạo ra ở C31 https://pms.snp.com.vn/browse/SDP-6501
            var sqlSphd = $@"
                        DELETE ITEM_STOPS
                        WHERE ROWID IN 
                        (
                            SELECT i.ROWID FROM ITEM_STOPS i 
                                INNER JOIN EDI_ITEM_PRE_ADVICE e ON e.ITEM_KEY = i.ITEM_KEY
                                INNER JOIN CONFIG_STOPCODE s ON s.STOP_CODE = i.STOP_CD
                                INNER JOIN ITEM_SPECIAL_HANDLING sphd ON sphd.ITEM_KEY = i.ITEM_KEY	AND sphd.CODE = i.SPECIAL_HDL_CODE
                                AND sphd.IS_LL_HDL = '{BooleanType.Yes}'
                            WHERE i.ITEM_KEY = {itemKey} 
                        ) ";

            OraDatabase.ExecuteNonQuery(sqlSphd);
        }

        public List<EdiItemPreAdvice> GetListContainerPreAdvicesNotLoaded(string tfcCode)
        {
            var sql = $@"
                            SELECT e.* 
                            FROM EDI_ITEM_PRE_ADVICE e
	                            LEFT JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND i.DEP_CAR = e.TFC_CODE AND extract(YEAR FROM i.DEP_TS) > 1900
                            WHERE e.TFC_CODE = '{tfcCode}' AND e.HIST_FLG <> 'Y' AND i.ITEM_KEY IS NULL ".Trim(); //AND i.CATEGORY in ('I', 'E', 'T', 'O') 

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();
        }

        /// <summary>
        /// Get list container in load list difference ISO code with inyard item
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewIsoDiscrepancy> GetListIsoDiscrepancies(string tfcCode)
        {
            var sql = $@"
                            SELECT i.ITEM_KEY, i.ITEM_NO, i.ISO YARD_ISO, e.ISO LOADLIST_ISO  
                            FROM EDI_ITEM_PRE_ADVICE e
	                            INNER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND e.ISO <> i.ISO
                            WHERE e.TFC_CODE =  '{tfcCode}' ".Trim();

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewIsoDiscrepancy(d)).ToList();
        }

        public List<ViewIsoDiscrepancy> GetListIsoLengthDiscrepancies(string tfcCode)
        {
            var sql = $@"
                            SELECT i.ITEM_KEY, i.ITEM_NO, i.ISO YARD_ISO, e.ISO LOADLIST_ISO  
                            FROM EDI_ITEM_PRE_ADVICE e
	                            INNER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND substr(e.ISO,1,1) <> substr(i.ISO,1,1)
                            WHERE e.TFC_CODE =  '{tfcCode}' ".Trim();

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new ViewIsoDiscrepancy(d)).ToList();
        }

        public List<string> GetListFelDiscrepancies(string tfcCode)
        {
            var sql = $@"
                            SELECT i.ITEM_NO
                            FROM EDI_ITEM_PRE_ADVICE e
	                            INNER JOIN ITEM i ON i.ITEM_NO = e.ITEM_NO AND i.HIST_FLG <> 'Y' AND i.FEL <> e.FEL
                            WHERE e.TFC_CODE =  '{tfcCode}' ".Trim();

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select d[0].TrimEx()).ToList();
        }

        public void UpdateItemKeyCtrNoFromYardToEdiPreAdvice(int oldItemKey, int newItemKey, string newItemNo)
        {
            var sql =
                $@"UPDATE EDI_ITEM_PRE_ADVICE e SET ITEM_KEY = {newItemKey}, ITEM_NO = '{newItemNo}' WHERE ITEM_KEY = {oldItemKey} ";
            OraDatabase.ExecuteNonQuery(sql);
        }


        public List<EdiItemPreAdvice> GetRemainLoadListCtrIsNotLoadedOnvessel(string vesId)
        {
            if (vesId.IsEmpty())
            {
                return new List<EdiItemPreAdvice>();
            }

            string sql = $@"
                                SELECT e.* FROM EDI_ITEM_PRE_ADVICE e
                                    INNER JOIN VESSEL_DETAILS v ON v.TFC_CODE_E = e.TFC_CODE
                                    LEFT OUTER JOIN ITEM_LOCATION il ON il.ITEM_KEY = e.ITEM_KEY AND il.STK_PCH = 'C' 
                                WHERE e.VES_ID = '{vesId}' AND e.IS_COPRAR_LOAD = 'Y'
	                                AND (il.ITEM_KEY IS NULL OR il.STK_REF != v.VES_ID) "; //

            var queryresult = OraDatabase.ExecuteSql(sql);
            return (from d in queryresult.AsEnumerable()
                    select new EdiItemPreAdvice(d)).ToList();
        }

        /// <summary>
        /// Update Edi_item_pre_advice.Ig_Flg = 'Y' when container is arrived 
        /// </summary>
        public bool UpdateEipaIngateStatus(string vesId)
        {
            try
            {
                if (vesId.IsEmpty())
                {
                    return true;
                }

                var sql = $@"
                                        
                            UPDATE EDI_ITEM_PRE_ADVICE SET
                            IG_FLG = 'Y'
                            WHERE ROWID IN
                            ( 
	                            SELECT e.ROWID FROM EDI_ITEM_PRE_ADVICE e
		                            INNER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND extract(YEAR FROM i.ARR_TS) > 1900	
	                            WHERE e.IG_FLG <> 'Y' AND e.VES_ID = '{vesId}'
                            ) ";

                OraDatabase.ExecuteSingleNonQuery(sql);

                return true;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return false;
        }


        #endregion

        #region DISCHARGE_LIST Methods

        /// <summary>
        /// Count total container has been discharged to yard from discharge list
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public int CountDischargedContainers(string tfcCodeI)
        {
            var sql = $@"
                                        SELECT COUNT(1) 
                                        FROM DISCHARGE_LIST d
	                                        INNER JOIN ITEM i ON i.ITEM_KEY = d.ITEM_KEY
                                        WHERE d.TFC_CODE = '{tfcCodeI}' AND extract(year FROM i.ARR_TS ) > 1900
                                            ";

            var countDischarged = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return countDischarged;
        }


        /// <summary>
        /// Count total container in discharge list has exit vessel
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public int CountDischargeContainersHasExitVessel(string tfcCodeI)
        {

            var sql = $@"
                                        SELECT COUNT(1) 
                                        FROM DISCHARGE_LIST d
                                        WHERE d.TFC_CODE = '{tfcCodeI}' AND d.EXIT_TFC_CODE != ' '
                                            ";

            var countPlanned = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return countPlanned;
        }


        /// <summary>
        /// Get list of discharge list in system by Vessel
        /// Only get Item_no, Manifest_key and hist_flg to check
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<DischargeList> GetExistDischargeListByVesselToCheck(string tfcCodeI)
        {
            if (tfcCodeI.IsEmpty())
                return null;

            string sql = $"SELECT ITEM_NO, MANIFEST_KEY, HIST_FLG FROM DISCHARGE_LIST WHERE TFC_CODE = '{tfcCodeI}'";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);

            var result = from row in queryResult.AsEnumerable()
                         select new DischargeList(row);

            return result.ToList();
        }

        public List<string> GetPlannedDischargeContainers(string tfcCodeI)
        {
            var sql = $@"
                                        SELECT d.ITEM_NO
                                        FROM DISCHARGE_LIST d
	                                        INNER JOIN TG2VPT p ON  p.TGCTNR = d.ITEM_NO AND (p.TGCPFG = 'N' OR p.TGCPFG = ' ') 
                                        WHERE  d.TFC_CODE = '{tfcCodeI}' AND TGACTN IN ('12', '13','14') AND (d.HIST_FLG = ' ' OR d.HIST_FLG = 'N')
                                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from row in queryResult.AsEnumerable()
                         select row[0].TrimEx();

            return result.ToList();
        }

        /// <summary>
        /// Count how many container is planned to discharge from vessel
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public int CountPlannedDischargeContainers(string vesId)
        {

            var sql = $@"
                                        SELECT COUNT(1) 
                                        FROM TG2VPT p
	                                        INNER JOIN DISCHARGE_LIST d ON d.VES_ID = '{vesId}' 
			                                        AND p.TGCTNR = d.ITEM_NO AND (d.HIST_FLG = ' ' OR d.HIST_FLG = 'N')
                                        WHERE TGACTN IN ('12', '13','14') AND (TGCPFG = 'N' OR TGCPFG = ' ')
                                            ";

            var countPlanned = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return countPlanned;
        }

        /// <summary>
        /// Count how many container is planned load to vessel
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public int CountPlannedLoadContainers(string vesId)
        {

            var sql = $@"
                                    SELECT COUNT(1) 
                                    FROM TG2VPT p
	                                    INNER JOIN EDI_ITEM_PRE_ADVICE d ON d.VES_ID = '{vesId}' AND d.LOAD_LIST_FLG = 'Y'
			                                    AND p.TGCTNR = d.ITEM_NO AND (d.HIST_FLG = ' ' OR d.HIST_FLG = 'N')
                                        WHERE p.TGVLCD <> ' ' AND p.TGACTN IN ('11')  and (p.TGCPFG = 'N' OR p.TGCPFG = ' ') 
                                           ";

            var countPlanned = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return countPlanned;
        }

        /// <summary>
        /// check if the container has been pland to discharged
        /// </summary>
        /// <returns></returns>
        public bool IsHasVesselPlan(string itemNo, string vesId = "", bool? isImport = null)
        {
            bool retValue = false;
            try
            {
                var whereActiond = isImport == null
                    ? "AND TGACTN IN ('11', '12', '13', '14') "
                    : (isImport == true ? " AND TGACTN IN ('12', '13','14') " : " AND TGACTN IN ('11') ");

                var whereVessel = vesId.Trim().Length > 0
                                  ? $@" AND TGVLCD = '{vesId.SubstringEx(0, 3)}' AND TGVYMX = '{vesId.SubstringEx(4, 3)}' {whereActiond}"
                    : "";

                var sql =
                    $@"SELECT TGCPFG FROM TG2VPT WHERE TGCTNR = '{itemNo}' {whereActiond} AND (TGCPFG = 'N' OR TGCPFG = ' ') {whereVessel}  ";

                var result = OraDatabase.ExecuteSql(sql);

                if (result.Rows.Count > 0)
                    retValue = true;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return retValue;
        }

        /// <summary>
        /// check if the container has been pland to discharged
        /// </summary>
        /// <returns></returns>
        public bool IsHasVesselPlanDischarge(string itemNo, string vesId = "")
        {
            bool retValue = false;
            try
            {
                var whereActiond = " AND TGACTN IN ('12', '13','14') ";

                var whereVessel = vesId.Trim().Length > 0
                                  ? $@" AND TGVLCD = '{vesId.SubstringEx(0, 3)}' AND TGVYMX = '{vesId.SubstringEx(4, 3)}' {whereActiond}"
                    : "";

                var sql =
                    $@"SELECT TGCPFG FROM TG2VPT WHERE TGCTNR = '{itemNo}' {whereActiond} AND (TGCPFG = 'N' OR TGCPFG = ' ') {whereVessel}  ";

                var result = OraDatabase.ExecuteSql(sql);

                if (result.Rows.Count > 0)
                    retValue = true;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return retValue;
        }

        /// <summary>
        /// check if the container has been pland to discharged
        /// </summary>
        /// <returns></returns>
        public bool IsHasVesselPlanLoad(string itemNo, string vesId = "")
        {
            bool retValue = false;
            try
            {
                var whereActiond = " AND TGACTN IN ('11') ";

                var whereVessel = vesId.Trim().Length > 0
                                  ? $@" AND TGVLCD = '{vesId.SubstringEx(0, 3)}' AND TGVYMX = '{vesId.SubstringEx(4, 3)}' {whereActiond}"
                    : "";

                var sql =
                    $@"SELECT TGCPFG FROM TG2VPT WHERE TGCTNR = '{itemNo}' {whereActiond} AND (TGCPFG = 'N' OR TGCPFG = ' ') {whereVessel}  ";

                var result = OraDatabase.ExecuteSql(sql);

                if (result.Rows.Count > 0)
                    retValue = true;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return retValue;
        }

        public bool IsContainerDeparted(int itemKey)
        {
            bool retValue = false;
            try
            {
                var sql = $@"SELECT 1 FROM ITEM WHERE ITEM_KEY = {itemKey} AND extract(year from DEP_TS) > 1900  ";

                var result = OraDatabase.ExecuteSql(sql);

                if (result.Rows.Count > 0)
                    retValue = true;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return retValue;
        }

        /// <summary>
        /// Gets location of the given container from the vessel's discharge list 
        /// </summary>
        /// <param name="itemNo"></param>
        /// <param name="vesselId"></param>
        /// <returns></returns>
        public string GetVesselLocationFromDischargeList(string itemNo, string vesselId)
        {
            string sql = $@"
                            
                                        SELECT STOWLOC 
                                        FROM DISCHARGE_LIST   
                                        WHERE ITEM_NO = '{itemNo}' AND HIST_FLG <> 'Y' AND VES_ID = '{vesselId}'

                            ";


            string location = OraDatabase.ExecuteScalar(sql).ToStringEx();

            return location;
        }

        /// <summary>
        /// Get current location of container which has been discharged from vessel
        /// </summary>
        /// <param name="vesselId"></param>
        /// <returns></returns>
        public List<ItemLocation> GetCurrentLocationDischargedTranship(string vesselId)
        {
            string sql = $@"
                            
                                        SELECT il.ITEM_KEY, il.STK_CLASS, il.STK_REF 
                                        FROM DISCHARGE_LIST d 
	                                        INNER JOIN ITEM	i ON i.ITEM_KEY = d.ITEM_KEY
	                                        INNER JOIN ITEM_LOCATION il ON il.ITEM_KEY = i.ITEM_KEY AND il.STK_PCH = 'C'
                                        WHERE d.VES_ID = '{vesselId}'

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from row in queryResult.AsEnumerable()
                         select new ItemLocation(row);

            return result.ToList();
        }

        /// <summary>
        /// List all container in discharge list and load list
        /// select by tfc code import in discharge_list
        /// </summary>
        /// <param name="tfCodeI"></param>
        /// <returns></returns>
        public List<EdiItemPreAdvice> GetContainerInBothLoadListAndDischargeByTfcI(string tfCodeI)
        {
            string sql = $@"
                            
                          SELECT 
	                          d.item_no,i.item_key,i.ENTRY_VES_CALL_SIGN,i.DISCH_VES,i.ENTRY_VES_NAME,i.TRANSPORT_TYPE,i.tfc_code,i.ves_name,
                                       i.voyage,i.ll_pod,i.pod,i.fin_disch_port,i.created_by,i.is_coprar_load, i.LOAD_LIST_FLG
                          FROM DISCHARGE_LIST d
	                          INNER JOIN EDI_ITEM_PRE_ADVICE i ON i.ITEM_NO = d.ITEM_NO AND d.FEL = i.FEL
		                          AND (i.IG_FLG = 'N' OR i.IG_FLG = ' ')
                          WHERE d.TFC_CODE = '{tfCodeI}'

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from row in queryResult.AsEnumerable()
                         select new EdiItemPreAdvice(row);

            return result.ToList();
        }


        /// <summary>
        /// Get list of container discharge by vessel
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<DischargeList> GetDischargeListByVesselVoyage(string tfcCodeI)
        {
            if (tfcCodeI.IsEmpty())
            {
                return new List<DischargeList>();
            }

            string sWhereVessel = $" Tfc_Code = '{tfcCodeI}'";

            var sql = $@"SELECT * FROM DISCHARGE_LIST WHERE {sWhereVessel}";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from record in queryResult.AsEnumerable()
                         select new DischargeList(record);

            return result.ToList();

        }

        /// <summary>
        /// Get list of container in discharge list by vessel,
        /// which has not discharged to yard
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<DischargeList> GetDischargeListNotDischargedByVesId(string tfcCodeI)
        {
            if (tfcCodeI.IsEmpty())
            {
                return new List<DischargeList>();
            }

            string sWhereVessel = $" d.Tfc_Code = '{tfcCodeI}'";

            var sql = $@"SELECT * FROM DISCHARGE_LIST d
                                        LEFT JOIN ITEM i ON i.ITEM_KEY = d.ITEM_KEY AND to_char(i.ARR_TS, 'YYYY') != '1900'
                                        WHERE {sWhereVessel} AND i.ITEM_KEY IS NULL ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from record in queryResult.AsEnumerable()
                         select new DischargeList(record);

            return result.ToList();

        }

        public List<DischargeList> GetDischargeListNotSyncBaplie(string tfcCodeI)
        {
            if (tfcCodeI.IsEmpty())
            {
                return new List<DischargeList>();
            }

            var sql = $@"SELECT * FROM DISCHARGE_LIST d 
	                                    LEFT OUTER JOIN BAPLIE b ON b.CTR_NO = d.ITEM_NO AND b.TFC_CODE_I = d.TFC_CODE AND (b.HIST_FLG = ' ' OR  b.HIST_FLG = 'N')
                                    WHERE d.TFC_CODE = '{tfcCodeI}' AND (d.HIST_FLG = ' ' OR d.HIST_FLG = 'N') AND b.BAPLIE_KEY IS NULL ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from record in queryResult.AsEnumerable()
                         select new DischargeList(record);

            return result.ToList();
        }

        /// <summary>
        /// Get discharge comments data from DISCHARGE_LIST_COMMENTS table. 
        /// Return null if it's not found.
        /// </summary>
        /// <returns>
        ///         - Return an DischargeListComments object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischargeListComments> GetDischargeListComments(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT *
                                        FROM DISCHARGE_LIST_COMMENTS
                                        WHERE MANIFEST_KEY = {manifestKey}  
                                         ORDER BY COMMENT_SEQ DESC                                             

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischargeListComments(record);

            return result.ToList();
        }

        /// <summary>
        /// Get discharge list detail by given manifestKey
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns></returns>
        public DischargeList GetDischargeListByManifestKey(int manifestKey)
        {
            //UPD_TS, CRT_TS, HIST_FLG, MANIFEST_KEY, BILL_OF_LADING, VES_CD, PKG_COUNT, FDISCH_PORT, COMMODITY, COPRAR_DISCHARGE, 
            //ISO, CUSTOMER_CODE, STOWLOC, EXIT_LOAD_VOY, EXIT_CALL_SIGN, FIN_DEST_NAME, CATEGORY, TARE, BB_FLG, SITE_ID, 
            //VES_NAME, ITEM_KEY, EXIT_VESSEL_NAME, STOWAGE_INSTRUCTION_TEXT, EXIT_TFC_CODE, EXIT_DEP_BY, UPD_CNT, 
            //LL_POD, ORIG_ISO, VES_ID, TERMINAL_ID, PTI, SHIPPER, FORWARDER, CREATED_BY, SLOT_CODE, AGENT, LINE_OPER, 
            //TFC_CODE, BOOK_NO, LOAD_PORT, DISCH_PORT, PLACE_OF_RECEIPT, PLACE_OF_DELIVERY, CONSIGNEE, SEAL_NO_CURRENT, 
            //ITEM_NO, ITEM_TYPE, FEL, ITEM_SIZE, HEIGHT, PKG_UNIT, CGO_GROSS_WT, WEIGHT, RFR_TRANS_TEMP, GRADE 

            string sql = $@"
                                        SELECT *
                                        FROM DISCHARGE_LIST
                                        WHERE MANIFEST_KEY = {manifestKey}

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new DischargeList(item);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// Get discharged container data from DISCHARGE_LIST table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="vesId"> </param>
        /// <param name="tfcCode">If tfcCode is empty, the method will ignore the tfc code condition</param>
        /// <param name="itemNo"> </param>
        /// <returns>
        ///         - Return an DischargeList object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public DischargeList GetDischargeList(string itemNo, string vesId, string tfcCode)
        {
            string tfcCondition = "";

            if (vesId.TrimEx() != "")
            {
                tfcCondition = $@" AND VES_ID = '{vesId}'";
            }
            else if (tfcCode.TrimEx() != "")
            {
                tfcCondition = $@" AND TFC_CODE = '{tfcCode}'";
            }

            if (tfcCondition.Trim() == string.Empty)
            {
                tfcCondition = " AND (HIST_FLG = ' ' OR HIST_FLG = 'N') ";
            }

            //HIST_FLG, MANIFEST_KEY, BILL_OF_LADING, VES_CD, PKG_COUNT, FDISCH_PORT, COMMODITY,  
            //ISO, STOWLOC, EXIT_LOAD_VOY, FIN_DEST_NAME, CATEGORY, TARE, SITE_ID, 
            //ITEM_KEY, STOWAGE_INSTRUCTION_TEXT, EXIT_TFC_CODE, EXIT_DEP_BY,  
            //LL_POD, ORIG_ISO, VES_ID, TERMINAL_ID, PTI, SHIPPER, FORWARDER, CREATED_BY, SLOT_CODE, AGENT, LINE_OPER, 
            //TFC_CODE, BOOK_NO, LOAD_PORT, DISCH_PORT, PLACE_OF_RECEIPT, PLACE_OF_DELIVERY, CONSIGNEE, SEAL_NO_CURRENT, 
            //ITEM_NO, ITEM_TYPE, FEL, ITEM_SIZE, HEIGHT, PKG_UNIT, CGO_GROSS_WT, WEIGHT, GRADE, DOMESTIC 

            string sql = $@"
                                        SELECT *
                                        FROM DISCHARGE_LIST
                                        WHERE ITEM_NO = '{itemNo}'
                                              {tfcCondition}
                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new DischargeList(item);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// Get discharged container data from DISCHARGE_LIST table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="vesId"> </param>
        /// <param name="tfcCode">If tfcCode is empty, the method will ignore the tfc code condition</param>
        /// <param name="itemNo"> </param>
        /// <param name="includeHistory"></param>
        /// <returns>
        ///         - Return an DischargeList object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischargeList> GetDischargeListByContDigit(string itemNo, string vesId, string tfcCode, bool? includeHistory = false)
        {
            string tfcCondition = "";

            if (vesId.TrimEx() != "")
            {
                tfcCondition = $@" AND VES_ID = '{vesId}'";
            }
            else if (tfcCode.TrimEx() != "")
            {
                tfcCondition = $@" AND TFC_CODE = '{tfcCode}'";
            }

            if (tfcCondition.Trim() == string.Empty)
            {
                tfcCondition = " AND (HIST_FLG = ' ' OR HIST_FLG = 'N') ";
            }

            string sql = $@"
                                        SELECT *
                                        FROM DISCHARGE_LIST
                                        WHERE ITEM_NO like '%{itemNo}%'
                                              {tfcCondition}
                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new DischargeList(item);

            if (result.Any())
            {
                return result.ToList();
            }
            return null;
        }

        /// <summary>
        /// Get discharge special handling data from DISCH_SPECIAL_HANDLING table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischSpecialHandling object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischSpecialHandling> GetDischSpecialHandling(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, CODE, DESCRIPTION, CRT_TS, UPD_TS, SEQ_NO 
                                        FROM DISCH_SPECIAL_HANDLING
                                        WHERE   MANIFEST_KEY = {manifestKey} 
                                            AND to_char(COMPL_TS,'dd/mm/yyyy') = '31/12/1900'
                                         ORDER BY CRT_TS DESC                                              

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischSpecialHandling(record);

            return result.ToList();

        }

        /// <summary>
        /// Get discharge seal data from DISCH_SEAL table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischSeal object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischSeal> GetDischSeal(int manifestKey)
        {

            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, SEAL_TYPE, SEAL_NO, SEAL_DATE, UPD_TS, SEAL_SEQ, UPD_CNT 
                                        FROM DISCH_SEAL
                                        WHERE MANIFEST_KEY = {manifestKey}  
                                        ORDER BY UPD_TS DESC                                            

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischSeal(record);

            return result.ToList();

        }

        /// <summary>
        /// Get discharge oog data from DISCH_OOG table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischOog object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public DischOog GetDischOog(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, OOG_HEIGHT, OOG_LEFT, OOG_RIGHT, OOG_TOP, OOG_BACK, OOG_FRONT, CRT_TS, LENUNITS, UPD_CNT, CABLE_REQUIRED
                                        FROM DISCH_OOG
                                        WHERE MANIFEST_KEY = {manifestKey}                                              

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischOog(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }

            return null;

        }

        /// <summary>
        /// Get discharge dangerous data from DISCH_DANGEROUS table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischDangerous object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischDangerous> GetDischDangerous(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, DGS_SEQ, DGS_CLASS, UN_NO, ACTIVITY, UPD_TS, UPD_CNT 
                                        FROM DISCH_DANGEROUS
                                        WHERE   MANIFEST_KEY = {manifestKey}    
                                            AND DGS_CLASS != ' '
                                        ORDER BY DGS_CLASS

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischDangerous(record);

            return result.ToList();

        }


        /// <summary>
        /// Get discharge reefer data from DISCH_REEFER table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischReefer object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public DischReefer GetDischReefer(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, SETTING_TEMP, MAX_TEMP, MIN_TEMP, TEMP_UNIT, UPD_TS 
                                        FROM DISCH_REEFER
                                        WHERE MANIFEST_KEY = {manifestKey}                                              

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischReefer(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }

            return null;

        }

        /// <summary>
        /// Get discharge attached details data from DISCH_ATTACHED_DETAILS table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischAttachedDetails object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischAttachedDetails> GetDischAttachedDetails(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, ITEM_ID, ATT_ITEM_KEY, EQUIPMENT_TYPE, UPD_TS, BB_ID, BB_HEIGHT, BB_LENGHT, BB_WIDTH, BB_QTY, 
                                                BB_TYPE, UPD_CNT 
                                        FROM DISCH_ATTACHED_DETAILS
                                        WHERE MANIFEST_KEY = {manifestKey}
                                        ORDER BY UPD_TS DESC                                               

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischAttachedDetails(record);

            return result.ToList();
        }

        /// <summary>
        /// Get discharge damage data from DISCH_DAMAGE table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischDamage object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischDamage> GetDischDamage(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, DAMAGE_CD, DAMAGE_DESC, DAMAGE_LOC, DAMAGE_SEQ, UPD_TS, UPD_CNT 
                                        FROM DISCH_DAMAGE
                                        WHERE MANIFEST_KEY = {manifestKey}                                              

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischDamage(record);

            return result.ToList();

        }

        /// <summary>
        /// Get discharge breakbulk data from DISCH_BREAKBULK table. 
        /// Return null if it's not found.
        /// </summary>
        /// <param name="manifestKey"></param>
        /// <returns>
        ///         - Return an DischBreakBulk object if it is found.
        ///         - Return a null value if it is not found.
        /// </returns>
        public List<DischBreakbulk> GetDischBreakBulk(int manifestKey)
        {
            string sql = $@"
                            
                                        SELECT 
                                                MANIFEST_KEY, BB_ID, BB_HEIGHT, BB_LENGTH, BB_NUM, BB_TYPE, BB_WIDTH, ET_STOWLOC_1, ET_STOWLOC_2, ET_STOWLOC_3, 
                                                ET_STOWLOC_4, ET_STOWLOC_5, ET_STOWLOC_6, ET_STOWLOC_7, ET_STOWLOC_8, ET_STOWLOC_9, UPD_TS 
                                        FROM DISCH_BREAKBULK
                                        WHERE MANIFEST_KEY = {manifestKey}                                              

                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DischBreakbulk(record);

            return result.ToList();
        }


        /// <summary>
        /// Get wrong seal report by discharge vessel
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <param name="psIssueBy"></param>
        /// <returns></returns>
        public List<WrongSealReport> GetWrongSealReportByDischargeVessel(string tfcCodeI, string psIssueBy)
        {

            try
            {
                string lsQuery = string.Format(@"

                        SELECT DISTINCT i.LINE_OPER, v.VES_ID , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                               i.ITEM_NO, i.ISO , i.LENGTH, its.SEAL_TYPE , its.SEAL_NO AS INVALID_SEAL, GETSEALNO(i.ITEM_KEY) CURRENT_SEAL, i.ITEM_KEY, its.REMARKS, its.SEAL_DATE, its.CRT_TS
                        FROM ITEM i
                            INNER JOIN ITEM_SEAL its ON its.ITEM_KEY = i.ITEM_KEY AND its.SEAL_TYPE = 'LOP'  AND its.INVALID_SEAL = 'Y' AND (its.INVALID_SEAL_CHECKING_PLACE = ' ' OR its.INVALID_SEAL_CHECKING_PLACE = 'CTA')
                            INNER JOIN VESSEL_DETAILS v ON v.TFC_CODE_I = '{0}'
                        WHERE i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_I 

                        UNION

                        SELECT DISTINCT i.LINE_OPER, v.VES_ID , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                               i.ITEM_NO, i.ISO , i.LENGTH, 'LOP' SEAL_TYPE , ' ' AS INVALID_SEAL, GETSEALNO(i.ITEM_KEY) CURRENT_SEAL, i.ITEM_KEY, ' ' REMARKS, to_date('31121900', 'ddMMyyyy') SEAL_DATE, i.ARR_TS CRT_TS
                        FROM ITEM i
                            INNER JOIN VESSEL_DETAILS v ON v.TFC_CODE_I = '{0}'
                            INNER JOIN DISCHARGE_LIST d ON d.TFC_CODE = v.TFC_CODE_I AND d.ITEM_KEY = i.ITEM_KEY
                             LEFT OUTER JOIN DISCH_SEAL ds ON ds.MANIFEST_KEY = d.MANIFEST_KEY AND ds.SEAL_TYPE = 'LOP'
                        WHERE i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_I AND EXISTS (SELECT 1 FROM ITEM_SEAL x WHERE x.ITEM_KEY = i.ITEM_KEY )
                         AND ds.MANIFEST_KEY IS NULL
                         ", tfcCodeI).TrimEx();

                //string lsQuery = string.Format(@"

                //        SELECT DISTINCT i.LINE_OPER, v.VES_ID , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                //               i.ITEM_NO, i.ISO , i.LENGTH, its.SEAL_TYPE , its.SEAL_NO AS INVALID_SEAL, its.CURRENT_SEAL_NO CURRENT_SEAL, i.ITEM_KEY, its.REMARKS, its.SEAL_DATE, its.CRT_TS
                //        FROM ITEM i
                //            INNER JOIN ITEM_SEAL its ON its.ITEM_KEY = i.ITEM_KEY AND its.SEAL_TYPE = 'LOP'  AND its.INVALID_SEAL = 'Y' AND (its.INVALID_SEAL_CHECKING_PLACE = ' ' OR its.INVALID_SEAL_CHECKING_PLACE = 'CTA')
                //            INNER JOIN VESSEL_DETAILS v ON v.TFC_CODE_I = '{0}'
                //        WHERE i.ARR_CAR = v.TFC_CODE_I 

                //        UNION

                //        SELECT DISTINCT i.LINE_OPER, v.VES_ID , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                //               i.ITEM_NO, i.ISO , i.LENGTH, 'LOP' SEAL_TYPE , ' ' AS INVALID_SEAL, its.CURRENT_SEAL_NO CURRENT_SEAL, i.ITEM_KEY, ' ' REMARKS, to_date('31121900', 'ddMMyyyy') SEAL_DATE, i.ARR_TS CRT_TS
                //        FROM ITEM i
                //            INNER JOIN VESSEL_DETAILS v ON v.TFC_CODE_I = '{0}'
                //            INNER JOIN ITEM_SEAL its ON its.ITEM_KEY = i.ITEM_KEY AND its.SEAL_TYPE = 'LOP' AND its.CURRENT_SEAL_NO <> ' '
                //            INNER JOIN DISCHARGE_LIST d ON d.TFC_CODE = v.TFC_CODE_I AND d.ITEM_KEY = i.ITEM_KEY
                //             LEFT OUTER JOIN DISCH_SEAL ds ON ds.MANIFEST_KEY = d.MANIFEST_KEY AND ds.SEAL_TYPE = 'LOP'
                //        WHERE i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_I 
                //         AND ds.MANIFEST_KEY IS NULL
                //         ", tfcCodeI).TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new WrongSealReport(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get wrong seal report by discharge vessel
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<DamageReport> GetDamageReportByDischargeVessel(string tfcCodeI)
        {

            try
            {
                string lsTfcCodeI = "";

                if (tfcCodeI.IsEmpty())
                {
                    return new List<DamageReport>();
                }

                lsTfcCodeI = " v.TFC_CODE_I = '" + tfcCodeI.TrimEx() + "' ";

                string lsQuery = $@"
                                
                        SELECT DISTINCT i.LINE_OPER, v.VES_ID, v.VES_CD , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                               i.ITEM_NO, i.FEL, i.ISO , i.LENGTH, i.ITEM_KEY, 
                               trim(itd.DAMAGE_DESC) ||' - ' ||  trim(nvl(s.LOCAL_DESCR, '')) || ' - ' || trim(itd.DAMAGE_DIMENSION) as VN_DESCRIPTION,
                               trim(dc.DESCR_EN) ||' - ' ||  trim(nvl(s.DESCR, '')) || ' - ' || trim(itd.DAMAGE_DIMENSION) as EN_DESCRIPTION
                         FROM ITEM i
                            INNER JOIN ITEM_DAMAGE itd ON itd.ITEM_KEY = i.ITEM_KEY  AND itd.DAMAGE_INSPECT_LOCATION = 'V'
                            LEFT JOIN DAMAGE_CODE dc ON dc.DAMAGE_CD = itd.DAMAGE_CD
                            INNER JOIN VESSEL_DETAILS v ON {lsTfcCodeI}
                            LEFT JOIN SYS_CODES s ON s.CODE_TP = '{SysCodeType.DamageLoc}' AND s.CODE_REF = itd.DAMAGE_LOC
                         WHERE i.ARR_CAR = v.TFC_CODE_I
   
                         ORDER BY i.ITEM_NO ".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new DamageReport(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get container report by discharge vessel, with Rule:
        /// 1. Container Discharge 
        /// 2. Container Stay onboard
        /// Created by dvthuan 28/03/2014
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<ContainerDischargeLoad> GetContainerReportByDischargeVessel(string tfcCodeI)
        {

            try
            {
                var lsTfcCodeI = "";

                if (tfcCodeI.IsEmpty())
                {
                    return new List<ContainerDischargeLoad>();
                }

                lsTfcCodeI = $" v.TFC_CODE_I = '{tfcCodeI.TrimEx()}' ";

                var lsQuery =
                    $@"SELECT nvl (i.LINE_OPER, e.LINE_OPER) AS LINE_OPER
                            , v.VES_ID
                            , v.VES_CD
                            , v.IN_VOYAGE VOYAGE
                            , v.VES_NAME
                            , v.ACT_DEP_TS
                            , v.ACT_BERTH_TS
                            , nvl (i.ITEM_NO, e.ITEM_NO) AS ITEM_NO
                            , nvl (i.FEL, e.FEL) AS FEL
                            , nvl (i.ISO, e.ISO) AS ISO
                            , nvl (i.LENGTH, e.ITEM_SIZE) AS LENGTH
                            , i.ITEM_KEY
                            , i.COMMENTS AS DESCRIPTION
                            , nvl (i.IS_REEFER, e.IS_REEFER) AS IS_REEFER
                            , nvl (i.IS_OOG, e.IS_OOG) AS IS_OOG
                            , nvl (i.IS_DANGEROUS, e.IS_DANGEROUS) AS IS_DANGEROUS
                            , nvl (i.GROSS, e.WEIGHT) AS GROSS
                            , i.IS_FL
                            , nvl (i.ITEM_TYPE, e.ITEM_TYPE) AS ITEM_TYPE
                            , 'IMDG Class: ' || e.HAZ_CLASS AS HAZ_CLASS
                            , 'Un no: ' || e.UN_NO AS UN_NO
                            , nvl (i.OOG_WIDTH, e.OOG_WIDTH) AS OOG_WIDTH
                            , nvl (i.OOG_HEIGHT, e.OOG_HEIGHT) AS OOG_HEIGHT
                            , nvl (i.OOG_LENGTH, e.OOG_LENGTH) AS OOG_LENGTH
                            , nvl (i.OOG_BACK, e.OOG_BACK) AS OOG_BACK
                            , nvl (i.OOG_FRONT, e.OOG_FRONT) AS OOG_FRONT
                            , nvl (i.OOG_LEFT, e.OOG_LEFT) AS OOG_LEFT
                            , nvl (i.OOG_TOP, e.OOG_TOP) AS OOG_TOP
                            , nvl (i.OOG_RIGHT, e.OOG_RIGHT) AS OOG_RIGHT
                            , e.STOWLOC
                            , nvl (ir.SETTING_TEMP, 9999) AS SETTING_TEMP
                            , nvl (ir.FROZEN_TEMP, 9999) AS FROZEN_TEMP
                            , ir.VENT_VOLUMETRIC_RATE
                            , ir.HUMIDITY
                            , ir.O2
                            , ir.CO2
                            , CASE WHEN EXISTS (SELECT io.ITEM_KEY
                                                FROM TOPOVN.ITEM_OOG io
                                                WHERE io.ITEM_KEY = i.ITEM_KEY AND io.CABLE_REQUIRED = 'Y') 
                                                OR EXISTS (SELECT ch.ITEM_KEY
	                                                       FROM TOPOVN.CABLEHOOK_REQUEST ch
	                                                       WHERE ch.ITEM_KEY = i.ITEM_KEY AND ch.VES_ID = v.VES_ID AND ch.CH_METHOD 
	                                                       IN ('V_NTAU', 'V_XTAU', 'V_DNTAU', 'V_DXTAU', 'Y_NTAU', 'Y_XTAU', 'Y_DNTAU', 'Y_DXTAU')
	                                                       AND to_char (ch.CANCEL_REQUEST_TS, 'YYYY') = '1900') 
	                                                       THEN 'Y' ELSE 'N' END AS CABLE_HOOK
                            , nvl (i.CUST_VGM, 0) AS WEIGHT
                            , e.CATEGORY
                            , CASE 
                                WHEN E.HIST_FLG <> 'Y' Then GetHazUnNo (e.MANIFEST_KEY, 'DISCHARGE_LIST')
                                ELSE GetHazUnNo (i.ITEM_KEY, 'ITEM')
                              END AS HAZ_UNNO
                        FROM VIEW_DISCHARGE_LIST e INNER
                        JOIN VESSEL_DETAILS v ON v.TFC_CODE_I = e.TFC_CODE
                        LEFT OUTER JOIN VIEW_ITEM i ON e.TFC_CODE = i.ARR_CAR AND e.ITEM_KEY = i.ITEM_KEY
                        LEFT OUTER JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
                        WHERE {lsTfcCodeI}".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new ContainerDischargeLoad(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        /// <summary>
        ///     Get container reefer report by discharge vessel, with Rule:
        ///     1. Container Discharge
        ///     2. Container Stay onboard
        ///     Created by dvthuan 28/03/2014
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<ContainerDischargeLoad> GetContainerReeferReportByDischargeVessel(string tfcCodeI)
        {
            try
            {
                var lsTfcCodeI = "";

                if (tfcCodeI.IsEmpty())
                {
                    return new List<ContainerDischargeLoad>();
                }

                lsTfcCodeI = " v.TFC_CODE_I = '" + tfcCodeI.TrimEx() + "' ";

                var lsQuery = $@"
                                
                        SELECT e.LINE_OPER, v.VES_ID, v.VES_CD , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS,
                               e.ITEM_NO, e.FEL, e.ISO , iso.LENGTH, e.ITEM_KEY, e.ITEM_TYPE, 
                               nvl(trim(GETSEALNO(i.ITEM_KEY, 'ITEM')), GETSEALNO(e.MANIFEST_KEY, 'DISCHARGE_LIST')) SEAL_NO, 
                                 GET_SPHD(e.MANIFEST_KEY, 'DISCHARGE_LIST') AS SPHD_CODE,
                               --CASE WHEN dr.NO_PLUGIN_REQUIRED = 'Y' THEN 'KCD' ELSE '' END 
                               nvl(trim(GETCOMMENTS(i.ITEM_KEY, 'ITEM')), GETCOMMENTS(e.MANIFEST_KEY, 'DISCHARGE_LIST')) DESCRIPTION,
       
                               'Y' IS_REEFER , nvl(i.GROSS, e.WEIGHT) GROSS,
                                CASE WHEN dr.SETTING_TEMP IS NULL OR dr.SETTING_TEMP >= 9999 THEN ' '
   		                                ELSE to_char(dr.SETTING_TEMP,'fm99990.0') END AS SETTING_TEMP,

                                CASE WHEN ir.FROZEN_TEMP IS NULL OR ir.FROZEN_TEMP >= 9999 THEN ' '
   		                                ELSE to_char(ir.FROZEN_TEMP,'fm99990.0') END AS FROZEN_TEMP,

	                            nvl(ir.TEMP_UNIT, nvl(dr.TEMP_UNIT, 'C')) TEMP_UNIT, 
                                nvl(ir.VENT_VOLUMETRIC_RATE, dr.VENT_VOLUMETRIC_RATE) AS VENT_VOLUMETRIC_RATE, 
                                nvl(ir.VENT_VOLUMETRIC_UNIT, dr.VENT_VOLUMETRIC_UNIT) AS VENT_VOLUMETRIC_UNIT,
                                ir.HUMIDITY, ir.O2, ir.CO2, GetHazUnNo(e.MANIFEST_KEY, 'DISCHARGE_LIST') AS HAZ_UNNO
       
                         FROM VESSEL_DETAILS v
                            INNER JOIN DISCHARGE_LIST e ON e.TFC_CODE = v.TFC_CODE_I
    	                        LEFT OUTER JOIN DISCH_REEFER dr ON dr.MANIFEST_KEY = e.MANIFEST_KEY
                            INNER JOIN CONVERT_TO_ISO iso ON iso.ISO = e.ISO AND iso.REEFER_FLG = 'Y'
    	                        LEFT OUTER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY
                                LEFT OUTER JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
                         WHERE {lsTfcCodeI} 

                         ORDER BY i.ITEM_NO ".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new ContainerDischargeLoad(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get container report by discharge vessel, with Rule:
        /// 1. Container Restowed 
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<ContainerDischargeLoad> GetContainerRestowedReportByVessel(string tfcCodeI)
        {

            try
            {
                var lsTfcCodeI = "";

                if (tfcCodeI.IsEmpty())
                {
                    return new List<ContainerDischargeLoad>();
                }

                lsTfcCodeI = " v.TFC_CODE_I = '" + tfcCodeI.TrimEx() + "' ";

                var lsQuery = $@"
                                
                        SELECT DISTINCT i.LINE_OPER, v.VES_ID, v.VES_CD , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                            i.ITEM_NO, i.FEL, i.ISO , i.LENGTH, i.ITEM_KEY, i.COMMENTS as DESCRIPTION,i.IS_REEFER , i.IS_OOG, i.IS_DANGEROUS, 
                            i.IS_FL, i.ITEM_TYPE, i.HAZ_CLASS, i.UN_NO, i.OOG_WIDTH, i.OOG_HEIGHT, i.OOG_LENGTH, i.OOG_BACK, i.OOG_FRONT, i.OOG_LEFT, 
                            i.OOG_TOP, i.OOG_RIGHT, (l.X || l.Y || l.Z) STOWLOC, i.SLOT_CODE AS SLOT, i.GROSS, i.SEAL_NO,i.DISCH_PORT AS POD

                        FROM VIEW_ITEM i
                        INNER JOIN ITEM_LOCATION l ON i.ITEM_KEY = l.ITEM_KEY AND l.STK_PCH= 'C' AND l.STK_CLASS = 'V'
                        INNER JOIN VESSEL_DETAILS v ON  {lsTfcCodeI} 
                        WHERE i.ARR_CAR = v.TFC_CODE_I AND i.DEP_CAR = v.TFC_CODE_E ".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new ContainerDischargeLoad(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get container report by loading vessel, with Rule:
        /// 1. Container loading: Những container đã lên tàu
        /// Created by dvthuan 30/03/2014
        /// Update by dvthuan 6/5/2014: Thêm category = 'R' trong loadlist container
        /// </summary>
        /// <param name="tfcCodeE"></param>
        /// <returns></returns>
        public List<ContainerDischargeLoad> GetContainerReportByLoading(string tfcCodeE)
        {

            try
            {
                var lsTfcCodeE = "";

                if (tfcCodeE.IsEmpty())
                {
                    return new List<ContainerDischargeLoad>();
                }

                lsTfcCodeE = " v.TFC_CODE_E = '" + tfcCodeE.TrimEx() + "' ";

                var lsQuery = $@"                       
                                SELECT DISTINCT i.LINE_OPER
                                , v.VES_ID
                                , v.VES_CD
                                , v.IN_VOYAGE VOYAGE
                                , v.VES_NAME
                                , v.ACT_DEP_TS
                                , v.ACT_BERTH_TS
                                , i.ITEM_NO
                                , i.FEL
                                , i.ISO
                                , i.LENGTH
                                , i.ITEM_KEY
                                , i.COMMENTS AS DESCRIPTION
                                , i.IS_REEFER
                                , i.IS_OOG
                                , i.IS_DANGEROUS
                                , i.IS_FL
                                , i.ITEM_TYPE
                                , i.HAZ_CLASS
                                , i.UN_NO
                                , i.OOG_WIDTH
                                , i.OOG_HEIGHT
                                , i.OOG_LENGTH
                                , i.OOG_BACK
                                , i.OOG_FRONT
                                , i.OOG_LEFT
                                , i.OOG_TOP
                                , i.OOG_RIGHT
                                , (l.X || l.Y || l.Z) STOWLOC
                                , e.Liner_Code AS SLOT
                                , i.GROSS
                                , i.SEAL_NO
                                , e.POD
                                , e.COMMODITY
                                , nvl (ir.SETTING_TEMP, 9999) AS SETTING_TEMP
                                , nvl (ir.FROZEN_TEMP, 9999) AS FROZEN_TEMP
                                , nvl (ir.TEMP_UNIT, ' ') TEMP_UNIT
                                , ir.VENT_VOLUMETRIC_RATE
                                , ir.VENT_VOLUMETRIC_UNIT
                                , ir.HUMIDITY
                                , ir.O2
                                , ir.CO2
                                , ir.UNPLUG_TS
                                , CASE WHEN ir.SETTING_TEMP >= 0 AND o.TEMP_UPPER <>0 AND ir.FROZEN_TEMP > ir.SETTING_TEMP + o.TEMP_UPPER THEN 'Y'
                                       	WHEN ir.SETTING_TEMP < 0 AND o.TEMP_LOWER <>0 AND ir.FROZEN_TEMP > ir.SETTING_TEMP + o.TEMP_LOWER THEN 'Y'
                                       	ELSE 'N' END AS TEMP_WARNING
                                , CASE WHEN EXISTS (SELECT io.ITEM_KEY FROM TOPOVN.ITEM_OOG io WHERE io.ITEM_KEY = i.ITEM_KEY 
                	                AND io.CABLE_REQUIRED = 'Y')
			                        OR EXISTS (SELECT ch.ITEM_KEY FROM TOPOVN.CABLEHOOK_REQUEST ch WHERE ch.ITEM_KEY = i.ITEM_KEY 
									                    AND ch.VES_ID = v.VES_ID 
									                    AND ch.CH_METHOD IN ('V_NTAU', 'V_XTAU', 'V_DNTAU', 'V_DXTAU'
										                    , 'Y_NTAU', 'Y_XTAU', 'Y_DNTAU', 'Y_DXTAU')
									                    AND TO_CHAR(ch.CANCEL_REQUEST_TS, 'YYYY') = '1900')
				                    THEN 'Y'
				                    ELSE 'N' 
				                    END AS CABLE_HOOK
                                , CASE WHEN EXISTS (SELECT iv.CERTIFIED_WEIGHT FROM ITEM_VGM iv WHERE iv.ITEM_KEY = i.ITEM_KEY)
                	                THEN 
                                        (SELECT iv.CERTIFIED_WEIGHT FROM (SELECT * FROM ITEM_VGM iv1 ORDER BY iv1.CRT_TS DESC) iv WHERE 
                                            iv.ITEM_KEY = i.ITEM_KEY AND ROWNUM = 1)
                	                ELSE 0
                                    END AS BIGGEST_GROSS
                                , i.CUST_VGM As WEIGHT
                                , i.CATEGORY
                                , GetHazUnNo(i.ITEM_KEY) AS HAZ_UNNO
                                FROM VIEW_ITEM i
                                INNER JOIN ITEM_LOCATION l ON i.ITEM_KEY = l.ITEM_KEY AND l.STK_PCH= 'C'
                                INNER JOIN VESSEL_DETAILS v ON  {lsTfcCodeE}
                                LEFT JOIN EDI_ITEM_PRE_ADVICE e ON i.ITEM_KEY = e.ITEM_KEY
                                LEFT OUTER JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
                                LEFT JOIN LINE_OPER o ON i.LINE_OPER = o.LINE_OPER
                                WHERE i.DEP_CAR = v.TFC_CODE_E AND extract(YEAR from i.DEP_TS) > 1900
                                ORDER BY ITEM_NO".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new ContainerDischargeLoad(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Container Lạnh xuất tàu
        /// </summary>
        /// <param name="tfcCodeE"></param>
        /// <returns></returns>
        public List<ContainerDischargeLoad> GetContainerReportByReeferLoading(string tfcCodeE)
        {
            try
            {
                var lsTfcCodeE = "";

                if (tfcCodeE.IsEmpty())
                {
                    return new List<ContainerDischargeLoad>();
                }

                lsTfcCodeE = " v.TFC_CODE_E = '" + tfcCodeE.TrimEx() + "' ";

                var lsQuery = $@"                       
                                    SELECT DISTINCT i.LINE_OPER, v.VES_ID, v.VES_CD , v.IN_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                                       i.ITEM_NO, i.FEL, i.ISO , i.LENGTH, i.ITEM_KEY, i.COMMENTS as DESCRIPTION,i.IS_REEFER , i.IS_OOG, i.IS_DANGEROUS, 
                                       i.IS_FL, i.ITEM_TYPE, HAZ_UNNO, i.HAZ_CLASS, i.UN_NO, i.OOG_WIDTH, i.OOG_HEIGHT, i.OOG_LENGTH, i.OOG_BACK, i.OOG_FRONT, i.OOG_LEFT, 
                                       i.OOG_TOP, i.OOG_RIGHT, e.Liner_Code AS SLOT, i.GROSS, i.SEAL_NO, e.POD, e.COMMODITY,
                                       e.RF_TEMP AS SETTING_TEMP, 
                                       CASE WHEN ir.FROZEN_TEMP IS NULL OR ir.FROZEN_TEMP >= 9999 THEN ' '
   		                                    ELSE to_char(ir.FROZEN_TEMP) END AS FROZEN_TEMP, 
   	                                    nvl(e.TEMP_UNIT, ' ') TEMP_UNIT, 
                                       CASE WHEN e.VENT_VOLUMETRIC_RATE >0 THEN e.VENT_VOLUMETRIC_RATE ELSE ir.VENT_VOLUMETRIC_RATE END AS VENT_VOLUMETRIC_RATE,
                                       nvl(ir.VENT_VOLUMETRIC_UNIT, e.VENT_VOLUMETRIC_UNIT) AS VENT_VOLUMETRIC_UNIT, -- dvthuan add 4/3/2015
                                       ir.HUMIDITY, ir.O2, ir.CO2, ir.UNPLUG_TS,
                                       CASE WHEN ir.SETTING_TEMP >= 0 AND o.TEMP_UPPER <>0 AND ir.FROZEN_TEMP > ir.SETTING_TEMP + o.TEMP_UPPER THEN 'Y' 
   		                                    WHEN ir.SETTING_TEMP < 0 AND o.TEMP_LOWER <>0 AND ir.FROZEN_TEMP > ir.SETTING_TEMP + o.TEMP_LOWER THEN 'Y' 
   		                                    ELSE 'N' END AS TEMP_WARNING, trim(i.SPHD_CODE) AS SPHD_CODE
                                    FROM VIEW_ITEM i
	                                    INNER JOIN VESSEL_DETAILS v ON {lsTfcCodeE}
	                                    LEFT JOIN EDI_ITEM_PRE_ADVICE e ON i.ITEM_KEY = e.ITEM_KEY 
	                                    LEFT OUTER JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
	                                    INNER JOIN CONVERT_TO_ISO iso ON iso.ISO = i.ISO AND iso.REEFER_FLG = 'Y'
	                                    LEFT JOIN LINE_OPER o ON i.LINE_OPER = o.LINE_OPER
                                    WHERE i.DEP_CAR = v.TFC_CODE_E AND extract(YEAR from i.DEP_TS) > 1900
                                    ORDER BY ITEM_NO".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new ContainerDischargeLoad(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }
        /// <summary>
        /// Container bổ sung sau closing time
        /// </summary>
        /// <param name="tfcCodeE"></param>
        /// <returns></returns>
        public DataTable GetContainerAfterClosingTime(string tfcCodeE)
        {

            try
            {
                if (tfcCodeE.IsEmpty())
                {
                    return new DataTable();
                }

                var lsTfcCodeE = " v.TFC_CODE_E = '" + tfcCodeE.TrimEx() + "' ";

                var lsQuery =
                    $@"SELECT e.FILE_SEQ, CRT_TS, CASE WHEN extract(YEAR FROM v.RECV_CTR_CUTOFF_TS) = 1900 THEN v.EST_BERTH_TS ELSE v.RECV_CTR_CUTOFF_TS END CLOSING_TIME,
                            e.ITEM_NO, e.ISO, e.LENGTH, e.FEL, e.LINER_CODE,e.GROSS,e.POD, e.COMMENTS
                            FROM   VESSEL_DETAILS v
                            INNER JOIN EDI_ITEM_PRE_ADVICE e ON e.TFC_CODE = v.TFC_CODE_E 
                            WHERE {lsTfcCodeE} AND v.RECV_CTR_CUTOFF_TS  < CRT_TS       
                            ORDER BY e.FILE_SEQ, CRT_TS ".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                return queryResult;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }
        /// <summary>
        /// Get wrong seal report by discharge vessel
        /// </summary>
        /// <param name="tfcCodeE"></param>
        /// <param name="psIssueBy"></param>
        /// <returns></returns>
        public List<WrongSealReport> GetWrongSealReportByLoadVessel(string tfcCodeE, string psIssueBy)
        {

            try
            {
                string lsSealBy;

                if (tfcCodeE.IsEmpty())
                {
                    return new List<WrongSealReport>();
                }

                string lsTfcCodeI = " v.TFC_CODE_E = '" + tfcCodeE.TrimEx() + "' ";


                if (psIssueBy.TrimEx() != "")
                    lsSealBy = "AND its.SEAL_TYPE = '" + psIssueBy.TrimEx() + "' ";
                else
                    lsSealBy = "";

                string lsQuery = $@"
                                
                        SELECT DISTINCT i.LINE_OPER, v.VES_ID , v.OUT_VOYAGE VOYAGE, v.VES_NAME, v.ACT_DEP_TS , v.ACT_BERTH_TS , 
                               i.ITEM_NO , i.LENGTH, its.SEAL_TYPE , its.SEAL_NO AS INVALID_SEAL, itr.SEAL_NO CURRENT_SEAL, i.ITEM_KEY, its.REMARKS
                         FROM ITEM i
                            INNER JOIN ITEM_SEAL its ON its.ITEM_KEY = i.ITEM_KEY {lsSealBy}  AND its.INVALID_SEAL = 'Y' 
	                            LEFT OUTER JOIN ITEM_SEAL itr ON itr.ITEM_KEY = i.ITEM_KEY AND itr.SEAL_TYPE = 'LOP'  AND itr.INVALID_SEAL <> 'Y' 
                            INNER JOIN VESSEL_DETAILS v ON {lsTfcCodeI}
                         WHERE i.DEP_CAR = v.TFC_CODE_E
   
                         ORDER BY its.SEAL_TYPE ".TrimEx();

                var queryResult = OraDatabase.ExecuteSql(lsQuery);

                var result = (from d in queryResult.AsEnumerable()
                              select new WrongSealReport(d)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        /// <summary>
        /// Get all containers in discharge_list and sub details
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public List<ViewDischargeList> GetAllDischargeListAndSubdetails(string tfcCodeI)
        {
            var sql = $@"
                            SELECT 
                                DL.VES_ID, DL.tfc_code, DL.VES_NAME, 
                                DL.item_no As Ctnr_No, 
                                DL.CATEGORY, 
                                DL.ORIG_ISO, DL.ISO, DL.FEL, DL.GRADE, DL.Weight, DL.AGENT, 
                                DL.SLOT_CODE, DL.LINE_OPER As Liner, DL.crt_ts, DL.upd_ts,
                                DL.PLACE_OF_DELIVERY, DL.PLACE_OF_RECEIPT, DL.BILL_OF_LADING, Dsl.Seal_No As Seal_No, 
                                DL.CONSIGNEE, DL.PTI, Dgs.DGS_CLASS, Dgs.UN_NO, 
                                Oog.OOG_TOP, Oog.OOG_RIGHT, Oog.OOG_LEFT, Oog.OOG_BACK, Oog.OOG_FRONT, Oog.OOG_LENGTH, Oog.OOG_HEIGHT, Oog.OOG_WIDTH, 
                                Rfr.SETTING_TEMP As RFR_TRANS_TEMP, 
                                DL.EXIT_TFC_CODE, DL.EXIT_VESSEL_NAME, DL.LL_POD, 
                                DL.manifest_key, DL.item_key, DL.hist_flg 
                            FROM Discharge_List DL 
                                LEFT OUTER JOIN Disch_Oog Oog ON Oog.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN Disch_Dangerous Dgs ON Dgs.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN DISCH_REEFER Rfr ON Rfr.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN DISCH_SEAL Dsl ON Dsl.Manifest_Key = DL.Manifest_Key
                            WHERE DL.TFC_CODE = '{tfcCodeI}' 
                                AND ((Dgs.UPD_TS is null) or 
                                    (Dgs.UPD_TS In (select MAX(UPD_TS) From Disch_Dangerous Where Manifest_Key= DL.Manifest_Key )))
                                AND ((Dsl.UPD_TS is null) or 
                                    (Dsl.UPD_TS In (select MAX(UPD_TS) From DISCH_SEAL Where Manifest_Key= DL.Manifest_Key )))
                            ORDER BY DL.item_no  ".
                Trim();


            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = (from d in queryResult.AsEnumerable()
                          select new ViewDischargeList(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get all containers in discharge_list and sub details
        /// </summary>
        /// <param name="tfcCodeI"></param>
        /// <returns></returns>
        public DataTable GetDataSourceDischargeList(string tfcCodeI)
        {
            var sql = $@"
                            SELECT 
                                DL.VES_ID, DL.tfc_code, DL.VES_NAME, 
                                DL.item_no As Ctnr_No, 
                                DL.CATEGORY, 
                                DL.ORIG_ISO, DL.ISO, DL.FEL, DL.GRADE, DL.Weight, DL.AGENT, 
                                DL.SLOT_CODE, DL.LINE_OPER As Liner, DL.crt_ts, DL.upd_ts,
                                DL.PLACE_OF_DELIVERY, DL.PLACE_OF_RECEIPT, DL.BILL_OF_LADING, Dsl.Seal_No As Seal_No, 
                                DL.CONSIGNEE, DL.PTI, Dgs.DGS_CLASS, Dgs.UN_NO, 
                                Oog.OOG_TOP, Oog.OOG_RIGHT, Oog.OOG_LEFT, Oog.OOG_BACK, Oog.OOG_FRONT, Oog.OOG_LENGTH, Oog.OOG_HEIGHT, Oog.OOG_WIDTH, 
                                Rfr.SETTING_TEMP As RFR_TRANS_TEMP, 
                                DL.EXIT_TFC_CODE, DL.EXIT_VESSEL_NAME, DL.LL_POD, 
                                DL.manifest_key, DL.item_key, DL.hist_flg 
                            FROM Discharge_List DL 
                                LEFT OUTER JOIN Disch_Oog Oog ON Oog.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN Disch_Dangerous Dgs ON Dgs.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN DISCH_REEFER Rfr ON Rfr.Manifest_Key = DL.Manifest_Key 
                                LEFT OUTER JOIN DISCH_SEAL Dsl ON Dsl.Manifest_Key = DL.Manifest_Key
                            WHERE DL.TFC_CODE = '{tfcCodeI}' 
                                AND ((Dgs.UPD_TS is null) or 
                                    (Dgs.UPD_TS In (select MAX(UPD_TS) From Disch_Dangerous Where Manifest_Key= DL.Manifest_Key )))
                                AND ((Dsl.UPD_TS is null) or 
                                    (Dsl.UPD_TS In (select MAX(UPD_TS) From DISCH_SEAL Where Manifest_Key= DL.Manifest_Key )))
                            ORDER BY DL.item_no  ".
                Trim();


            return OraDatabase.ExecuteSql(sql);

        }

        /// <summary>
        /// Get max comment seq of the container specified by the given manifest key
        /// </summary>
        /// <param name="manifestKey"></param>
        public int GetMaxDischCommentSeq(int manifestKey)
        {
            var sql = $@"
                            SELECT max(COMMENT_SEQ) FROM DISCHARGE_LIST_COMMENTS WHERE MANIFEST_KEY = '{manifestKey}'
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// Get manifest key of the given container on the given vessel
        /// </summary>
        /// <param name="ctnrNo"></param>
        /// <param name="tfcCode"></param>
        /// <param name="history">
        ///     - Yes: Indicates the given vessel is in history
        ///     - No: Indicates the given vessel is active in system
        ///     - null or else: no check history flag
        /// </param>
        /// <returns></returns>
        public int GetDischargeManifestKey(string ctnrNo, string tfcCode, BooleanType history)
        {
            string historyCondition = string.Empty;

            if (history == BooleanType.Yes)
            {
                historyCondition = @" AND HIST_FLG = 'Y' ";
            }
            else if (history == BooleanType.No || history == BooleanType.Blank)
            {
                historyCondition = @" AND (HIST_FLG = ' ' OR  HIST_FLG = 'N') ";
            }

            var sql = $@"
                            SELECT MANIFEST_KEY FROM DISCHARGE_LIST WHERE TFC_CODE = '{tfcCode}' AND ITEM_NO = '{ctnrNo}' {historyCondition}
                        ";

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        #endregion

        #region BAPLIE Methods

        /// <summary>
        /// get list baplie attached details by given baplieKey
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieAttachedDetails> GetBaplieAttachedDetails(int baplieKey)
        {
            var sql = $@"
                        SELECT BAPLIE_KEY, ITEM_ID, ATT_ITEM_KEY, EQUIPMENT_TYPE, UPD_TS, BB_ID, BB_HEIGHT, BB_LENGHT, BB_WIDTH, BB_QTY, BB_TYPE
                        FROM BAPLIE_ATTACHED_DETAILS 
                        WHERE BAPLIE_KEY = {baplieKey}";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieAttachedDetails(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get list bundle baplie container by given baplie key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieBreakbulk> GetBaplieBreakbulk(int baplieKey)
        {
            var sql = $@"
                        SELECT BAPLIE_KEY, BB_ID, BB_HEIGHT, BB_LENGTH, BB_NUM, BB_TYPE, BB_WIDTH, ET_STOWLOC_1, ET_STOWLOC_2, ET_STOWLOC_3, ET_STOWLOC_4, ET_STOWLOC_5, ET_STOWLOC_6, ET_STOWLOC_7, ET_STOWLOC_8, ET_STOWLOC_9, UPD_TS
                        FROM BAPLIE_BREAKBULK
                        WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieBreakbulk(d)).ToList();

            return result;
        }

        /// <summary>
        /// get list baplie damage details by given baplieKey
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieDamage> GetBaplieDamage(int baplieKey)
        {
            var sql = $@"
                        SELECT BAPLIE_KEY, DAMAGE_CD, DAMAGE_DESC, DAMAGE_LOC, DAMAGE_SEQ, UPD_TS
                        FROM BAPLIE_DAMAGE
                        WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieDamage(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get baplie comments by given baplie key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieComments> GetBaplieComments(int baplieKey)
        {
            var sql =
                $@"SELECT BAPLIE_KEY, COMMENT_SEQ, COMMENT_CD, COMMENTS FROM BAPLIE_COMMENTS WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieComments(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get baplie sela by given baplie key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieSeal> GetBaplieSeal(int baplieKey)
        {
            var sql = $@"
                                    SELECT BAPLIE_KEY, SEAL_TYPE, SEAL_NO, SEAL_DATE, SEAL_SEQ, CURRENT_FLG
                                    FROM BAPLIE_SEAL WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieSeal(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get baplie Dangerous by given baplie Key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public List<BaplieDangerous> GetBaplieDangerous(int baplieKey)
        {
            var sql = $@"
                                   SELECT BAPLIE_KEY, DGS_SEQ, DGS_CLASS, UN_NO
                                   FROM BAPLIE_DANGEROUS
                                   WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieDangerous(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get baplie reefer detail by given baplie key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public BaplieReefer GetBaplieReefer(int baplieKey)
        {
            var sql = $@"
                                   SELECT BAPLIE_KEY, SETTING_TEMP, MAX_TEMP, MIN_TEMP, TEMP_UNIT
                                   FROM BAPLIE_REEFER
                                   WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieReefer(d));

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;

        }


        /// <summary>
        /// Get baplie oog detail by given baplie key
        /// </summary>
        /// <param name="baplieKey"></param>
        /// <returns></returns>
        public BaplieOog GetBaplieOog(int baplieKey)
        {
            var sql = $@"
                                   SELECT BAPLIE_KEY, OOG_BACK, OOG_FRONT, OOG_HEIGHT,OOG_LEFT, OOG_LENGTH, OOG_RIGHT, OOG_TOP, OOG_WIDTH, CRT_TS,	LENUNITS, BB_NUM , BB_TYPE 
                                   FROM BAPLIE_OOG
                                   WHERE BAPLIE_KEY = {baplieKey} ".Trim();

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new BaplieOog(d));

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }


        //Baplie
        /// <summary>
        /// Get specific container data in baplie table, by given container number and vessel topx Id
        /// </summary>
        /// <param name="vesId"></param>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public Baplie GetBaplieByVesselAndCtrNo(string vesId, string itemNo)
        {
            var sql = $@"
                                    SELECT *  
                                    FROM BAPLIE WHERE VES_ID = '{vesId}' AND CTR_NO = '{itemNo}'";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new Baplie(dr);

            if (result.Any())
                return result.First();
            return null;
        }

        /// <summary>
        /// Get current container data in baplie table by given baplie key
        /// </summary>
        /// <returns></returns>
        public Baplie GetBaplieByBaplieKey(int baplieKey)
        {
            //if (baplieKey < 0) throw new Exception($"Baplie Key {baplieKey} không hợp lệ");

            var sql = $@"
                                    SELECT * 
                                    FROM BAPLIE WHERE BAPLIE_KEY = {baplieKey} ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new Baplie(dr);

            if (result.Any())
                return result.First();
            return null;
        }

        /// <summary>
        /// Get current container data in baplie table by given container
        /// </summary>
        /// <param name="itemNo"></param>
        /// <returns></returns>
        public Baplie GetCurrentBaplieByCtrNo(string itemNo)
        {
            var sql = $@"
                                    SELECT *
                                    FROM BAPLIE WHERE CTR_NO = '{itemNo}' AND (HIST_FLG = 'N' OR HIST_FLG = ' ') ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new Baplie(dr);

            if (result.Any())
                return result.First();
            return null;
        }

        public List<Baplie> GetExistBaplieByVesselToCheck(string vesId)
        {
            var sql = $@"SELECT CTR_NO, BAPLIE_KEY, HIST_FLG FROM BAPLIE WHERE VES_ID = '{vesId}' ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new Baplie(dr);

            return result.ToList();
        }

        /// <summary>
        /// Get container in baplie table by vessel TopX Id
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public List<Baplie> GetBaplieByVessel(string vesId)
        {
            var sql = $@"
                                SELECT SITE_ID, PROCESS_FLAG, DISCHARGE_VOYAGE_NO_MC, LOAD_VOYAGE_NO_MC, VESSEL_NAME, CTR_NO, ISO_CODE, SHIPPING_LINE_CODE, SLOT, FULL_EMPTY, 
                                    CATEGORY, TEMP_SETTING, WEIGHT_GROSS, WEIGHT_TARE, BB_FLAG, STOWLOC, STOWAGE_INSTRUCTION_TEXT, POL_CODE, POD_CODE, FPD_CODE,
                                    REMARK, HIST_FLG, CRT_TS, UPD_TS, BB_ID_NUMMER, VES_ID, TERMINAL_ID, SPEC_HDL_CODE, BAPLIE_KEY, ITEM_KEY, CTR_LENGTH, CTR_HEIGHT, CTR_TYPE,
                                    EXIT_VOYAGE_NO, EXIT_VESSEL_CALL_SIGN, ORIG_ISO, LL_POD, TFC_CODE_I, TFC_CODE_E, CREATED_BY
                                FROM BAPLIE WHERE VES_ID = '{vesId}' ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new Baplie(dr);

            return result.ToList();
        }

        //Baplie Misc sub
        /// <summary>
        /// Get baplie information include sub detail data
        /// </summary>
        /// <param name="vesId"></param>
        /// <returns></returns>
        public List<ViewBaplie> GetBaplieDetailByVessel(string vesId)
        {
            var sql = $@"
                        SELECT B.*, D.MANIFEST_KEY, GetHazUnNo(D.MANIFEST_KEY, 'DISCHARGE_LIST') AS DISCH_HAZ_UNNO FROM VIEW_BAPLIE B
                        LEFT JOIN DISCHARGE_LIST D
                        ON B.CTR_NO = D.ITEM_NO AND D.VES_ID = '{vesId}'
                        WHERE B.VES_ID = '{vesId}' ".Trim();

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from dr in queryResult.AsEnumerable()
                         select new ViewBaplie(dr);

            return result.ToList();
        }

        #endregion

        #region BOOKINGS

        /// <summary>
        /// Check if booking note is multi container
        /// </summary>
        /// <param name="bookNo"></param>
        /// <param name="lineOper"></param>
        /// <returns></returns>
        public int CountBookings(string bookNo, string lineOper)
        {
            var whereLineOper = lineOper.Trim().Length > 0 ? $" AND LINE_OPER = '{lineOper}'" : "";
            var sql = $@"
                    SELECT count(BOOK_KEY) FROM BOOKINGS
                    WHERE BOOK_NO = '{bookNo}' {whereLineOper} ".TrimEx();

            var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();

            return count;
        }

        /// <summary>
        /// Get list bookings by Book note
        /// </summary>
        /// <param name="bookNo"></param>
        /// <param name="lineOper"></param>
        /// <returns></returns>
        public List<Bookings> GetListBookings(string bookNo, string lineOper)
        {
            var whereLineOper = lineOper.Trim().Length > 0 ? $" AND LINE_OPER = '{lineOper}'" : "";
            var sql = $@"
                    SELECT * FROM BOOKINGS
                    WHERE BOOK_NO = '{bookNo}' {whereLineOper} ".TrimEx();

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = (from d in queryResult.AsEnumerable()
                          select new Bookings(d)).ToList();

            return result;
        }

        /// <summary>
        /// Get bookings details by book key
        /// </summary>
        /// <param name="iBookKey"></param>
        /// <returns></returns>
        public Bookings GetBookingsDetails(int iBookKey)
        {
            var sql = $@"SELECT TFC_CODE, POD FROM BOOKINGS WHERE BOOK_KEY = {iBookKey}";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = (from d in queryResult.AsEnumerable()
                          select new Bookings(d));

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        /// <summary>
        /// Check if empty bookings assigned is avaiable by exit vessel
        /// </summary>
        /// <returns></returns>
        public bool CheckMtyBookingsAssignedAvailable(string tfcCodeE, string pod)
        {
            var sql = $@" Select 1 from mty_bookings_assigned 
                                        where carrier_type = 'V' and carrier_code = '{tfcCodeE}' and destination = '{pod}' and item_no = ' ' and qty_released > 0 
                                    ".TrimEx();
            return OraDatabase.ExecuteSql(sql) != null;
        }

        #endregion

        #region DISCREPANCIES Methods

        public List<DiscDangerous> GetDiscDangerous(int itemKey)
        {
            List<DiscDangerous> lstDangerous = new List<DiscDangerous>();
            string sql = $@"
                                        SELECT SITE_ID, ITEM_KEY, DGS_CLASS, TRADE_NAME, UN_NO, PKG_GROUP, NETT_WT, NO_PACKS, PKG_TYPE, GROSS_WT, PKG_TYPE_CODE, PKG_TYPE_TEXT, 
                                            TEMP_FLASHPOINT, TEMP_UNIT, CATEGORY, ACTIVITY, ACTIVITY_UNIT, IMDG_PAGE, IMDG_SHEET_NO, QTY_LIMIT, UPD_TS, LABEL_1, LABEL_2, LABEL_3, 
                                            EMS_NO_1, EMS_NO_2, MFAG_NO_1, MFAG_NO_2, MP_CLASS, MP_LABEL, DGS_SEQ, NAG, CSI, STK_NON_HAZ_CTR, UPD_CNT
                                        FROM DISC_DANGEROUS
                                        WHERE ITEM_KEY = {itemKey}                                              

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DiscDangerous(record);

            if (result.Any())
            {
                lstDangerous = result.ToList();
            }

            return lstDangerous;
        }

        public List<DiscComments> GetDiscComments(int itemKey)
        {
            List<DiscComments> lstComments = new List<DiscComments>();
            string sql = $@"
                                        SELECT ITEM_KEY, COMMENT_CD, COMMENTS, UPD_TS, INTERNAL_USE, COMMENT_SEQ, UPD_CNT
                                        FROM DISC_COMMENTS
                                        WHERE ITEM_KEY = {itemKey}                                              

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DiscComments(record);

            if (result.Any())
            {
                lstComments = result.ToList();
            }

            return lstComments;
        }

        public DiscOog GetDiscOog(int itemKey)
        {
            string sql = $@"
                                        SELECT ITEM_KEY, OOG_BACK, OOG_FRONT, OOG_HEIGHT, OOG_LEFT, OOG_LENGTH, OOG_RIGHT, OOG_TOP, OOG_WIDTH, UPD_TS, LENUNITS, BB_NUM, BB_TYPE, UPD_CNT
                                        FROM DISC_OOG
                                        WHERE ITEM_KEY = {itemKey}                                              

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DiscOog(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        public DiscReefer GetDiscReefer(int itemKey)
        {
            string sql = $@"
                                        SELECT ITEM_KEY, FLASHPOINT_TEMP, FLASHPOINT_TYPE, FROZEN_TEMP, RFR_TRANS_TEMP, RFR_TYPE, UPD_TS, FROZEN_TYPE, INGATE_TEMP, IG_TEMP_TYPE, 
                                            OUTGATE_TEMP, OG_TEMP_TYPE, HUMIDITY, VENTILATION, O2, CO2, MIN_TEMP, MAX_TEMP, IS_CONNECTED, CONAIR, TEMP_UNIT, UPD_CNT
                                        FROM DISC_REEFER
                                        WHERE ITEM_KEY = {itemKey}                                              

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DiscReefer(record);

            if (result.Any())
            {
                return result.FirstOrDefault();
            }
            return null;
        }

        public List<DiscDamage> GetDiscDamage(int itemKey)
        {
            List<DiscDamage> reefer = new List<DiscDamage>();
            string sql = $@"
                                        SELECT ITEM_KEY, DAMAGE_CD, DAMAGE_SEQ, DAMAGE_DESC, MAN_HOUR_REPAIRS, FIXEDUP_TS, FIXEDUP_OPER, CRT_TS, CHARGE_TO, INSPECT_BY, DAMAGE_LOC, UPD_CNT
                                        FROM DISC_DAMAGE
                                        WHERE ITEM_KEY = {itemKey}                                              

                            ";

            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new DiscDamage(record);

            if (result.Any())
            {
                reefer = result.ToList();
            }

            return reefer;
        }

        #endregion

        #region Miscellany

        /// <summary>
        /// Check if container status in yard and in load list miss match ?
        /// true: if difference
        /// false: same status
        /// </summary>
        /// <param name="containerNo"></param>
        /// <returns></returns>
        public bool IsMatchedStatusInEipaAndItem(string containerNo)
        {
            var sqlEdi =
                $@"SELECT FEL FROM EDI_ITEM_PRE_ADVICE WHERE ITEM_NO = '{containerNo}' AND (HIST_FLG = 'N' OR HIST_FLG = ' ') ";

            var sqlItem =
                $@"SELECT FEL FROM ITEM WHERE ITEM_NO = '{containerNo}' AND (HIST_FLG = 'N' OR HIST_FLG = ' ') ";

            var inYardStatus = OraDatabase.ExecuteScalar(sqlItem).TrimEx();
            var inEdiStatus = OraDatabase.ExecuteScalar(sqlEdi).TrimEx();

            if (inYardStatus != string.Empty && inEdiStatus != string.Empty && inYardStatus != inEdiStatus)
            {
                return false;
            }

            return true;
        }


        /// <summary>
        /// Check to see the given container has existed in EIPA or in custom clearance. If it's found, then return it's item key. Else, return 0
        /// </summary>
        /// <returns>
        ///     - item key: if it's found in EIPA or Custom Clearance
        ///     - 0: if it's not found
        /// </returns>
        public int CheckExistItemKeyInEipaOrCustClearance(string ctnrNo, string fel)
        {
            var sql = string.Format(@"
                            SELECT ITEM_KEY FROM EDI_ITEM_PRE_ADVICE
                            WHERE ITEM_NO = '{0}' AND FEL = '{1}' AND HIST_FLG <> 'Y'
                            UNION
                            SELECT ITEM_KEY FROM CUSTOM_CLEARANCE 
                            WHERE ITEM_NO = '{0}' AND FEL = '{1}' AND HIST_FLG <> 'Y'
                        ", ctnrNo, fel);

            var sqlResult = OraDatabase.ExecuteScalar(sql);
            return sqlResult != null ? sqlResult.CheckIntEx() : 0;
        }

        /// <summary>
        /// Fix data in Item and Vessel detail after process transfer data from CMS to TOPOVN
        /// </summary>
        public void FixMirrageDataFromCMS()
        {
            try
            {
                OraDatabase.ExecuteSpNonQuery("SP_FIX_MIRRAGE_CMS_TOPO");
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
        }


        #endregion

        #region ENQUIRY INBOUND DATA

        public DataTable GetFileSeqImportDischargeList(string tfcCode)
        {
            var sql = $@"
                        SELECT e.FILE_SEQ, min(CRT_TS) IMPORT_TS
                        FROM   VESSEL_DETAILS v
	                        INNER JOIN DISCHARGE_LIST e ON e.TFC_CODE = v.TFC_CODE_I
                        WHERE v.TFC_CODE_I = '{tfcCode}'
                        GROUP BY e.FILE_SEQ
                        ORDER BY IMPORT_TS
                        ";

            return OraDatabase.ExecuteSql(sql);
        }

        public ViewDischargeList GetCurrentViewDischargeList(string itemNo)
        {
            string historyConditon = @" AND (HIST_FLG = ' ' OR HIST_FLG = 'N')";

            string sql = $@" Select * FROM VIEW_DISCHARGE_LIST 
                                   WHERE ITEM_NO = '{itemNo}' {historyConditon}
                            ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from record in queryResult.AsEnumerable()
                         select new ViewDischargeList(record);

            return result.FirstOrDefault();
        }

        /// <summary>
        /// Enquiry container tobe discharge which currently in vessel
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewDischargeList> GetDischargeList_EPEReport(string tfcCode)
        {
            try
            {
                var sql = $@"
                    SELECT 
                    CASE 
                        WHEN V.HIST_FLG <> 'Y' Then GetHazUnNo (V.MANIFEST_KEY, 'DISCHARGE_LIST')
                        ELSE GetHazUnNo (V.ITEM_KEY, 'ITEM')
                      END AS HAZ_UNNO
                    , V.*
                    FROM VIEW_DISCHARGE_LIST V
                    WHERE V.TFC_CODE = '{tfcCode}' ";

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select new ViewDischargeList(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewDischargeList>();

        }

        /// <summary>
        /// Enquiry container tobe discharge which currently in vessel
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewDischargeList> GetContainerDischargeCurrentInVessel(string tfcCode)
        {
            try
            {
                var sql = $@"
                    SELECT d.*
                        FROM VIEW_DISCHARGE_LIST d
    	                    LEFT OUTER JOIN ITEM i ON i.ITEM_KEY = d.ITEM_KEY AND extract(YEAR FROM i.ARR_TS) > 1900
                    WHERE d.TFC_CODE = '{tfcCode}' AND i.ITEM_KEY IS NULL ";

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select new ViewDischargeList(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewDischargeList>();

        }

        /// <summary>
        /// Get containers arrival by vessel and been discharged to yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerDischargeCurrentInYard(string tfcCode)
        {
            try
            {
                var sql = $@"
                    SELECT i.*, 
                        nvl(t.CHI_CUC_HQ, c1.CHICUCHQ) SUB_CUST_CODE, 
                    	nvl(c.TENCHICUCHQ, c1.TENCHICUCHQ) SUB_CUST_DEP, 
                    	nvl(s.DESCR, s1.DESCR) CUST_DEP
                    FROM VIEW_ITEM i
                        LEFT JOIN THU_TUC_HAI_QUAN t ON i.ITEM_KEY = t.ITEM_KEY
                        LEFT JOIN CHICUCHQ c ON t.CHI_CUC_HQ = c.CHICUCHQ
                        LEFT JOIN SYS_CODES s ON c.CUCHQ = s.CODE_REF AND s.CODE_TP = 'CUSTOMSDEP'
                        
                        LEFT JOIN CUSTOM_CLEARANCE cc ON i.ITEM_KEY = cc.ITEM_KEY
                        LEFT JOIN CHICUCHQ c1 ON cc.SUB_CUSTOM_DEP = c1.CHICUCHQ AND cc.CUSTOM_DEP = c1.CUCHQ
                        LEFT JOIN SYS_CODES s1 ON c1.CUCHQ = s1.CODE_REF AND s1.CODE_TP = 'CUSTOMSDEP'
                    WHERE ARR_BY = 'V' AND ARR_CAR = '{tfcCode}' AND extract(YEAR FROM i.ARR_TS) > 1900 ";

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select new ViewItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewItem>();
        }

        /// <summary>
        /// Get container stay on board, wich will be restowed
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewBaplie> GetContainerStayonboardCurrentInVessel(string tfcCode)
        {
            try
            {
                var sql = $@"
                    SELECT b.* 
                    FROM VIEW_BAPLIE b
	                    LEFT JOIN DISCHARGE_LIST d ON d.VES_ID = b.VES_ID AND d.ITEM_NO = b.CTR_NO 
                    WHERE b.TFC_CODE_I = '{tfcCode}' AND b.POD_CODE <> '{GlobalSettings.HomePort}' AND (b.ARR_TS IS NULL OR extract(Year from b.ARR_TS) = 1900) AND d.MANIFEST_KEY IS NULL ";

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select new ViewBaplie(d)).ToList();
            }

            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewBaplie>();
        }


        /// <summary>
        /// Get container stay on board, wich has been discharged to yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerStayonboardDischarged(string tfcCode)
        {
            try
            {
                var sql = $@"
                    SELECT i.* 
                    FROM VIEW_ITEM i
                    WHERE i.ARR_BY = 'V' AND i.ARR_CAR = '{tfcCode}' AND (i.CATEGORY = 'R' OR i.CATEGORY = 'S') ";

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select new ViewItem(d)).ToList();
            }

            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewItem>();
        }

        #endregion

        #region ENQUIRY OUTBOUND DATA

        public DataTable GetFileSeqImportLoadList(string tfcCode)
        {
            var sql = $@"
                        SELECT e.FILE_SEQ, min(CRT_TS) IMPORT_TS, 
	                        CASE WHEN extract(YEAR FROM v.RECV_CTR_CUTOFF_TS) = 1900 THEN v.EST_BERTH_TS ELSE v.RECV_CTR_CUTOFF_TS END CLOSING_TIME,
	                        CASE WHEN v.RECV_CTR_CUTOFF_TS  < min(CRT_TS) THEN 'Y' ELSE 'N' END AFTER_CST
                        FROM   VESSEL_DETAILS v
	                        INNER JOIN EDI_ITEM_PRE_ADVICE e ON e.TFC_CODE = v.TFC_CODE_E
                        WHERE v.TFC_CODE_E = '{tfcCode}'	
                        GROUP BY e.FILE_SEQ, v.RECV_CTR_CUTOFF_TS, v.EST_BERTH_TS
                        ORDER BY e.FILE_SEQ, CLOSING_TIME
                        ";

            return OraDatabase.ExecuteSql(sql);
        }

        /// <summary>
        /// Enquiry container to be loaded which in community
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewEipaItem> GetContainerLoadListCommunity(string tfcCode)
        {
            try
            {
                var sql = $@"
                        SELECT e.*
                        FROM VIEW_EIPA_ITEM e
    	                    LEFT OUTER JOIN ITEM i ON i.ITEM_KEY = e.ITEM_KEY AND extract(year from i.ARR_TS) > 1900 
                        WHERE e.TFC_CODE = '{tfcCode}' AND i.ITEM_KEY IS NULL ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new ViewEipaItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        ///     Get containers departure by vessel and current in yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewEipaItem> GetEipaLoadListCurrentInYard(string tfcCode)
        {
            try
            {
                var sql = $@"
                            SELECT GetHazUnNo(i.ITEM_KEY, 'ITEM_PRE_ADVICE') AS LOAD_LIST_HAZ_UNNO
                                    , GetHazUnNo(i.ITEM_KEY, 'ITEM') AS ITEM_HAZ_UNNO
                                    , i.*
                            FROM VIEW_EIPA_ITEM e
                                INNER JOIN  ITEM i ON i.ITEM_KEY = e.ITEM_KEY 
                            WHERE e.TFC_CODE = '{tfcCode}' AND extract(year from i.ARR_TS) > 1900 
                            ORDER BY i.BOOK_NO, i.ISO, i.FEL";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new ViewEipaItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get containers departure by vessel and current in yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerLoadListCurrentInYard(string tfcCode)
        {
            try
            {
                //KHOATT: Theo yêu cầu của user TCI https://pms.snp.com.vn/browse/SDP-720
                //Thực hiện chỉnh sửa cho TCI để không ảnh hưởng đến các site khác
                //Đối với cont đã tồn bãi thì vẫn lấy thông tin nhiệt độ trong list xuất 
                //Cột SettingTemp = RfTemp 
                //Thay  i.SETTING_TEMP = e.RF_TEMP,CASE WHEN i.TEMP_UNIT = ' ' THEN NVL(irf.TEMP_UNIT,'C') ELSE i.TEMP_UNIT END TEMP_UNIT
                var settingTemp = GlobalSettings.SiteId != Sites.TCI.ToString() ?
                     "i.SETTING_TEMP" :
                     "e.RF_TEMP AS SETTING_TEMP";

                //CHỉnh sửa bổ sung cột i.FROZEN_TEMP
                //Cont tồn bãi mà nhiệt độ thực tế chưa có thì nhiệt độ current temp lấy settingtemp
                //Nếu có nhiệt độ thực tế, do bãi lạnh nhập thì lấy nhiệt độ đó
                var frozenTemp = GlobalSettings.SiteId != Sites.TCI.ToString() ?
                    "i.FROZEN_TEMP" :
                    "decode(i.FROZEN_TEMP," +
                    "' ',decode(i.SETTING_TEMP,NULL, ' ', to_char(i.SETTING_TEMP))," +
                    " NULL,decode(i.SETTING_TEMP,NULL, ' ', to_char(i.SETTING_TEMP))," +
                    " to_char(i.FROZEN_TEMP)) " +
                    "FROZEN_TEMP";

                var sql = $@"
                        SELECT i.SITE_ID, i.ITEM_KEY, nvl(trim(i.AGENT), e.AGENT) AGENT , decode(trim(i.LINE_OPER),NULL, e.LINER_CODE, i.LINE_OPER) LINE_OPER , e.SLOT SLOT_CODE, i.PEB_CLR_YN, i.PEB_EXP_NO, i.ITEM_NO, i.LENGTH, i.HEIGHT, i.ITEM_TYPE, i.ITEM_SIZE, i.COPRAR_LOAD, 
	                        i.GRADE, i.ARR_BY, i.ARR_CAR, i.ARR_TS, i.TURNING_CHE, i.DEP_BY, i.DEP_CAR, i.DEP_TS, i.CATEGORY, i.FEL, i.GROSS, i.TARE, 
                            i.LL_VGM, i.LL_VGM_AUTHORIZED, i.CUST_VGM, i.CUST_VGM_AUTHORIZED,	                        
                            i.CGO_GROSS_WT, i.HIST_FLG, i.CHARGES_TO_DT, i.FREE_DAYS, i.ORIG_ISO, i.ISO, i.EXPIRY_DATE, i.DISCH_PORT, i.FDISCH_PORT, i.LL_DISCH_PORT, i.BILL_OF_LADING, 
	                        i.BOOK_NO, i.EIR_ID, i.PLACE_OF_DELIVERY, i.PLACE_OF_RECEIPT, i.SEAL_NO, i.WHO_PAYS, i.DOMESTIC, i.ORG_ARR_CAR, i.ORG_IN_VOYAGE, i.EXIT_VES_CD, 
	                        i.EXIT_VOYAGE, i.VES_ID, i.CNT_ATTACH, i.CNT_BB, i.IS_REEFER, i.IS_OOG, i.SPECIAL_OOG, i.IS_OV_WEIGHT, i.IS_DANGEROUS, i.IS_FL, 
	                        i.BILL_TYPE, e.HAZ_CLASS, e.UN_NO, i.HAZ_UNNO, i.NO_PLUGIN_REQUIRED,{frozenTemp},{settingTemp}, i.TEMP_UNIT, i.VENT_RATE, i.VENT_UNIT, 
	                        i.OOG_WIDTH, i.OOG_HEIGHT, i.OOG_LENGTH, i.OOG_BACK, i.OOG_FRONT, i.OOG_LEFT, i.OOG_RIGHT, i.OOG_TOP, 
	                        i.STK_CLASS, i.STK_REF, i.STACK, i.X, i.Y, i.Z, i.IS_STOP, RTRIM(GET_ITEM_REMARKS(i.ITEM_KEY)) || '.' || Get_Item_Damage_String(i.ITEM_KEY, 'N','Y') COMMENTS, i.SPHD_CODE, i.ECN_NO, i.ICN_NO, i.LOCATION, i.PTI_DATE, i.PTI_COMPLETE_DATE, 
	                        i.VENDOR, i.PTI_METHOD, i.REMARKS, 
                            nvl(t.CHI_CUC_HQ, c1.CHICUCHQ) SUB_CUST_CODE, 
                    	    nvl(c.TENCHICUCHQ, c1.TENCHICUCHQ) SUB_CUST_DEP, 
                    	    nvl(s.DESCR, s1.DESCR) CUST_DEP,
                            i.LOAD_LIST_HAZ_UNNO,
                            CASE WHEN e.Item_key>0
   		                            THEN TOPOVN.GET_ITEM_COMMODITY(i.Item_key)
   	                            ELSE
   		                            ' '
   	                            END as COMMODITY
                        FROM EDI_ITEM_PRE_ADVICE e
                            INNER JOIN  VIEW_ITEM i ON i.ITEM_KEY = e.ITEM_KEY 
                            LEFT JOIN THU_TUC_HAI_QUAN t ON i.ITEM_KEY = t.ITEM_KEY
                            LEFT JOIN CHICUCHQ c ON t.CHI_CUC_HQ = c.CHICUCHQ
                            LEFT JOIN SYS_CODES s ON c.CUCHQ = s.CODE_REF AND s.CODE_TP = 'CUSTOMSDEP'

                            LEFT JOIN CUSTOM_CLEARANCE cc ON i.ITEM_KEY = cc.ITEM_KEY
                            LEFT JOIN CHICUCHQ c1 ON cc.SUB_CUSTOM_DEP = c1.CHICUCHQ AND cc.CUSTOM_DEP = c1.CUCHQ
                            LEFT JOIN SYS_CODES s1 ON c1.CUCHQ = s1.CODE_REF AND s1.CODE_TP = 'CUSTOMSDEP'
                        WHERE e.TFC_CODE = '{tfcCode}' AND extract(year from i.ARR_TS) > 1900 
                        ORDER BY i.BOOK_NO, i.ISO, i.FEL";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new ViewItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get containers current in yard and in load list
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewItem> GetContainerCurrentInYardHasLoadlist(string tfcCode)
        {
            try
            {
                var sql = $@"
                        SELECT i.*
                            FROM EDI_ITEM_PRE_ADVICE e
                                INNER JOIN  VIEW_ITEM i ON i.ITEM_NO = e.ITEM_NO AND (i.HIST_FLG = ' ' OR i.HIST_FLG = 'N')
                            WHERE e.TFC_CODE = '{tfcCode}' 
                            ORDER BY i.BOOK_NO, i.ISO, i.FEL";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new ViewItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get containers in load list has custom clearance
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<CustomClearance> GetContainerLoadListCustomClear(string tfcCode)
        {
            try
            {
                var sql = $@"
                        SELECT cc.*
                            FROM EDI_ITEM_PRE_ADVICE e
                                INNER JOIN CUSTOM_CLEARANCE cc ON cc.ITEM_KEY = e.ITEM_KEY
                            WHERE e.TFC_CODE = '{tfcCode}' AND e.LOAD_LIST_FLG = 'Y' ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new CustomClearance(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;

        }

        /// <summary>
        /// Count total container is custom clearance in C80
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public int CountContainerInCustomClearance(string tfcCode)
        {
            try
            {
                var sql = $@"
                        SELECT count(*)
                            FROM EDI_ITEM_PRE_ADVICE e
                                INNER JOIN CUSTOM_CLEARANCE cc ON cc.ITEM_KEY = e.ITEM_KEY
                            WHERE e.TFC_CODE = '{tfcCode}' AND e.LOAD_LIST_FLG = 'Y' ";

                return OraDatabase.ExecuteScalar(sql).CheckIntEx();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return 0;
        }

        /// <summary>
        /// Get containers departure by vessel and current in yard
        /// </summary>
        /// <param name="tfcCode"></param>
        /// <returns></returns>
        public List<ViewBookingDeliveredItem> GetContainersInBooking(string tfcCode)
        {
            try
            {
                var sql = $@"
                        SELECT DISTINCT i.item_no, ' ' as peb_clr_yn, ' ' as IS_ATTACH,
                            i.iso, i.ORIG_ISO, b.book_no,  i.line_oper, b.ctr_owner, to_char(i.length) as length ,
                            to_char(i.height) as height, 0 as gross, ' ' as peb_exp_no, ' ' as export_confirmed,  ' ' as arr_ts, ' ' as arr_by,
                            ' ' as dep_ts, ' ' as dep_by, b.POD as DischPort, b.FPOD as FPOD, i.item_key, 
                            ' ' as arr_car,b.ITEM_TYPE, ' ' as dgs_min, i.fel, ' ' as is_dangerous, ' ' as is_reefer, ' ' as item_status, 'B' as Ctr_Source,
                            ' ' as category,' ' as coprar_load 
                        FROM bookings b, bkg_ctrs_delivered bc, item i
                        WHERE b.TFC_CODE = '{tfcCode}'
	                        AND b.book_key = bc.book_key 
	                        AND i.item_key = bc.dep_item_key 
	                        AND bc.arr_item_key = 0
                            ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                return (from d in queryResult.AsEnumerable()
                        select new ViewBookingDeliveredItem(d)).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        public List<string> GetContainerFEDiscrepancyLoadListAgaintItem(string tfcCode)
        {
            try
            {
                var sql = string.Format(@"
                                Select i.ITEM_NO 
                                from item i, edi_item_pre_advice eipa  where ( (i.dep_by = 'V' and i.dep_car = '{0}' ) or (eipa.tfc_code = '{0}' ) )
                                    and (eipa.ig_flg = 'N' or eipa.ig_flg = ' ') and (eipa.hist_flg = 'N' or eipa.hist_flg = ' ') 
                                    and i.item_no = eipa.item_no and i.fel != eipa.fel and (i.hist_flg = 'N' or i.hist_flg = ' ') 
                                    and i.ITEM_NO = eipa.ITEM_NO and extract(year from i.Arr_ts) != 1900
                                ", tfcCode);

                var queryresult = OraDatabase.ExecuteSql(sql);
                return (from d in queryresult.AsEnumerable()
                        select d[0].TrimEx()).ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        #endregion

        #region EDI

        /// <summary>
        /// Add data from sub item table to edifact sub table
        /// select data by itemKey and message type
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="msgType"></param>
        /// <returns></returns>
        public bool AddSubItemToSubEdifact(int itemKey, EdiMsgType msgType)
        {
            string[] SQL = new string[7];

            try
            {
                //Item Comments
                SQL[0] = "INSERT INTO EDIFACT_ITEM_COMMENTS (MSG_TYPE, ITEM_KEY, COMMENT_CD, COMMENTS, UPD_TS, INTERNAL_USE, COMMENT_SEQ, UPD_CNT) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, COMMENT_CD, COMMENTS, sysdate, INTERNAL_USE, COMMENT_SEQ, UPD_CNT " +
                        " FROM ITEM_COMMENTS WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item Damage
                SQL[1] = "INSERT INTO EDIFACT_ITEM_DAMAGE (MSG_TYPE, ITEM_KEY, DAMAGE_CD, DAMAGE_DESC, DAMAGE_SEQ, " +
                            " MAN_HOUR_REPAIRS, FIXEDUP_TS, FIXEDUP_OPER, CHARGE_TO, INSPECT_BY, DAMAGE_LOC, UPD_CNT) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, DAMAGE_CD, DAMAGE_DESC, DAMAGE_SEQ, " +
                            " MAN_HOUR_REPAIRS, FIXEDUP_TS, FIXEDUP_OPER, CHARGE_TO, INSPECT_BY, DAMAGE_LOC, UPD_CNT " +
                        " FROM ITEM_DAMAGE WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item Dangerous
                SQL[2] = "INSERT INTO EDIFACT_ITEM_DANGEROUS (MSG_TYPE, SITE_ID, ITEM_KEY, DGS_CLASS, TRADE_NAME, UN_NO, " +
                            " PKG_GROUP, NETT_WT, NO_PACKS, PKG_TYPE, GROSS_WT, PKG_TYPE_CODE, PKG_TYPE_TEXT ) " +
                        " SELECT \'" + msgType + "\', SITE_ID, ITEM_KEY, DGS_CLASS, TRADE_NAME, UN_NO, " +
                            " PKG_GROUP, NETT_WT, NO_PACKS, PKG_TYPE, GROSS_WT, PKG_TYPE_CODE, PKG_TYPE_TEXT " +
                        " FROM ITEM_DANGEROUS WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item OOG
                SQL[3] = "INSERT INTO EDIFACT_ITEM_OOG (MSG_TYPE, ITEM_KEY, OOG_BACK, OOG_FRONT, OOG_HEIGHT, OOG_LEFT, " +
                            " OOG_LENGTH, OOG_RIGHT, OOG_TOP, OOG_WIDTH, UPD_TS, LENUNITS, BB_NUM, BB_TYPE, UPD_CNT) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, OOG_BACK, OOG_FRONT, OOG_HEIGHT, OOG_LEFT, " +
                            " OOG_LENGTH, OOG_RIGHT, OOG_TOP, OOG_WIDTH, sysdate, LENUNITS, BB_NUM, BB_TYPE, UPD_CNT " +
                        " FROM ITEM_OOG WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item Reefer
                SQL[4] = "INSERT INTO EDIFACT_ITEM_REEFER (MSG_TYPE, ITEM_KEY, CHILLED_TEMP, FLASHPOINT_TEMP, FLASHPOINT_TYPE, " +
                            " FROZEN_TEMP, RFR_TRANS_TEMP, RFR_TYPE, UPD_TS, CHILLED_TYPE, FROZEN_TYPE,INGATE_TEMP, IG_TEMP_TYPE, " +
                            " OUTGATE_TEMP, OG_TEMP_TYPE, PRETRIP_INSPECTION, PRE_COOLING, PTIDATE, NUMBER_PROBES, HUMIDITY, VENTILATION, " +
                            " O2, CO2, PLUG_TS, UNPLUG_TS, RFR_CHART_COUNT, MIN_TEMP, MAX_TEMP, IS_CONNECTED, CONAIR, TEMP_UNIT, UPD_CNT ) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, CHILLED_TEMP, FLASHPOINT_TEMP, FLASHPOINT_TYPE, " +
                            " FROZEN_TEMP, RFR_TRANS_TEMP, RFR_TYPE, sysdate, CHILLED_TYPE, FROZEN_TYPE,INGATE_TEMP, IG_TEMP_TYPE, " +
                            " OUTGATE_TEMP, OG_TEMP_TYPE, PRETRIP_INSPECTION, PRE_COOLING, PTIDATE, NUMBER_PROBES, HUMIDITY, VENTILATION, " +
                            " O2, CO2, PLUG_TS, UNPLUG_TS, RFR_CHART_COUNT, MIN_TEMP, MAX_TEMP, IS_CONNECTED, CONAIR, TEMP_UNIT, UPD_CNT  " +
                        " FROM ITEM_REEFER WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item Seal
                SQL[5] = "INSERT INTO EDIFACT_ITEM_SEAL (MSG_TYPE, ITEM_KEY, SEAL_TYPE, SEAL_NO, CRT_TS, SEAL_FEE, WHO_PAYS_TYPE, REMARKS, " +
                            " SITE_ID, SEAL_DATE, SEAL_SEQ, UPD_CNT ) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, SEAL_TYPE, SEAL_NO, sysdate, SEAL_FEE, WHO_PAYS_TYPE, REMARKS, " +
                            " SITE_ID, SEAL_DATE, SEAL_SEQ, UPD_CNT " +
                        " FROM ITEM_SEAL WHERE ITEM_KEY = \'" + itemKey + "\'";

                //Item Subs
                SQL[6] = "INSERT INTO EDIFACT_ITEM_SUBS (MSG_TYPE, ITEM_KEY, ITEM_ID, ISO, UPD_TS, RETURNED, EQUIPMENT_TYPE, GENSET_TYPE, " +
                            " GENSET_NO, ATT_ITEM_KEY, LINE_OPER, SLAVE_POS, DAMAGED_FLG, RESTRICTION_FLG, TARE, TURN_IN_NO, LENGTH, ITEM_TYPE, " +
                            " BB_ID, BB_HEIGHT, BB_LENGTH, BB_QTY, BB_TYPE, BB_WIDTH, ITEM_CLASS, NEW_ATT_KEY, UPD_CNT, SUB_CRT_TS, GROSS, LOADLIST_FLG, CURRENT_FLG ) " +
                        " SELECT \'" + msgType + "\', ITEM_KEY, ITEM_ID, ISO, sysdate, RETURNED, EQUIPMENT_TYPE, GENSET_TYPE, " +
                            " GENSET_NO, ATT_ITEM_KEY, LINE_OPER, SLAVE_POS, DAMAGED_FLG, RESTRICTION_FLG, TARE, TURN_IN_NO, LENGTH, ITEM_TYPE, " +
                            " BB_ID, BB_HEIGHT, BB_LENGTH, BB_QTY, BB_TYPE, BB_WIDTH, ITEM_CLASS, NEW_ATT_KEY, UPD_CNT, SUB_CRT_TS, GROSS, LOADLIST_FLG, CURRENT_FLG " +
                        " FROM ITEM_SUBS WHERE ITEM_KEY = \'" + itemKey + "\'";

                foreach (string sql in SQL)
                {
                    OraDatabase.ExecuteSql(sql);
                }

                return true;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return false;
        }


        /// <summary>
        /// Sync data from table Edipa_Item_Pre_Advice to table Item
        /// </summary>
        /// <param name="itemKey"></param>
        /// <returns></returns>
        public bool SyncDataFromEipaToItem(int itemKey)
        {
            var sql = $@"
                        INSERT INTO ITEM i
                        (i.ITEM_KEY, i.ITEM_NO, i.DEP_BY, i.DEP_CAR, i.LINE_OPER, i.BOOK_NO, i.ITEM_TYPE, i.CATEGORY, i.FEL, i.ISO, i.LENGTH, i.HEIGHT, i.TARE, i.GROSS, i.CGO_GROSS_WT,
	                        i.LL_DISCH_PORT, i.COPRAR_LOAD, i.FDISCH_PORT, i.SITE_ID, i.DOMESTIC, i.ORIG_ISO, i.GRADE, i.SLOT_CODE)
                        SELECT ITEM_KEY, ITEM_NO, 'V', TFC_CODE, LINER_CODE, BOOK_NO, CTR_TYPE, CATEGORY, FEL, ISO, LENGTH, HEIGHT, TARE, GROSS, CARGO_WGT, 
	                        POD, IS_COPRAR_LOAD, FIN_DISCH_PORT, SITE_ID, DOMESTIC_FLG, 
	                        ORIG_ISO, GRADE, SLOT
                        FROM TOPOVN.EDI_ITEM_PRE_ADVICE
                        WHERE ITEM_KEY = {itemKey}
                        ";

            return OraDatabase.ExecuteSingleNonQuery(sql) > 0;
        }

        #endregion EDI

        #region Custom CLearance       

        /// <summary>
        /// Update ITEM.PIB_IMP_NO
        /// </summary>
        /// <param name="itemKey"></param>
        /// <param name="soToKhai"></param>
        public bool UpdateItemPIB_IMP_NO(int itemKey, string soToKhai)
        {
            try
            {
                var sql = $@"                            
                                        UPDATE ITEM
                                        SET 
	                                        PIB_IMP_NO = '{soToKhai}'                                            
                                        WHERE ITEM_KEY = {itemKey} 
                        ";
                OraDatabase.ExecuteNonQuery(sql);
                return true;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return false;
        }

        public CustomClearance GetCurrentCustomClearanceByItemNo(string itemNo, string vesId = "")
        {
            var sql = $@"
                                    SELECT i.ITEM_KEY, i.ITEM_NO, i.ECN_NO, i.CUSTOM_DEP, i.SUB_CUSTOM_DEP, GETSEALNO(i.ITEM_KEY) LINER_SEAL_NO, i.CUSTOM_SEAL_NO, 
                                        i.REMARKS, i.VES_ID, i.OUT_VOYAGE, i.CRT_TS, i.SITE_ID, i.HIST_FLG, i.ISO, i.CUST_REG_NO, i.FEL, i.OPER_NAME, i.GROSS, 
                                        nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, 0)) as VGM,
                                        i.IS_CLOSING_TS, i.REF_TOTAL, i.CUST_OUT_VOYAGE
                                    FROM CUSTOM_CLEARANCE i
                                        LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
	                                    LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = i.ITEM_KEY AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y'
                                    WHERE i.ITEM_NO = '{itemNo}' ";

            if (vesId.IsEmpty())
            {
                sql += string.Format(@" AND (i.HIST_FLG = ' ' OR i.HIST_FLG = 'N') ");
            }
            else
            {
                sql += $@" AND i.VES_ID = '{vesId}' ";
            }

            var queryResult = OraDatabase.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return new CustomClearance(queryResult.Rows[0]);
            }

            return null;

        }

        public CustomClearance GetCurrentCustomClearance(int itemKey)
        {
            var sql =
                $@"SELECT i.ITEM_KEY, i.ITEM_NO, i.ECN_NO, i.CUSTOM_DEP, i.SUB_CUSTOM_DEP, GETSEALNO(i.ITEM_KEY) LINER_SEAL_NO, i.CUSTOM_SEAL_NO, 
                            i.REMARKS, i.VES_ID, i.OUT_VOYAGE, i.CRT_TS, i.SITE_ID, i.HIST_FLG, i.ISO, i.CUST_REG_NO, i.FEL, i.OPER_NAME, i.GROSS, 
                            nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, 0)) as VGM,
                            i.IS_CLOSING_TS, i.REF_TOTAL, i.CUST_OUT_VOYAGE
                        FROM CUSTOM_CLEARANCE i
                            LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
	                        LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = i.ITEM_KEY AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                        WHERE i.ITEM_KEY = {itemKey}";

            var queryResult = OraDatabase.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return new CustomClearance(queryResult.Rows[0]);
            }

            return null;



        }

        public CustomClearance GetCurrentCustomClearance(string itemNo, string fel)
        {
            var sql =
                $@"SELECT i.ITEM_KEY, i.ITEM_NO, i.ECN_NO, i.CUSTOM_DEP, i.SUB_CUSTOM_DEP, GETSEALNO(i.ITEM_KEY) LINER_SEAL_NO, i.CUSTOM_SEAL_NO, 
                            i.REMARKS, i.VES_ID, i.OUT_VOYAGE, i.CRT_TS, i.SITE_ID, i.HIST_FLG, i.ISO, i.CUST_REG_NO, i.FEL, i.OPER_NAME, i.GROSS, 
                            nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, 0)) as VGM,
                            i.IS_CLOSING_TS, i.REF_TOTAL, i.CUST_OUT_VOYAGE
                        FROM CUSTOM_CLEARANCE i
                            LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
	                        LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = i.ITEM_KEY AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                        WHERE i.ITEM_NO ='{itemNo}' AND i.FEL = '{fel}' AND (i.HIST_FLG = ' ' OR i.HIST_FLG = 'N' )";

            var queryResult = OraDatabase.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return new CustomClearance(queryResult.Rows[0]);
            }

            return null;

        }

        public List<CustomClearance> GetCustomClearancesByEcnNo(string ecnNo, string taxfileNo = "")
        {
            if (ecnNo.IsEmpty())
            {
                return new List<CustomClearance>();
            }

            string sql = "";

            if (taxfileNo.IsEmpty())
            {
                sql = $@"
                                    SELECT i.ITEM_KEY, i.ITEM_NO, i.ECN_NO, i.CUSTOM_DEP, i.SUB_CUSTOM_DEP, GETSEALNO(i.ITEM_KEY) LINER_SEAL_NO, i.CUSTOM_SEAL_NO, 
                                        i.REMARKS, i.VES_ID, i.OUT_VOYAGE, i.CRT_TS, i.SITE_ID, i.HIST_FLG, i.ISO, i.CUST_REG_NO, i.FEL, i.OPER_NAME, i.GROSS, 
                                        nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, 0)) as VGM,
                                        i.IS_CLOSING_TS, i.REF_TOTAL, i.CUST_OUT_VOYAGE
                                    FROM CUSTOM_CLEARANCE i
                                        LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
	                                    LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = i.ITEM_KEY AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                    WHERE i.ECN_NO = '{ecnNo}' ";
            }
            else
            {
                sql =
                    $@"SELECT c.ITEM_KEY, c.ITEM_NO, c.ECN_NO, c.CUSTOM_DEP, c.SUB_CUSTOM_DEP, GETSEALNO(c.ITEM_KEY) LINER_SEAL_NO, c.CUSTOM_SEAL_NO, 
                                        c.REMARKS, c.VES_ID, c.OUT_VOYAGE, c.CRT_TS, c.SITE_ID, c.HIST_FLG, c.ISO, c.CUST_REG_NO, c.FEL, c.OPER_NAME, c.GROSS, 
                                        nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, 0)) as VGM,
                                        c.IS_CLOSING_TS, c.REF_TOTAL, c.CUST_OUT_VOYAGE 
                                    FROM CUSTOM_CLEARANCE c
	                                    INNER JOIN CUSTOMER a ON a.CUST_REG_NO = c.CUST_REG_NO
                                        LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = c.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
                                        LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = c.ITEM_KEY AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                    WHERE c.ECN_NO = '{ecnNo}' AND a.TAX_FILE_NO = '{taxfileNo}' AND c.HIST_FLG <> 'Y' ";
            }
            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from d in queryResult.AsEnumerable()
                         select new CustomClearance(d);

            return result.ToList();
        }

        public List<CustomClearance> GetHistCustomClearances(string operName, DateTime frDate, DateTime toDate)
        {
            var whereOper = operName.IsEmpty() ? "" : $"AND OPER_NAME = '{operName}'";
            var whereDate = "";

            if (frDate.IsNotNull())
            {
                whereDate += " AND CRT_TS >= " + frDate.ToOracleShortDateString();
            }

            if (toDate.IsNotNull())
            {
                whereDate += " AND CRT_TS < " + toDate.AddDays(1).ToOracleShortDateString();
            }

            var sql = $@"SELECT * FROM CUSTOM_CLEARANCE 
                                        WHERE 1= 1 {whereOper} {whereDate} ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from d in queryResult.AsEnumerable()
                         select new CustomClearance(d);

            return result.ToList();

        }

        public DataTable GetHistDataCustomClearances(string operName, DateTime frDate, DateTime toDate)
        {
            var whereOper = operName.IsEmpty() ? "" : $"AND cc.OPER_NAME = '{operName}'";
            var whereDate = "";

            if (frDate.IsNotNull())
            {
                whereDate += " AND cc.CRT_TS >= " + frDate.ToOracleShortDateString();
            }

            if (toDate.IsNotNull())
            {
                whereDate += " AND cc.CRT_TS < " + toDate.AddDays(1).ToOracleShortDateString();
            }

            var sql =
                $@"SELECT cc.ITEM_NO SoCont, cc.FEL TrangThai, cc.ISO, cc.VES_ID VesId, cc.OUT_VOYAGE ChuyenXuat, cc.CUSTOM_DEP CucHQ, cc.SUB_CUSTOM_DEP ChiCucHQ, cc.ECN_NO SoToKhai, 
                                            cc.LINER_SEAL_NO SealHTau, cc.CUSTOM_SEAL_NO SealHQ, cc.CUST_REG_NO MaKH, c.FULL_NAME TenKH, 
                                             CASE 
                                                    WHEN cc.REMARKS LIKE 'KDCHQ' THEN TRIM(SUBSTR(cc.REMARKS,5)) 
                                                    ELSE cc.REMARKS 
                                            END AS GhiChu,cc.CRT_TS NgayThanhLy, cc.OPER_NAME MaNhanVien 
                                            ,cc.REF_TOTAL AS SOLUONG,
                                            (SELECT count(ECN_NO) FROM CUSTOM_CLEARANCE s WHERE ECN_NO=cc.ECN_NO) AS SOLUONGDADKX
                                        FROM CUSTOM_CLEARANCE cc 
                                                LEFT OUTER JOIN CUSTOMER c ON c.CUST_REG_NO = cc.CUST_REG_NO
                                        WHERE 1= 1 {whereOper} {whereDate} 
                                        ORDER BY cc.CRT_TS DESC
                                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            return queryResult;

        }

        public List<CustomsExceptionRequest> GetHistDataCustomsRequest(DateTime frDate, DateTime toDate)
        {
            var whereDate = "";

            if (frDate.IsNotNull())
            {
                whereDate += " AND APPROVED_TS >= " + frDate.ToOracleShortDateString();
            }

            if (toDate.IsNotNull())
            {
                whereDate += " AND APPROVED_TS < " + toDate.AddDays(1).ToOracleShortDateString();
            }

            var sql = $@"SELECT * FROM CUSTOMS_EXCEPTION_REQUESTS
                                        WHERE APPROVED_FLG = 'Y' {whereDate}
                                        ORDER BY APPROVED_TS DESC
                                        ";

            var queryResult = OraDatabase.ExecuteSql(sql);

            var result = from d in queryResult.AsEnumerable()
                         select new CustomsExceptionRequest(d);

            return result.ToList();

        }

        public ImportCustomsClearance GetCurrentImportCustomClearance(string itemNo, string fel)
        {
            var sql =
                $@"SELECT * FROM IMPORT_CUSTOMS_CLEARANCE WHERE ITEM_NO ='{itemNo}' AND FEL = '{fel}' AND (HIST_FLG = ' ' OR HIST_FLG = 'N' )";

            var queryResult = OraDatabase.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return new ImportCustomsClearance(queryResult.Rows[0]);
            }

            return null;
        }

        /// <summary>
        /// Lấy LoadList
        /// Created by pxtung 15/11/2010
        /// </summary>
        /// <returns></returns>
        public DataTable GetCustomInfoByVesId(string psVesId, string psEcnNo, bool isImport = true)
        {
            string lsQuery;
            string lsWhere = string.Empty;

            if (psEcnNo != string.Empty)
            {
                lsWhere = $@" AND cc.ECN_NO = '{psEcnNo}'";
            }

            if (isImport)
            {
                lsQuery =
                    $@" SELECT cc.ITEM_NO, cc.ISO, vd.VES_NAME VESSEL, vd.IN_VOYAGE VOYAGE, cm.FULL_NAME Customer,  
                                                cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, 
                                                GETSEALNO(cc.ITEM_KEY) SealHT, cc.REMARKS, cc.ECN_NO, cc.CUST_OUT_VOYAGE
                                            FROM IMPORT_CUSTOM_CLEARANCE cc 
                                            INNER JOIN VESSEL_DETAILS vd ON cc.VES_ID = vd.VES_ID
                                                LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO 
                                            WHERE cc.VES_ID =  '{psVesId}' {lsWhere} ";

            }
            else
            {
                lsQuery =
                    $@" SELECT cc.ITEM_NO, cc.ISO, vd.VES_NAME VESSEL, vd.OUT_VOYAGE VOYAGE, cm.FULL_NAME Customer, cc.Gross,
                                                cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, cc.CRT_TS,
                                                GETSEALNO(cc.ITEM_KEY) SealHT, cc.REMARKS Remark, cc.ECN_NO , cc.CUST_OUT_VOYAGE , p.DESCR as LLPOD
                                            FROM CUSTOM_CLEARANCE cc 
                                            INNER JOIN ITEM i ON i.ITEM_KEY = cc.ITEM_KEY
                                            INNER JOIN VESSEL_DETAILS vd ON cc.VES_ID = vd.VES_ID
                                                LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO 
                                                LEFT OUTER JOIN PORTCODE p ON p.PORT = i.LL_DISCH_PORT
                                            WHERE cc.VES_ID =  '{psVesId}'  {lsWhere} ";
            }

            return OraDatabase.ExecuteSql(lsQuery);

        }

        /// <summary>
        /// Lấy DS cont thanh lý hải quan 
        /// </summary>
        /// <returns></returns>
        public DataTable GetCustomInfoByEcnNo(string psEcnNo, string taxFileNo, string vesId)
        {
            var swhereCust = taxFileNo.IsEmpty() ? " " : $" AND cm.TAX_FILE_NO = '{taxFileNo}'";
            var swhereVessel = vesId.IsEmpty() ? " " : $" AND cc.VES_ID = '{vesId}'";

            var lsQuery = $@"
                                        SELECT cc.ITEM_NO, cc.ISO, vd.VES_ID, vd.VES_NAME VESSEL, vd.OUT_VOYAGE VOYAGE, cm.FULL_NAME Customer, cc.Gross, 
                                            nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, nvl(vgmLOP.CERTIFIED_WEIGHT, 0))) as VGM,
                                            cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, cc.CRT_TS,
	                                        GETSEALNO(i.ITEM_KEY) AS SealHT,
                                            CASE WHEN cc.CRT_TS > (CASE WHEN extract(YEAR FROM vd.RECV_CTR_CUTOFF_TS) = 1900 THEN vd.EST_BERTH_TS ELSE vd.RECV_CTR_CUTOFF_TS END)
                                            THEN 'CST' ELSE ' ' END Remark, cc.ECN_NO , cc.CUST_OUT_VOYAGE , p.DESCR as LLPOD
                                        FROM CUSTOM_CLEARANCE cc
                                        INNER JOIN ITEM i ON i.ITEM_KEY = cc.ITEM_KEY
                                        INNER JOIN VESSEL_DETAILS vd ON cc.VES_ID = vd.VES_ID
                                            LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO
                                            LEFT OUTER JOIN PORTCODE p ON p.PORT = i.LL_DISCH_PORT
                                            LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
                                            LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = cc.ITEM_KEY AND vgm.RECEIVE_FROM = 'CUST' AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                            LEFT OUTER JOIN ITEM_VGM vgmLOP ON vgmLOP.ITEM_KEY = cc.ITEM_KEY AND vgmLOP.RECEIVE_FROM = 'LOP' AND vgmLOP.LOAD_LIST_FLG <> 'Y' AND vgmLOP.HIST_FLG <> 'Y' AND vgmLOP.DUMMY_FLG <> 'Y'
                                        WHERE(cc.HIST_FLG = ' ' or cc.HIST_FLG = 'N')  AND cc.ECN_NO =  '{psEcnNo}' {swhereCust} {swhereVessel} ORDER BY vd.VES_ID, cc.CRT_TS ";

            //nvl(its.SEAL_NO, ' ') AS SealHT,
            //LEFT OUTER JOIN ITEM_SEAL its ON its.ITEM_KEY = cc.ITEM_KEY AND its.SEAL_TYPE = 'LOP' AND its.INVALID_SEAL <> 'Y' AND to_char(its.SEAL_REMOVING_DATE, 'yyyy') = '1900'
            //        AND NOT EXISTS  ( SELECT 1 FROM ITEM_SEAL x
            //                            WHERE x.ITEM_KEY = cc.ITEM_KEY AND x.SEAL_TYPE = 'LOP' AND x.INVALID_SEAL <> 'Y' AND to_char(x.SEAL_REMOVING_DATE, 'yyyy') = '1900' AND x.CRT_TS > its.CRT_TS
            //                        )

            return OraDatabase.ExecuteSql(lsQuery);
        }

        public DataTable GetCustomInfoByEcnNoItemNo(string psEcnNo, string vesId, string itemNo)
        {
            var lsQuery = string.Format(@" SELECT cc.ITEM_NO, cc.ISO, vd.VES_ID, vd.VES_NAME VESSEL, vd.OUT_VOYAGE VOYAGE, cm.FULL_NAME Customer, cc.Gross, 
                                                nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, nvl(vgmLOP.CERTIFIED_WEIGHT, 0))) as VGM,
                                                cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, cc.CRT_TS,
                                                GETSEALNO(i.ITEM_KEY) AS SealHT, 
                                                CASE WHEN cc.CRT_TS >  (CASE WHEN extract(YEAR FROM vd.RECV_CTR_CUTOFF_TS) = 1900 THEN vd.EST_BERTH_TS ELSE vd.RECV_CTR_CUTOFF_TS END )
                                                THEN 'CST' ELSE ' ' END Remark, cc.ECN_NO , cc.CUST_OUT_VOYAGE , p.DESCR as LLPOD
                                            FROM CUSTOM_CLEARANCE cc 
                                            INNER JOIN ITEM i ON i.ITEM_KEY = cc.ITEM_KEY
                                            INNER JOIN VESSEL_DETAILS vd ON vd.VES_ID = '{1}' 
                                                LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO 
                                                LEFT OUTER JOIN PORTCODE p ON p.PORT = i.LL_DISCH_PORT
                                                LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
                                                LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = cc.ITEM_KEY AND vgm.RECEIVE_FROM = 'CUST' AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                                LEFT OUTER JOIN ITEM_VGM vgmLOP ON vgmLOP.ITEM_KEY = cc.ITEM_KEY AND vgmLOP.RECEIVE_FROM = 'LOP' AND vgmLOP.LOAD_LIST_FLG <> 'Y' AND vgmLOP.HIST_FLG <> 'Y' AND vgmLOP.DUMMY_FLG <> 'Y'
                                            WHERE cc.ECN_NO =  '{0}' AND cc.VES_ID = vd.VES_ID AND cc.ITEM_NO = '{2}'", psEcnNo, vesId, itemNo);
            //(cc.HIST_FLG = ' ' or cc.HIST_FLG = 'N')  AND

            return OraDatabase.ExecuteSql(lsQuery);
        }

        //Lấy ds preview print
        public DataTable GetCustomClearanceByEcnNo(string psEcnNo, string taxFileNo, string vesId)
        {
            var swhereCust = taxFileNo.IsEmpty() ? " " : $" AND cm.TAX_FILE_NO = '{taxFileNo}'";
            var swhereVessel = vesId.IsEmpty() ? " " : $" AND cc.VES_ID = '{vesId}'";

            var lsQuery = $@"
                                        SELECT cc.ITEM_NO, cc.ISO, vd.VES_ID, vd.VES_NAME VESSEL, vd.OUT_VOYAGE VOYAGE, cm.FULL_NAME Customer, cc.Gross, 
                                            nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, nvl(vgmLOP.CERTIFIED_WEIGHT, 0))) as VGM,
                                            cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, cc.CRT_TS,
	                                        TOPOVN.GET_SEALNO_INYARD(i.ITEM_KEY) AS SealHT,
                                            CASE WHEN cc.CRT_TS > (CASE WHEN extract(YEAR FROM vd.RECV_CTR_CUTOFF_TS) = 1900 THEN vd.EST_BERTH_TS ELSE vd.RECV_CTR_CUTOFF_TS END)
                                            THEN 'CST' ELSE ' ' END Remark, cc.ECN_NO , cc.CUST_OUT_VOYAGE , p.DESCR as LLPOD,cc.item_key
                                        FROM CUSTOM_CLEARANCE cc
                                        INNER JOIN ITEM i ON i.ITEM_KEY = cc.ITEM_KEY
                                        INNER JOIN VESSEL_DETAILS vd ON cc.VES_ID = vd.VES_ID
                                            LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO
                                            LEFT OUTER JOIN PORTCODE p ON p.PORT = i.LL_DISCH_PORT
                                            LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
                                            LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = cc.ITEM_KEY AND vgm.RECEIVE_FROM = 'CUST' AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                            LEFT OUTER JOIN ITEM_VGM vgmLOP ON vgmLOP.ITEM_KEY = cc.ITEM_KEY AND vgmLOP.RECEIVE_FROM = 'LOP' AND vgmLOP.LOAD_LIST_FLG <> 'Y' AND vgmLOP.HIST_FLG <> 'Y' AND vgmLOP.DUMMY_FLG <> 'Y'
                                        WHERE cc.ECN_NO =  '{psEcnNo}' {swhereCust} {swhereVessel} ORDER BY vd.VES_ID, cc.CRT_TS ";


            return OraDatabase.ExecuteSql(lsQuery);
        }

        /// <summary>
        /// Lấy DS cont thanh lý hải quan theo khach hang
        /// </summary>
        /// <returns></returns>
        public DataTable GetCustomInfoByCustomer(string custRegNo, string psEcnNo, string vesId)
        {
            if (custRegNo.IsEmpty())
            {
                return new DataTable();
            }

            var swhereCust = psEcnNo.IsEmpty() ? " " : $" AND cc.ECN_NO = '{psEcnNo}'";
            var swhereVessel = vesId.IsEmpty() ? " " : $" AND cc.VES_ID = '{vesId}'";

            var lsQuery =
                $@" SELECT cc.ITEM_NO, cc.ISO, vd.VES_NAME VESSEL, vd.OUT_VOYAGE VOYAGE, cm.FULL_NAME Customer, cc.Gross, 
                                                nvl(vgmLL.CERTIFIED_WEIGHT, nvl(vgm.CERTIFIED_WEIGHT, nvl(vgmLOP.CERTIFIED_WEIGHT, 0))) as VGM,
                                                cc.CUSTOM_DEP Custom, cc.SUB_CUSTOM_DEP SubCustom, cc.CUSTOM_SEAL_NO SealHQ, cc.CRT_TS,
                                                GETSEALNO(cc.ITEM_KEY) SealHT, 
                                                CASE WHEN cc.CRT_TS >  (CASE WHEN extract(YEAR FROM vd.RECV_CTR_CUTOFF_TS) = 1900 THEN vd.EST_BERTH_TS ELSE vd.RECV_CTR_CUTOFF_TS END )
                                                THEN 'CLS' ELSE ' ' END Remark, cc.ECN_NO , cc.CUST_OUT_VOYAGE , p.DESCR as LLPOD
                                            FROM CUSTOM_CLEARANCE cc 
                                            INNER JOIN ITEM i ON i.ITEM_KEY = cc.ITEM_KEY
                                            INNER JOIN VESSEL_DETAILS vd ON cc.VES_ID = vd.VES_ID
                                                LEFT OUTER JOIN CUSTOMER cm ON cc.CUST_REG_NO = cm.CUST_REG_NO 
                                                LEFT OUTER JOIN PORTCODE p ON p.PORT = i.LL_DISCH_PORT
                                                LEFT OUTER JOIN ITEM_VGM vgmLL ON vgmLL.ITEM_KEY = i.ITEM_KEY AND vgmLL.LOAD_LIST_FLG = 'Y' AND vgmLL.HIST_FLG <> 'Y' AND vgmLL.DUMMY_FLG <> 'Y'
                                                LEFT OUTER JOIN ITEM_VGM vgm ON vgm.ITEM_KEY = cc.ITEM_KEY AND vgm.RECEIVE_FROM = 'CUST' AND vgm.LOAD_LIST_FLG <> 'Y' AND vgm.HIST_FLG <> 'Y' AND vgm.DUMMY_FLG <> 'Y'
                                                LEFT OUTER JOIN ITEM_VGM vgmLOP ON vgmLOP.ITEM_KEY = cc.ITEM_KEY AND vgmLOP.RECEIVE_FROM = 'LOP' AND vgmLOP.LOAD_LIST_FLG <> 'Y' AND vgmLOP.HIST_FLG <> 'Y' AND vgmLOP.DUMMY_FLG <> 'Y'
                                            WHERE (cc.HIST_FLG = ' ' or cc.HIST_FLG = 'N') 
                                                AND cc.custRegNo =  '{psEcnNo}' {swhereCust} {swhereVessel}";

            return OraDatabase.ExecuteSql(lsQuery);
        }

        /// <summary>
        /// Lấy DS số tờ khai lấy từ hệ thống hải quan
        /// </summary>
        /// <returns></returns>
        public DataTable GetCustomInfoByItemKey(string lstItemKey)
        {
            if (lstItemKey.IsEmpty())
            {
                return new DataTable();
            }

            var lsQuery =
                $@" SELECT v.cust_decl_no FROM VIEW_INTFHQ_CUSTOMS_CLEANCE v 
                    where v.item_key IN {lstItemKey} and v.declaration_type = '2' GROUP BY v.cust_decl_no";

            return OraDatabase.ExecuteSql(lsQuery);
        }

        public DataTable GetHistVoyagebyItemNoorCC(string ItemNO, string ECN)
        {
            string sql = string.Empty;
            DataTable result = null;
            if (!ItemNO.IsEmpty() && !ECN.IsEmpty())
            {
                sql = string.Format(@"
                    SELECT i.ITEM_KEY, i.ITEM_NO, i.ISO, i.FEL, hq.TO_KHAI 
                        , CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.VES_NAME ELSE v1.VES_NAME END  AS TAU_XUAT, 
                        CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.OUT_VOYAGE ELSE v1.OUT_VOYAGE END AS CHUYEN_XUAT, 
                        CASE WHEN v1.ACT_DEP_TS IS NULL OR extract(YEAR FROM v1.ACT_DEP_TS) = 1900 THEN i.DEP_TS ELSE v1.ACT_DEP_TS END  AS NGAY_ROI_CAT_LAI, 
                        CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.SITE_ID ELSE v1.SITE_ID END AS CANG_XUAT 
                     FROM ITEM i 
                     INNER JOIN THU_TUC_HAI_QUAN hq ON hq.ITEM_KEY = i.ITEM_KEY AND hq.TO_KHAI = '{1}' AND hq.LOAI_HANG='X' 
                     LEFT OUTER JOIN VESSEL_DETAILS v1 ON i.DEP_BY = 'V' AND v1.TFC_CODE_E = i.DEP_CAR /*AND v1.VES_TYPE <> 'B'*/ 
                     LEFT OUTER JOIN VESSEL_DETAILS v ON v.VES_CD = i.EXIT_VES_CD AND v.OUT_VOYAGE = i.EXIT_VOYAGE 
                     WHERE i.ITEM_NO = '{0}'
                      ORDER BY NGAY_ROI_CAT_LAI
                ", ItemNO, ECN);
            }
            else
            {
                if (!ItemNO.IsEmpty())
                {
                    sql = $@"
                         SELECT i.ITEM_KEY, i.ITEM_NO, i.ISO, i.FEL, hq.TO_KHAI 
                            , CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.VES_NAME ELSE v1.VES_NAME END  AS TAU_XUAT, 
                            CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.OUT_VOYAGE ELSE v1.OUT_VOYAGE END AS CHUYEN_XUAT, 
                            CASE WHEN v1.ACT_DEP_TS IS NULL OR extract(YEAR FROM v1.ACT_DEP_TS) = 1900 THEN i.DEP_TS ELSE v1.ACT_DEP_TS END  AS NGAY_ROI_CAT_LAI, 
                            CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.SITE_ID ELSE v1.SITE_ID END AS CANG_XUAT 
                         FROM ITEM i 
	                        INNER JOIN THU_TUC_HAI_QUAN hq ON hq.ITEM_KEY = i.ITEM_KEY AND hq.LOAI_HANG = 'X' 
	                        LEFT OUTER JOIN VESSEL_DETAILS v1 ON i.DEP_BY = 'V' AND v1.TFC_CODE_E = i.DEP_CAR /*AND v1.VES_TYPE <> 'B'*/ 
	                        LEFT OUTER JOIN VESSEL_DETAILS v ON v.VES_CD = i.EXIT_VES_CD AND v.OUT_VOYAGE = i.EXIT_VOYAGE 
                        WHERE i.ITEM_NO = '{ItemNO}' AND extract(YEAR FROM i.DEP_TS) > 1900 
	                        AND NOT EXISTS 
                                ( SELECT * FROM ITEM i1 
                                    INNER JOIN THU_TUC_HAI_QUAN hq1 ON hq1.ITEM_KEY = i1.ITEM_KEY AND hq1.LOAI_HANG = 'X' 
                                    WHERE i1.ITEM_NO = i.ITEM_NO AND i1.DEP_TS >= i.DEP_TS AND i1.ITEM_KEY <> i.ITEM_KEY 
                                ) 
                        ";

                }
                else
                {
                    sql = $@"
                        SELECT i.ITEM_KEY, i.ITEM_NO, i.ISO, i.FEL, hq.TO_KHAI 
                            , CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.VES_NAME ELSE v1.VES_NAME END  AS TAU_XUAT, 
                            CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.OUT_VOYAGE ELSE v1.OUT_VOYAGE END AS CHUYEN_XUAT, 
                            CASE WHEN v1.ACT_DEP_TS IS NULL OR extract(YEAR FROM v1.ACT_DEP_TS) = 1900 THEN i.DEP_TS ELSE v1.ACT_DEP_TS END  AS NGAY_ROI_CAT_LAI, 
                            CASE WHEN v1.ves_type IS NULL OR v1.ves_type = 'B' THEN v.SITE_ID ELSE v1.SITE_ID END AS CANG_XUAT 
                         FROM ITEM i 
                         INNER JOIN THU_TUC_HAI_QUAN hq ON hq.ITEM_KEY = i.ITEM_KEY AND hq.TO_KHAI = '{ECN}' AND hq.LOAI_HANG='X' 
                         LEFT OUTER JOIN VESSEL_DETAILS v1 ON i.DEP_BY = 'V' AND v1.TFC_CODE_E = i.DEP_CAR /*AND v1.VES_TYPE <> 'B'*/ 
                         LEFT OUTER JOIN VESSEL_DETAILS v ON v.VES_CD = i.EXIT_VES_CD AND v.OUT_VOYAGE = i.EXIT_VOYAGE 
                         ORDER BY NGAY_ROI_CAT_LAI
                        ";
                }
            }

            result = OraReport.ExecuteSql(sql);
            return result;
        }
        /// Lấy DS cont thanh lý hải quan 

        #endregion

        #region Global Container Search

        /// <summary>
        /// 
        /// </summary>
        /// <returns></returns>
        public List<DbView> SearchContByCondition(string sourceDbView, string conditionClause, GlobalSearchState searchState)
        {

            DbView dbViewSource = DbView.GetDbViewInheritedClasses().Where(x =>
            {
                if (x.GetScalarClass() != null)
                {
                    return x.GetScalarClass().IsFilterViewSource && x.GetScalarClass().DbTableName == sourceDbView;
                }

                return false;
            }).FirstOrDefault();

            //Not found the view
            if (dbViewSource == null)
            {
                return new List<DbView>();
            }

            string sql = string.Empty;

            sql = $@"SELECT * FROM {sourceDbView} {conditionClause} ";

            string historyColumn = dbViewSource.GetDbTableInfoFromCache().DbColumns.Where(x => dbViewSource.GetDbTableInfoFromCache().ScalarPropertyList[x.Value].IsHistoryFlag).Select(x => x.Key).FirstOrDefault();

            if (searchState != GlobalSearchState.All && string.IsNullOrWhiteSpace(historyColumn))
            {
                throw new DaException(ExceptionTypeEnum.HistoryFlagNotFound);
            }

            if (!string.IsNullOrWhiteSpace(historyColumn))
            {
                if (searchState == GlobalSearchState.Current)
                {
                    if (!string.IsNullOrWhiteSpace(historyColumn) && !conditionClause.Contains(historyColumn))
                    {
                        sql = $@"SELECT * FROM {sourceDbView} {(conditionClause.ToUpper().Contains("WHERE") ? conditionClause + " AND " : " WHERE ")}  {historyColumn} <> 'Y'";
                    }
                }
                else if (searchState == GlobalSearchState.History)
                {
                    if (!string.IsNullOrWhiteSpace(historyColumn) && !conditionClause.Contains(historyColumn))
                    {
                        sql = $@"SELECT * FROM {sourceDbView} {(conditionClause.ToUpper().Contains("WHERE") ? conditionClause + " AND " : " WHERE ")}  {historyColumn} = 'Y'";
                    }
                }
            }


            //var queryResult = OraDatabase.ExecuteSql(sql);
            var queryResult = OraReport.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return queryResult.AsEnumerable().Select(x => (DbView)Activator.CreateInstance(dbViewSource.GetType(), x)).ToList();

                /*
                if (sourceDbView == DbViewName.ViewItem)
                {
                    return (from d in queryResult.AsEnumerable()
                            select new ViewItem(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewDischargeList)
                {
                    return (from d in queryResult.AsEnumerable()
                        select new ViewDischargeList(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewEipaItem)
                {
                    return (from d in queryResult.AsEnumerable()
                        select new ViewEipaItem(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewStuffStrip)
                {
                    return (from d in queryResult.AsEnumerable()
                        select new ViewStuffStrip(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewCustomClearance)
                {
                    return (from d in queryResult.AsEnumerable()
                            select new ViewCustomClearance(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewYardConsolidation)
                {
                    return (from d in queryResult.AsEnumerable()
                            select new ViewYardConsolidation(d)).ToList<DbView>();
                }
                if (sourceDbView == DbViewName.ViewStuffStrip)
                {
                    return (from d in queryResult.AsEnumerable()
                            select new ViewStuffStrip(d)).ToList<DbView>();
                }
                 * 
                 */

            }

            return new List<DbView>();

        }

        #endregion

        #region DEPOSIT

        public Item GetItemByEirNo(string eirNo)
        {
            string sqlEir = $@"SELECT ITEM_KEY FROM PREGATE_TRANSACT WHERE EIR_ID = '{eirNo}' ";
            var itemKey = OraDatabase.ExecuteScalar(sqlEir).CheckIntEx();

            if (itemKey == 0)
            {
                return null;
            }

            string sql = $@"
                                        SELECT                                                  
                                                EIR_ID, AGENT, TARE, ITEM_KEY, ITEM_NO, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER,
                                                CUST_REG_NO, ITEM_SIZE, ITEM_STATUS, ARR_TS
                                        FROM ITEM
                                        WHERE ITEM_KEY = {itemKey} AND (HIST_FLG = ' ' OR HIST_FLG = 'N') AND FEL = 'E' ";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new Item(item);
            return result.FirstOrDefault();
        }

        #endregion

        #region INTERFACE CONTAINER

        public List<IntfContainer> IntfGetContainerByDocNo(string psDocNo)
        {
            string sql = "select * from INTF_CONTAINER ";
            string swhere = "where CREATE_DATE BETWEEN SYSDATE - 3 and SYSDATE and PROC_STATE != 'Y'";
            if (psDocNo.Trim() != string.Empty)
            {
                swhere = swhere + " and Document_No ='" + psDocNo + "'";
            }
            sql = sql + swhere;
            var queryResult = OraDatabase.ExecuteSql(sql);
            List<IntfContainer> result = (from item in queryResult.AsEnumerable()
                                          select new IntfContainer(item)).ToList();
            return result;
        }

        #endregion

        #region REGISTER ONLINE

        public DataTable GetPregateTransactOnlineByEir(string eirID, bool isEirTopo)
        {
            string sql = "select p.Cust_Reg_No,p.Consignor, p.UserId, t.* from Pregate p join Pregate_Transact t on p.Charge_Batch_Id = t.Charge_Batch_Id ";
            string sWhere = "where t.Delete_Flg = 'False'";

            if (isEirTopo)
            {
                sWhere = sWhere + " and t.Sophieu = '" + eirID + "' ";
            }
            else
            {
                sWhere = sWhere + " and t.Eir_Id = '" + eirID + "'";
            }
            sql = sql + sWhere;
            return SqlDatabase.ExcuteSQLQuery(sql);
        }

        public DataTable GetLastPregateTransactOnline(string containerNo)
        {
            string sql = "select TOP 1 p.Cust_Reg_No,p.Consignor, p.UserId, t.* from Pregate p join Pregate_Transact t on p.Charge_Batch_Id = t.Charge_Batch_Id ";
            string sWhere = "where t.Delete_Flg = 'False'";

            sWhere = sWhere + " and t.Item_No = '" + containerNo + "' ";

            sql = sql + sWhere + " ORDER BY p.Crt_Ts DESC";

            return SqlDatabase.ExcuteSQLQuery(sql);
        }

        public DataTable GetPregateTransactOnlineByBatchId(string batchId, bool isEirTopo)
        {
            string sql = "select p.Cust_Reg_No,p.Consignor, p.UserId, t.* from Pregate p join Pregate_Transact t on p.Charge_Batch_Id = t.Charge_Batch_Id ";
            string sWhere = "where t.Delete_Flg = 'False'";
            batchId = batchId.DeleteSingleQuote();

            if (isEirTopo)
            {
                sWhere = sWhere + " and p.Charge_Batch_Id = '" + batchId + "' ";
            }
            else
            {
                sWhere = sWhere + " and p.Charge_Batch_Id = '" + batchId + "'";
            }
            sql = sql + sWhere;
            return SqlDatabase.ExcuteSQLQuery(sql);
        }

        public DataTable GetAllPreGateTransactOnline()
        {
            string sql = "select p.Cust_Reg_No,p.Consignor, p.UserId, t.* from Pregate p join Pregate_Transact t on p.Charge_Batch_Id = t.Charge_Batch_Id ";
            string sWhere = "where t.Delete_Flg = 'False'";
            sql = sql + sWhere;
            return SqlDatabase.ExcuteSQLQuery(sql);
        }

        public DataTable GetItemChargesOnline(int eirIdOnline)
        {
            try
            {
                string sql = "SELECT * FROM ITEM_CHARGES ";
                string sWhere =
                    $" WHERE EIR_ID = {eirIdOnline} AND Delete_Flg = 0 AND Is_Cancelled <> 'Y' AND Partner_Status = 'confirmed' ";
                sql = sql + sWhere;
                return SqlDatabase.ExcuteSQLQuery(sql);
            }
            catch (Exception)
            {

                throw;
            }
        }

        #endregion

        #region Seal Cutting
        public List<SealCutting> LoadSealCuttingWithItemView(string BillOfLading, string BookingNo)
        {
            string query =
                "select SEAL_CUTTING.*, STACK, X, Y, Z, BOOK_NO, BILL_OF_LADING FROM SEAL_CUTTING, VIEW_ITEM WHERE VIEW_ITEM.ITEM_KEY > 0 AND VIEW_ITEM.ITEM_KEY = SEAL_CUTTING.ITEM_KEY --AND HIST_FLG <> 'Y'";

            if (!string.IsNullOrWhiteSpace(BookingNo))
            {
                query = $"{query} AND TRIM(BOOK_NO) = '{BookingNo}'";
            }
            else if (!string.IsNullOrWhiteSpace(BillOfLading))
            {
                query = $"{query} AND TRIM(BILL_OF_LADING) = '{BillOfLading}' ";
            }
            var queryResult = OraDatabase.ExecuteSql(query);
            List<SealCutting> lst = new List<SealCutting>();
            foreach (DataRow row in queryResult.AsEnumerable())
            {

                SealCutting seal = new SealCutting(row);
                string location =
                    $"{row["Stack"].ToStringEx()} {row["X"].ToStringEx()} {row["Y"].ToStringEx()} {row["Z"].ToStringEx()}";

                seal.Location = location;
                seal.BookingNo = row["BOOK_NO"].ToStringEx();
                seal.BillNo = row["BILL_OF_LADING"].ToStringEx();
                lst.Add(seal);


            }

            return lst;



        }
        #endregion

        #region ROT CONTAINER
        public List<RotContainer> GetRotContainer(RotContainer poRotCont)
        {
            var lstRot = new List<RotContainer>();

            string swhere = " where HST_FLG != 'Y'";
            if (poRotCont.TrkId != string.Empty)
            {
                swhere += " and Trk_ID = '" + poRotCont.TrkId + "'";
            }
            if (poRotCont.ItemNo != string.Empty)
            {
                swhere += " and Item_No = '" + poRotCont.ItemNo + "'";
            }
            try
            {
                string sql = "select * from  ROT_CONTAINER" + swhere;
                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from rot in queryResult.AsEnumerable()
                              select new RotContainer(rot)).ToList();
                if (result.Any())
                {
                    lstRot = result.OrderByDescending(x => x.CrtTs).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return lstRot;
        }

        public List<RotContainer> GetRotContainer(string truckNo, string histFlg = "")
        {
            var lstRot = new List<RotContainer>();

            string swhere = "";

            if (histFlg != BooleanType.Yes)
            {
                swhere = $" where TRK_ID='{truckNo}' AND HST_FLG != 'Y'";
            }
            else
            {
                swhere = $" where TRK_ID='{truckNo}' AND HST_FLG = 'Y'";
            }

            try
            {
                string sql = "SELECT * FROM  ROT_CONTAINER" + swhere;
                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from rot in queryResult.AsEnumerable()
                              select new RotContainer(rot)).ToList();
                if (result.Any())
                {
                    lstRot = result.OrderByDescending(x => x.CrtTs).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return lstRot;
        }
        public List<RotContainer> GetRotContainer(int trkKey)
        {
            var lstRot = new List<RotContainer>();

            string swhere = $" WHERE TRK_KEY='{trkKey}' and HST_FLG != 'Y'";

            try
            {
                string sql = "SELECT * FROM  ROT_CONTAINER" + swhere;
                var queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from rot in queryResult.AsEnumerable()
                              select new RotContainer(rot)).ToList();
                if (result.Any())
                {
                    lstRot = result.OrderByDescending(x => x.CrtTs).ToList();
                }
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return lstRot;
        }

        public DataTable GetContCycles(string itemNo)
        {


            try
            {
                string sql =
                    $"SELECT * FROM VIEW_ITEM_CYCLES WHERE ITEM_NO = '{itemNo}' ORDER BY ITEM_KEY, EXECUTION_DATE";
                var queryResult = OraDatabase.ExecuteSql(sql);

                return queryResult;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return null;
        }

        public List<Item> GetItems(string strCtrNo)
        {
            string sql = $@"
                            
                                        SELECT 
                                                GRADE, TERMINAL_ID, ORIG_ISO, UPD_CNT, ORG_CRT_TS, CHARGES_TO_DT, FROM_DEPOT, SLOT_CODE, DIRECT_LOAD_FLG, 
                                                EIR_ID, EXPIRY_DATE, FREE_DAYS, AGENT, TRANSFER_CD, LOAD_PORT, PEB_EXP_NO, PLACE_OF_DELIVERY, PLACE_OF_RECEIPT, 
                                                RELEASE_NO, SEAL_NO_CURRENT, TARE, VES_ID, HIST_FLG, UPD_TS, 
                                                LL_DISCH_PORT, COPRAR_LOAD, SERVICEABLE, MAX_GROSS, SITE_ID, ITEM_KEY, ITEM_NO, ARR_BY, ARR_CAR, 
                                                ARR_TS, BILL_OF_LADING, BOOK_NO, CONSIGNEE, CONSIGNOR, CATEGORY, CGO_GROSS_WT, DEP_BY, DEP_CAR, DEP_TS, 
                                                DISCH_PORT, FDISCH_PORT, FEL, GROSS, HEIGHT, IS_CTR, ISO, ITEM_CLASS, ITEM_TYPE, LENGTH, LINE_OPER, ORG_PORT,
                                                EIR_ID, ACC_NO, CMID, EXPIRY_DATE, DOMESTIC,
                                                CUST_REG_NO, DELIVERY_METHOD, DIRECT_IMPORT_DELIVERY, DELIVERY_METHOD,
                                                INTER_MOVE_CODE, IS_CFS, ITEM_SIZE, ITEM_STATUS, ORIGIN_TERMINAL,
                                                SEAL_CHECK_REQ, SHIPPER, SPECIAL_GEAR, SPECIAL_HDL_CODE,
                                                SPECIAL_USED, TRUCK_BARGE_INTERNAL, TURNING_CHE, 
                                                BB_ID, COPRAR_DISCHARGE, CTR_OWNER, 
                                                PKG_UNIT, PKG_TYPE, RECEIVAL_METHOD, RA_KEY, EXIT_VES_CD, EXIT_VOYAGE
                                        FROM ITEM
                                        WHERE ITEM_NO = '{strCtrNo}' AND EXTRACT(YEAR FROM ARR_TS) > 1900

                            ";


            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new Item(item);

            return result.ToList();
        }

        public List<ItemSpecialHandlingExt> GetItemSpecialHandlingExt(int itemKey)
        {
            string sql = $@"SELECT hdlex.* 
                                            FROM TOPOVN.ITEM_SPECIAL_HANDLING_EXT hdlex, ITEM_SPECIAL_HANDLING hdl 
                                            WHERE hdlex.ITEM_KEY = hdl.ITEM_KEY AND hdlex.SPECIAL_HDL_CODE = hdl.CODE AND hdlex.ITEM_KEY = {itemKey}";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new ItemSpecialHandlingExt(item);

            return result.ToList();

            //return OraDatabase.GetDbValueObjectByAssignedFields(new ItemSpecialHandlingExt {ItemKey = itemKey});
        }

        public List<SpecialHandlingCodeExt> GetSpecialHandlingExt(int itemKey)
        {
            string sql = $@"SELECT hdlex.* 
                                            FROM TOPOVN.SPECIAL_HDL_CODE_EXT  hdlex, ITEM_SPECIAL_HANDLING hdl 
                                            WHERE hdlex.SPECIAL_HDL_CODE = hdl.CODE AND hdl.ITEM_KEY = {itemKey}";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from item in queryResult.AsEnumerable()
                         select new SpecialHandlingCodeExt(item);

            return result.ToList();

            //return OraDatabase.GetDbValueObjectByAssignedFields(new ItemSpecialHandlingExt {ItemKey = itemKey});
        }

        #endregion

        #region VGM

        public List<ViewReportVgm> GetListContainerNoVgmLLButInyard(string tfcCode)
        {
            string sql = $@"
                                SELECT e.TFC_CODE, e.VES_ID, e.ITEM_NO, e.AGENT, e.LINER_CODE, e.ISO, v.CERTIFIED_WEIGHT AS LINER_VGM, v.CERTIFIED_BY AS LINER_VGM_AUTHORIZED, i.CERTIFIED_WEIGHT AS CUST_VGM, i.CERTIFIED_BY AS CUST_VGM_AUTHORIZED
                                FROM EDI_ITEM_PRE_ADVICE e 
                                    LEFT OUTER JOIN ITEM_VGM i ON i.ITEM_KEY = e.ITEM_KEY AND i.LOAD_LIST_FLG <> 'Y' AND i.HIST_FLG <> 'Y' AND i.DUMMY_FLG <> 'Y'
                                    LEFT OUTER JOIN ITEM_VGM v ON v.ITEM_KEY = e.ITEM_KEY AND v.LOAD_LIST_FLG = 'Y' AND v.HIST_FLG <> 'Y' AND v.DUMMY_FLG <> 'Y'
                                WHERE e.TFC_CODE = '{tfcCode}' AND e.FEL = 'F' AND v.CERTIFIED_WEIGHT IS NULL AND i.CERTIFIED_WEIGHT IS NOT NULL
                            ".TrimEx();

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from r in queryResult.AsEnumerable()
                         select new ViewReportVgm(r);

            return result.ToList();
        }

        public List<ViewReportVgm> GetListContainerVgmInyard(string tfcCode)
        {
            string sql = $@"
                                SELECT e.TFC_CODE, e.VES_ID, e.ITEM_NO, e.AGENT, e.LINER_CODE, e.ISO, v.CERTIFIED_WEIGHT AS LINER_VGM, v.CERTIFIED_BY AS LINER_VGM_AUTHORIZED, i.CERTIFIED_WEIGHT AS CUST_VGM, i.CERTIFIED_BY AS CUST_VGM_AUTHORIZED
                                FROM EDI_ITEM_PRE_ADVICE e 
                                    LEFT OUTER JOIN ITEM_VGM i ON i.ITEM_KEY = e.ITEM_KEY AND i.LOAD_LIST_FLG <> 'Y' AND i.HIST_FLG <> 'Y' AND i.DUMMY_FLG <> 'Y'
                                    LEFT OUTER JOIN ITEM_VGM v ON v.ITEM_KEY = e.ITEM_KEY AND v.LOAD_LIST_FLG = 'Y' AND v.HIST_FLG <> 'Y' AND v.DUMMY_FLG <> 'Y'
                                WHERE TFC_CODE = '{tfcCode}' AND e.FEL = 'F' AND i.CERTIFIED_WEIGHT IS NOT NULL
                            ".TrimEx();

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from r in queryResult.AsEnumerable()
                         select new ViewReportVgm(r);

            return result.ToList();
        }

        public List<ViewReportVgm> GetListContainerVgmConflictInyard(string tfcCode)
        {
            string sql = $@"
                                SELECT e.TFC_CODE, e.VES_ID, e.ITEM_NO, e.AGENT, e.LINER_CODE, e.ISO, v.CERTIFIED_WEIGHT AS LINER_VGM, v.CERTIFIED_BY AS LINER_VGM_AUTHORIZED, i.CERTIFIED_WEIGHT AS CUST_VGM, i.CERTIFIED_BY AS CUST_VGM_AUTHORIZED
                                FROM EDI_ITEM_PRE_ADVICE e 
                                    LEFT OUTER JOIN ITEM_VGM i ON i.ITEM_KEY = e.ITEM_KEY AND i.LOAD_LIST_FLG <> 'Y' AND i.HIST_FLG <> 'Y' AND i.DUMMY_FLG <> 'Y'
                                    LEFT OUTER JOIN ITEM_VGM v ON v.ITEM_KEY = e.ITEM_KEY AND v.LOAD_LIST_FLG = 'Y' AND v.HIST_FLG <> 'Y' AND v.DUMMY_FLG <> 'Y'
                                WHERE TFC_CODE = '{tfcCode}' AND e.FEL = 'F' AND nvl(v.CERTIFIED_WEIGHT,0) <> nvl(i.CERTIFIED_WEIGHT,0)
                            ".TrimEx();

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = from r in queryResult.AsEnumerable()
                         select new ViewReportVgm(r);

            return result.ToList();
        }

        public List<ItemVgm> GetVgmReportByTruckIn(DateTime fromDate, DateTime toDate)
        {
            //SELECT *  FROM ITEM_VGM WHERE RECEIVE_FROM = 'CUST' AND UPPER(CERTIFIED_BY) NOT LIKE '%CHU%HANG%' AND CRT_TS BETWEEN TO_DATE('2016-07-01', 'YYYY-MM-DD') AND TO_DATE('2016-07-01 06:00:00', 'YYYY-MM-DD HH24:Mi:SS') ORDER BY CRT_TS

            //SELECT *  FROM ITEM_VGM WHERE RECEIVE_FROM = 'CUST' AND UPPER(CERTIFIED_BY) LIKE '%CHU%HANG%' AND CRT_TS BETWEEN TO_DATE('2016-07-01', 'YYYY-MM-DD') AND TO_DATE('2016-07-01 06:00:00', 'YYYY-MM-DD HH24:Mi:SS') ORDER BY CRT_TS

            string sql =
                $@" SELECT *  FROM ITEM_VGM WHERE RECEIVE_FROM = 'CUST' AND UPPER(CERTIFIED_BY) NOT LIKE '%CHU%HANG%' AND 
                                                    CRT_TS BETWEEN {fromDate.ToOracleDateString()} AND {toDate.ToOracleDateString()} AND HIST_FLG <> 'Y' AND DUMMY_FLG <> 'Y' AND MANIFEST_KEY = 0
                                                    ORDER BY CRT_TS";

            DataTable queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from r in queryResult.AsEnumerable()
                          select new ItemVgm(r)).ToList();

            foreach (var itemVgm in result)
            {
                itemVgm.Method = "Khách hàng tự khai";
            }

            sql = $@" SELECT *  FROM ITEM_VGM WHERE RECEIVE_FROM = 'CUST' AND UPPER(CERTIFIED_BY) LIKE '%CHU%HANG%' AND 
                                                    CRT_TS BETWEEN {fromDate.ToOracleDateString()} AND {toDate.ToOracleDateString()} AND HIST_FLG <> 'Y' AND DUMMY_FLG <> 'Y' AND MANIFEST_KEY = 0
                                                    ORDER BY CRT_TS";

            queryResult = OraDatabase.ExecuteSql(sql);
            var result2 = (from r in queryResult.AsEnumerable()
                           select new ItemVgm(r)).ToList();

            foreach (var itemVgm in result2)
            {
                itemVgm.Method = "Có mộc";
            }
            var ret = result.ToList();

            ret.AddRange(result2.ToList());

            return ret;
        }

        public void MarkItemVgmToLoadList(ItemVgm vgm)
        {
            vgm.LoadListFlg = BooleanType.Yes;

            OraDatabase.UpdateDbValueObjectOnConditionRowId(vgm, true);
        }

        public List<Item> GetItemsInOut(DateTime fromDate, DateTime toDate, string siteId)
        {
            try
            {

                var psSQL = string.Format("SELECT * from ITEM WHERE (ARR_TS BETWEEN {0} AND {1} OR DEP_TS BETWEEN {0} AND {1}) AND SITE_ID = '{2}' AND Category <> 'R'",
                                        fromDate.ToOracleDateString(), toDate.ToOracleDateString(), siteId);

                var queryResult = OraDatabase.ExecuteSql(psSQL);

                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        public List<Item> GetItemsInYard(string siteId)
        {
            try
            {

                var psSQL =
                    $"SELECT i.* FROM ITEM i WHERE i.HIST_FLG <> 'Y' AND i.DEP_TS <= TO_DATE('1901-01-01', 'YYYY-MM-DD') AND i.CATEGORY <> 'R' AND Site_ID = '{siteId}'";

                var queryResult = OraDatabase.ExecuteSql(psSQL);

                return (from d in queryResult.AsEnumerable()
                        select new Item(d)).ToList();

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<Item>();
        }

        public DataTable GetItemsInOut02(DateTime fromDate, DateTime toDate, string siteId)
        {
            try
            {

                var psSQL = string.Format("SELECT " +
                                          " i.ITEM_NO, i.ITEM_KEY, i.FEL, GETSEALNO(i.ITEM_KEY) SEAL_NO, i.ISO, i.CATEGORY," +
                                          "i.LINE_OPER, i.AGENT, i.ARR_BY, i.ARR_TS, i.DEP_BY, i.DEP_TS, " +
                                          " CASE WHEN i.ARR_BY = 'T' THEN tIn.TRK_ID ELSE i.ARR_CAR END ARR_CAR, " +
                                          "CASE WHEN i.ARR_BY = 'T' THEN tIn.LANE_NO_IN ELSE ' ' END LANE_NO_IN, " +
                                          "CASE WHEN i.DEP_BY = 'T' THEN tOut.TRK_ID ELSE i.DEP_CAR END DEP_CAR, " +
                                          "CASE WHEN i.DEP_BY = 'T' THEN tOut.LANE_NO_OUT ELSE ' ' END LANE_NO_OUT, " +
                                          "i.BILL_OF_LADING, i.BOOK_NO, i.TARE, i.CGO_GROSS_WT, i.GROSS,i.MAX_GROSS, " +
                                          "NVL(pStuff.OPERATION_METHOD, ' ') STUFF_METHOD, NVL(pStrip.OPERATION_METHOD, ' ') STRIP_METHOD, " +
                                          "nvl(hqn.To_Khai, nvl(hqx.TO_KHAI, ' ')) TO_KHAI," +
                                          "nvl(hqn.Ngay_To_Khai, nvl(hqx.Ngay_To_Khai, TO_DATE('1900-01-01', 'YYYY-MM-DD'))) NGAY_TO_KHAI" +
                                          " FROM ITEM  i" +
                                          " LEFT OUTER JOIN TRK_TRANSACT trkIn ON trkIn.ITEM_KEY = i.ITEM_KEY AND trkIn.R_D = 'R'" +
                                          " LEFT OUTER JOIN TRUCK tIn ON tIn.TRK_KEY = trkIn.TRK_KEY" +
                                          " LEFT OUTER JOIN TRK_TRANSACT trkOut ON trkOut.ITEM_KEY = i.ITEM_KEY AND trkOut.R_D = 'D' " +
                                          " LEFT OUTER JOIN TRUCK tOut ON tOut.TRK_KEY = trkOut.TRK_KEY" +
                                          " LEFT OUTER JOIN PREGATE_TRANSACT pStuff ON pStuff.ITEM_KEY = i.ITEM_KEY AND pStuff.R_D = ' ' " +
                                          " AND pStuff.OPERATION_TYPE IN ('STUFF')" +
                                          " LEFT OUTER JOIN PREGATE_TRANSACT pStrip ON pStrip.ITEM_KEY = i.ITEM_KEY AND pStrip.R_D = ' ' AND pStrip.OPERATION_TYPE IN ('STRIP')" +
                                          " LEFT OUTER JOIN THU_TUC_HAI_QUAN hqn ON i.ITEM_KEY = hqn.ITEM_KEY AND hqn.LOAI_HANG = 'N'" +
                                          " LEFT OUTER JOIN THU_TUC_HAI_QUAN hqx ON i.ITEM_KEY = hqx.ITEM_KEY AND hqx.LOAI_HANG = 'X' " +
                                          " WHERE (i.ARR_TS BETWEEN {0} AND {1} OR i.DEP_TS BETWEEN {0} AND {1}) AND i.SITE_ID = '{2}' AND i.Category <> 'R'",
                                          fromDate.ToOracleDateString(), toDate.ToOracleDateString(), siteId);

                var queryResult = OraDatabase.ExecuteSql(psSQL);

                return queryResult;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new DataTable();
        }

        #endregion VGM

        #region VIEW_REEFER_DAMAGE: Quannx Added

        /// <summary>
        /// Get list ViewReeferDamage by pti date
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewReeferPtiGas> GetListViewReeferPti(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_REEFER_PTI_GAS WHERE PTI_DATE >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') AND PTI_DATE <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewReeferPtiGas(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get list ViewReeferDamage by gas date
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewReeferPtiGas> GetListViewReeferGas(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_REEFER_PTI_GAS WHERE GAS_DATE >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') AND GAS_DATE <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewReeferPtiGas(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get ViewDetailPtiInVoyage by VesCd and InVoyage
        /// </summary>
        /// <param name="VesCd"></param>
        /// <param name="inVoyage"></param>
        /// <returns></returns>
        public List<ViewDetailPtiInVoyage> GetListViewDetailPtiInVoyageByVessel(string VesCd, string inVoyage)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_DETAIL_PTI_IN_VOYAGE WHERE VES_CD = '{VesCd.TrimEx()}' AND TFC_CODE_I = '{inVoyage.TrimEx()}'";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewDetailPtiInVoyage(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get ViewDetailPtiInVoyage by Pti Date
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewDetailPtiInVoyage> GetListViewDetailPtiInVoyageByDate(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_DETAIL_PTI_IN_VOYAGE WHERE PTI_DATE >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') AND PTI_DATE <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewDetailPtiInVoyage(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get ViewDetailPtiOutVoyage by VesCd and OutVoyage
        /// </summary>
        /// <param name="VesCd"></param>
        /// <param name="outVoyage"></param>
        /// <returns></returns>
        public List<ViewDetailPtiOutVoyage> GetListViewDetailPtiOutVoyageByVessel(string VesCd, string outVoyage)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_DETAIL_PTI_OUT_VOYAGE WHERE VES_CD = '{VesCd.TrimEx()}' AND TFC_CODE_E = '{outVoyage.TrimEx()}'";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewDetailPtiOutVoyage(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Get ViewDetailPtiOutVoyage by Pti Date
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewDetailPtiOutVoyage> GetListViewDetailPtiOutVoyageByDate(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql =
                    $@"SELECT * FROM VIEW_DETAIL_PTI_OUT_VOYAGE WHERE PTI_DATE >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') AND PTI_DATE <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewDetailPtiOutVoyage(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// Thống kê tổng số giờ chạy điện cont lạnh theo hãng tàu
        /// </summary>
        /// <returns></returns>
        public DataTable GetPtiSummary()
        {
            try
            {
                string sql = string.Format(@"SELECT T1.LINE_OPER,
	                                            SUM(T1.PTI_IN_20) AS TOTAL_PTI_IN_20,
	                                            SUM(T1.PTI_IN_40) AS TOTAL_PTI_IN_40,
	                                            SUM(T1.PTI_OUT_20) AS TOTAL_PTI_OUT_20,
	                                            SUM(T1.PTI_OUT_40) AS TOTAL_PTI_OUT_40
                                            FROM
                                            (
	                                            SELECT in1.LINE_OPER,
		                                            SUM(CASE WHEN in1.LENGTH = '20' THEN in1.RUN_TIME_20 ELSE 0 END) AS PTI_IN_20,
		                                            SUM(CASE WHEN in1.LENGTH = '40' THEN in1.RUN_TIME_40 ELSE 0 END) AS PTI_IN_40,
		                                            0 AS PTI_OUT_20,
		                                            0 AS PTI_OUT_40
	                                            FROM 
		                                            TOPOVN.VIEW_DETAIL_PTI_IN_VOYAGE in1 
	                                            GROUP BY in1.LINE_OPER
	                                            UNION 
	                                            SELECT out1.LINE_OPER,
		                                            0 AS PTI_IN_20,
		                                            0 AS PTI_IN_40,
		                                            SUM(CASE WHEN out1.LENGTH = '20' THEN out1.RUN_TIME_20 ELSE 0 END) AS PTI_OUT_20,
		                                            SUM(CASE WHEN out1.LENGTH = '40' THEN out1.RUN_TIME_40 ELSE 0 END) AS PTI_OUT_40
	                                            FROM 
		                                            TOPOVN.VIEW_DETAIL_PTI_OUT_VOYAGE out1
	                                            GROUP BY out1.LINE_OPER
                                            ) T1
                                            GROUP BY T1.LINE_OPER");

                return OraDatabase.ExecuteSql(sql);
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewPlugUnPlugReeferItems> GetListViewPlugUnPlugReeferItems(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql = $@"SELECT * FROM VIEW_PLUG_UNPLUG_REEFER_ITEMS 
                                            WHERE 
                                                DEP_TS >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') AND DEP_TS <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewPlugUnPlugReeferItems(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        /// <summary>
        /// Lay thong tin PTI
        /// </summary>
        /// <param name="fromDate"></param>
        /// <param name="toDate"></param>
        /// <returns></returns>
        public List<ViewPtiItemCharges> GetListItemPti(DateTime fromDate, DateTime toDate)
        {
            try
            {
                string sql =
                    $@"SELECT p.ITEM_KEY, p.ITEM_NO,p.PTI_DATE, p.PTI_COMPLETE_DATE, p.PTI_METHOD, p.REMARKS,p.IS_PTI, p.IS_PTI_OK,
                                               i.CATEGORY, i.LENGTH, i.LINE_OPER, i.ISO, i.ARR_TS, i.DEP_TS,
                                               pp.CALCULATED_FLG, pp.USER_CONFIRM, pp.COMMITTED_TS, pp.COMMITTED_FLG, pp.ITEM_CHARGES_ID, i.SITE_ID
                                            FROM ITEM_PTI p 
                                                INNER JOIN ITEM i ON p.ITEM_KEY = i.ITEM_KEY
                                                LEFT OUTER JOIN PTI_PRODUCTIVITY_ITEM pp ON i.ITEM_KEY = pp.ITEM_KEY 
                                            WHERE p.IS_PTI_OK = 'Y' 
                                            AND p.PTI_DATE >= to_Date('{fromDate}', 'dd/mm/yyyy hh24:mi:ss') 
                                            AND p.PTI_DATE <= to_Date('{toDate}', 'dd/MM/yyyy hh24:mi:ss')
                                            AND NOT EXISTS (SELECT 1 FROM ITEM_PTI pt WHERE p.ITEM_KEY = pt.ITEM_KEY AND pt.IS_PTI_OK = 'Y' AND pt.PTI_DATE > p.PTI_DATE) ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewPtiItemCharges(d);

                return result.ToList();
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }


        #region Edit Item Subs in one Screen
        public DataSet GetListContForConvert(int seqFrom, int seqTo, string convertState)
        {
            try
            {
                var ds = new DataSet();
                var sql = $@"SELECT a.Seq, a.STATE, ic.* FROM CMS_CONVERT_SEQ a, ITEM_COMMENTS ic, ITEM i 
                                                WHERE a.ITEM_KEY = ic.ITEM_KEY AND ic.ITEM_KEY = i.ITEM_KEY AND i.HIST_FLG <> 'Y' 
                                                AND {seqFrom} <= a.Seq AND a.Seq <= {seqTo} AND a.State = '{convertState}'";

                if (convertState.IsEmpty())
                {
                    sql = $@"SELECT a.Seq, a.STATE, ic.* FROM CMS_CONVERT_SEQ a, ITEM_COMMENTS ic, ITEM i 
                                                WHERE a.ITEM_KEY = ic.ITEM_KEY AND ic.ITEM_KEY = i.ITEM_KEY AND i.HIST_FLG <> 'Y' 
                                                AND {seqFrom} <= a.Seq AND a.Seq <= {seqTo}";

                }
                var queryResult = OraDatabase.ExecuteSql(sql);
                ds.Tables.Add(queryResult);

                return ds;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        public DataTable GetItemComment02(int itemKey)
        {
            try
            {

                var sql = $@"SELECT * FROM ITEM_COMMENTS WHERE Item_Key = {itemKey}";
                var queryResult = OraDatabase.ExecuteSql(sql);
                return queryResult;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        public void SaveItemUpdateLog(int itemKey, string itemNo, string dataType, string userId, string userName, bool alwayInsertData = true)
        {
            try
            {
                if (itemKey <= 0 || dataType.IsEmpty() || userId.IsEmpty())
                {
                    return;
                }
                var sql =
                    $"INSERT INTO ITEM_UPDATE_LOGS(Item_Key, Item_No, Data_Type, User_ID, User_Name) VALUES({itemKey}, '{itemNo}', '{dataType}', '{userId}', '{userName}')";

                //HungTran Coding......==> problem: update hàng loạt
                if (!alwayInsertData)
                {

                    sql =
                        $"SELECT * FROM ITEM_UPDATE_LOGS WHERE ITEM_KEY = {itemKey} AND ITEM_NO = '{itemNo}' AND DATA_TYPE = '{dataType}'";

                    var queryResult = OraDatabase.ExecuteSql(sql);
                    //Update
                    if (queryResult.Rows.Count > 0)
                    {
                        return;
                        //nếu tồn tại thì update, ngược lại thì insert
                        //sql =
                        //string.Format(
                        //    "UPDATE ITEM_UPDATE_LOGS SET Data_Type = '{2}' ) WHERE ITEM_KEY = {0} AND ITEM_NO = '{1}' AND DATA_TYPE = '{2}'"
                        //    , itemKey, itemNo, dataType, userId, userName);
                    }

                }
                OraDatabase.ExecuteSql(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
        }

        public DataTable GetItemUpdateLogs(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var sql =
                    $"SELECT * FROM ITEM_UPDATE_LOGS WHERE {fromDate.ToOracleDateString()} <= Upd_Ts AND Upd_Ts <= {toDate.ToOracleDateString()} AND ROWNUM <= 1000000 ORDER BY Upd_Ts DESC ";
                var queryResult = OraDatabase.ExecuteSql(sql);

                return queryResult;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return null;
        }

        public void SaveCommentsEx(int itemKey, int seq, string cmsComment, string convertComment)
        {
            try
            {
                if (itemKey <= 0)
                {
                    throw new Exception("Chưa có thông tin ItemKey");
                }

                var sql =
                    $"UPDATE ITEM_COMMENTS SET CMS_COMMENTS= '{cmsComment}', CONVERT_COMMENTS='{convertComment}' WHERE Item_Key = {itemKey} AND COMMENT_SEQ = {seq} ";
                OraDatabase.ExecuteSql(sql);

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }


        }


        #endregion


        #endregion

        #region INTF_CUSTOMS

        public string GetCtnrKeyIntfCustom(string siteId, string itemNo, int itemKey, string impExp, string fel)
        {
            var sql =
                $"SELECT APP_INTFHQ.FORMAT_INTF_CTNR_KEY('{siteId.TrimEx()}', '{itemNo.TrimEx()}', {itemKey}, '{impExp.TrimEx()}', '{fel.TrimEx()}') FROM DUAL";

            return OraDatabase.ExecuteScalar(sql).TrimEx();
        }

        #endregion INTF_CUSTOMS

        public List<CtnrMoveRegDetails> GetReqsScanFromCustom(string itemNo, DateTime fromDate, DateTime toDate)
        {
            try
            {
                var psSQL = $@"SELECT b.* FROM CTNR_MOVE_REG_MASTER a, CTNR_MOVE_REG_DETAILS b 
                                            WHERE b.ITEM_KEY = 0 AND b.CRT_TS BETWEEN {fromDate.ToOracleDateString()} AND {toDate.ToOracleDateString()} AND 
                                            a.REG_ID = b.REG_ID AND a.SPECIAL_HANDLING_CODE = '{SpecialHandlingCodes.MSQ}' AND 
                                            b.CTNR_NO = '{itemNo}' ";

                var queryResult = OraDatabase.ExecuteSql(psSQL);

                var result = (from r in queryResult.AsEnumerable()
                              select new CtnrMoveRegDetails(r)).ToList();

                return result;

            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<CtnrMoveRegDetails>();
        }

        public List<ViewCtrEnquiry> GetContainerByBookOrBill(string lineOper, string bookNo, string billOfLading)
        {
            var strLineOper = lineOper.IsEmpty() ? "" : $@" AND i.line_oper = '{lineOper.ToUpper()}' ";
            var strBookNo = bookNo.IsEmpty() ? "" : $@" AND i.BOOK_NO = '{bookNo.ToUpper()}' ";
            var strBill = billOfLading.IsEmpty() ? "" : $@" AND i.BILL_OF_LADING = '{billOfLading.ToUpper()}' ";

            try
            {
                var strSelect = string.Format($@"SELECT i.item_no,i.site_id, i.fel, i.category, i.length, i.height, i.gross, NVL(ish.SPECIAL_HDL_CODE, ' ') AS SPECIAL_HDL_CODE, i.AGENT,
                            i.CUST_RELEASE_MARK, i.BILL_OF_LADING, i.BOOK_NO, 
                            GETSEALNO(i.ITEM_KEY, 'ITEM') AS SEAL_NO_CURRENT,
                            GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS,i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car,
                            i.dep_ts, i.dep_by, i.item_key,  
                            i.line_oper, i.is_attach,i.bundle_to,i.UNBUNDLE_FROM, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT,
                             i.FDISCH_PORT, 
                            i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,i.bb_id,
                            i.RELEASE_NO, il.STK_REF, 
                            il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner, 
                            NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <= 1), ' ') AS CLR_BY, 
                            CASE WHEN i.EIR_ID = ' ' THEN ' ' 
                              ELSE NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <= 1), ' ') 
                            END OPERATION_METHOD, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                                 WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                              WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM CUSTOM_CLEARANCE tq 
                              WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check,
                            CASE WHEN i.Item_key>0 THEN TOPOVN.GET_ITEM_COMMODITY(i.Item_key) ELSE ' ' END as COMMODITY,
                            CASE WHEN i.ImoUno != '-' THEN i.ImoUno ELSE '' END ImoUno
                         FROM 
                (SELECT  
                        i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, i.AGENT,
                        i.BILL_OF_LADING, i.BOOK_NO, 
                        i.ITEM_KEY,
                        i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                        i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                        i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg, i.RELEASE_NO, 
                        i.Owner, 
                        i.EIR_ID,
                        i.PEB_EXP_NO,
                        i.PIB_IMP_NO,
                                      
                        i.bb_id,
                        i.UNBUNDLE_FROM,
                        i.bundle_to,
                        i.is_attach,
                        LISTAGG(trim(d.dgs_class) || '-' || trim(d.un_no) , ';') WITHIN GROUP (ORDER BY d.dgs_class , d.un_no ) AS ImoUno
                    FROM item i LEFT JOIN item_dangerous d ON (d.item_key = i.Item_key)
                    WHERE i.ARR_TS > to_date('01/01/1901', 'dd/MM/yyyy')   
                    {strLineOper} 
                    {strBookNo} {strBill}  
                                     
                    GROUP BY 
                        i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.AGENT, i.CUST_RELEASE_MARK,
                        i.BILL_OF_LADING, i.BOOK_NO, 
                        i.ITEM_KEY,
                        i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                        i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                        i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg, i.RELEASE_NO, 
                        i.Owner, 
                        i.EIR_ID,
                        i.PEB_EXP_NO,
                        i.PIB_IMP_NO,
                        i.bb_id,
                        i.UNBUNDLE_FROM,
                        i.bundle_to,
                        i.is_attach
                ) i
                INNER JOIN item_location il ON (i.item_key = il.item_key)
                LEFT JOIN ITEM_SPECIAL_HANDLING ish ON (ish.ITEM_KEY = i.ITEM_KEY)

                         where il.stk_pch = 'C'
                                and rownum < 105002
                         ORDER BY i.item_no,i.item_key");

                if (!billOfLading.IsEmpty())
                {
                    strSelect = $@"SELECT d.item_no,i.site_id, d.fel, d.category, i.length, i.height, i.gross, NVL(ish.SPECIAL_HDL_CODE, ' ') AS SPECIAL_HDL_CODE, i.AGENT,
                            i.CUST_RELEASE_MARK, i.BILL_OF_LADING, i.BOOK_NO, 
                            GETSEALNO(i.ITEM_KEY, 'ITEM') AS SEAL_NO_CURRENT,
                            GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS,i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car,
                            i.dep_ts, i.dep_by, i.item_key,  
                            i.line_oper, i.is_attach,i.bundle_to,i.UNBUNDLE_FROM, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, 
                            i.FDISCH_PORT, 
                            i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,i.bb_id,
                            i.RELEASE_NO, il.STK_REF, 
                            il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner, 
                            NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <= 1), ' ') AS CLR_BY, 
                            CASE WHEN i.EIR_ID = ' ' THEN ' '
                              ELSE NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <= 1), ' ') 
                            END OPERATION_METHOD, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                               WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                               WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check, 
                            NVL((SELECT 'Y' AS Exp_Custom_Check FROM CUSTOM_CLEARANCE tq 
                              WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check,
                            CASE WHEN i.Item_key>0 THEN TOPOVN.GET_ITEM_COMMODITY(i.Item_key) ELSE ' ' END as COMMODITY,
                            CASE WHEN i.ImoUno != '-' THEN i.ImoUno ELSE '' END ImoUno
                        FROM DISCHARGE_LIST d, 
                            ( SELECT  
                                    i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, i.AGENT,
                                    i.BILL_OF_LADING, i.BOOK_NO, 
                                    i.ITEM_KEY,
                                    i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                    i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                    i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                    i.RELEASE_NO, 
                                    i.Owner, 
                                    i.EIR_ID,
                                    i.PEB_EXP_NO,
                                    i.PIB_IMP_NO,
                                    i.bb_id,
                                    i.UNBUNDLE_FROM,
                                    i.bundle_to,
                                    i.is_attach,
                                    LISTAGG(trim(d.dgs_class) || '-' || trim(d.un_no) , ';') WITHIN GROUP (ORDER BY d.dgs_class , d.un_no ) AS ImoUno
                                FROM item i LEFT JOIN item_dangerous d ON (d.item_key = i.Item_key)
                                GROUP BY 
                                    i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.AGENT, i.CUST_RELEASE_MARK,
                                    i.BILL_OF_LADING, i.BOOK_NO,
                                    i.ITEM_KEY,
                                    i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                    i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                    i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                    i.RELEASE_NO, 
                                    i.Owner, 
                                    i.EIR_ID,
                                    i.PEB_EXP_NO,
                                    i.PIB_IMP_NO,
                                    i.bb_id,
                                    i.UNBUNDLE_FROM,
                                    i.bundle_to,
                                    i.is_attach
                            ) i
                            LEFT JOIN item_location il ON (i.item_key = il.item_key and il.stk_pch = 'C')
                            LEFT JOIN ITEM_SPECIAL_HANDLING ish ON (ish.ITEM_KEY = i.ITEM_KEY)
                       WHERE d.ITEM_KEY = i.ITEM_KEY AND i.item_key = il.item_key and il.stk_pch = 'C'
                                  AND d.BILL_OF_LADING = '{billOfLading.Trim()}' AND d.ITEM_KEY > 0
                       ORDER BY i.item_no,i.item_key";
                }

                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCtrEnquiry(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCtrEnquiry>();
        }

        /// <summary>
        /// Get data for C03
        /// </summary>
        /// <returns></returns>

        public List<ViewCtrEnquiry> GetContainerInYard(string block, string cboLOP, string cboCtrISO, string cboCategory, string fel)
        {
            var strFel = fel.IsEmpty() ? "" : $@" AND i.fel = '{fel}' ";
            var strLineOper = cboLOP.IsEmpty() ? "" : $@" AND i.line_oper = '{cboLOP.ToUpper()}' ";
            var strIso = cboCtrISO.IsEmpty() ? "" : $@" AND i.iso = '{cboCtrISO.ToUpper()}' ";
            var strCategory = cboCategory.IsEmpty() ? "" : $@" AND i.Category = '{cboCategory.ToUpper()}' ";
            var strStack = block.IsEmpty() ? "" : $@" AND il.STACK = '{block.ToUpper()}' ";

            try
            {
                var strSelect = string.Format($@"SELECT i.item_no,i.site_id, i.fel, i.category, i.length, i.height, i.gross, NVL(ish.SPECIAL_HDL_CODE, ' ') AS SPECIAL_HDL_CODE, i.AGENT,
                                i.CUST_RELEASE_MARK, i.BILL_OF_LADING, i.BOOK_NO, GETSEALNO(i.ITEM_KEY, 'ITEM') AS SEAL_NO_CURRENT,
                                GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS,i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car,
                                i.dep_ts, i.dep_by, i.item_key, i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT,
                                i.FDISCH_PORT, i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO,
                                i.hist_flg, i.RELEASE_NO, il.STK_REF, il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner,
                                NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <= 1), ' ') AS CLR_BY,
                                CASE WHEN i.EIR_ID = ' ' THEN ' '
                                  ELSE NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <= 1), ' ')
                                END OPERATION_METHOD,
                                i.PEB_EXP_NO AS ECN, PIB_IMP_NO AS ICN,
                                NVL((SELECT 'Y' AS Exp_Custom_Check
                                     FROM THU_TUC_HAI_QUAN tq
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check,
                                NVL((SELECT 'Y' AS Exp_Custom_Check
                                     FROM THU_TUC_HAI_QUAN tq
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check,
                                NVL((SELECT 'Y' AS Exp_Custom_Check
                                     FROM CUSTOM_CLEARANCE tq
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check,
                                CASE WHEN i.Item_key>0 THEN TOPOVN.GET_ITEM_COMMODITY(i.Item_key) ELSE ' ' END as COMMODITY,
                                CASE WHEN i.ImoUno != '-' THEN i.ImoUno ELSE '' END ImoUno
                            FROM
                            ( SELECT
                                i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.AGENT, i.CUST_RELEASE_MARK,
                                i.BILL_OF_LADING, i.BOOK_NO,
                                i.ITEM_KEY,
                                i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,
                                i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT,
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO,
                                i.hist_flg, i.RELEASE_NO,
                                i.Owner,
                                i.EIR_ID,
                                i.PEB_EXP_NO,
                                i.PIB_IMP_NO,
                                LISTAGG(trim(d.dgs_class) || '-' || trim(d.un_no) , ';') WITHIN GROUP (ORDER BY d.dgs_class , d.un_no ) AS ImoUno
                              FROM item i LEFT JOIN item_dangerous d ON (d.item_key = i.Item_key)
                              GROUP BY
                                i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.AGENT, i.CUST_RELEASE_MARK,
                                i.BILL_OF_LADING, i.BOOK_NO, i.ITEM_KEY,i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car,
                                i.dep_ts, i.dep_by, i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT,
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                i.RELEASE_NO,
                                i.Owner,
                                i.EIR_ID,
                                i.PEB_EXP_NO,
                                i.PIB_IMP_NO
                            ) i
                            INNER JOIN item_location il ON (i.item_key = il.item_key)
                            LEFT JOIN ITEM_SPECIAL_HANDLING ish ON (ish.ITEM_KEY = i.ITEM_KEY)
                            where il.stk_pch = 'C' and(i.hist_flg = ' ' or i.hist_flg = 'N')
                                And i.ARR_TS > to_date('01/01/1901', 'dd/MM/yyyy')
                                {strLineOper}
                                {strIso} {strCategory} {strFel}
                                {strStack}
                                and rownum < 105002
                            ORDER BY i.item_no,i.item_key");
                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCtrEnquiry(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCtrEnquiry>();
        }

        public List<ViewCtrEnquiry> GetContainerNo(string item_no)
        {
            string strSelect = "";
            try
            {
                var whereCtr = "";
                if (item_no.Trim().Length >= 11)
                {
                    whereCtr = $"  i.ITEM_NO = '{item_no.ToUpper()}' ";
                }
                else if (!item_no.IsEmpty())
                {
                    whereCtr = $"  i.ITEM_NO like '%{item_no.ToUpper()}%' ";
                }
                strSelect = $@"SELECT i.item_no,i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, i.SPECIAL_HDL_CODE, i.AGENT,
                                i.BILL_OF_LADING, i.BOOK_NO, 
                                GETSEALNO(i.ITEM_KEY, 'ITEM') AS SEAL_NO_CURRENT,
                                GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS,i.item_type, i.arr_car,i.arr_ts, i.arr_by,
                                i.dep_car, i.dep_ts, i.dep_by, i.item_key,  
                                i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg, 
                                i.RELEASE_NO, il.STK_REF, il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner, 
                                NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <= 1), ' ') AS CLR_BY, 
                                CASE WHEN i.EIR_ID = ' ' THEN ' ' 
                                  ELSE NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <= 1), ' ') 
                                END OPERATION_METHOD, 
                                i.PEB_EXP_NO AS ECN, PIB_IMP_NO AS ICN,
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check, 
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check, 
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM CUSTOM_CLEARANCE tq 
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check,
                                CASE WHEN i.Item_key>0 THEN TOPOVN.GET_ITEM_COMMODITY(i.Item_key) ELSE ' ' END as COMMODITY,
                                CASE WHEN i.ImoUno != '-' THEN i.ImoUno ELSE '' END ImoUno
                            FROM
                              (SELECT  
                                i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, i.SPECIAL_HDL_CODE, i.AGENT,
                                i.BILL_OF_LADING, i.BOOK_NO, 
                                i.ITEM_KEY,
                                i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                i.RELEASE_NO, 
                                i.Owner, 
                                i.EIR_ID,
                                i.PEB_EXP_NO,
                                i.PIB_IMP_NO,
                                LISTAGG(trim(d.dgs_class) || '-' || trim(d.un_no) , ';') WITHIN GROUP (ORDER BY d.dgs_class , d.un_no ) AS ImoUno
                              FROM item i LEFT JOIN item_dangerous d ON (d.item_key = i.Item_key)
                              WHERE {whereCtr}
                              GROUP BY 
                                       i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.SPECIAL_HDL_CODE, i.AGENT, i.CUST_RELEASE_MARK,
                                        i.BILL_OF_LADING, i.BOOK_NO, 
                                        i.ITEM_KEY,
                                        i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                        i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                        i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                        i.RELEASE_NO, 
                                        i.Owner, 
                                        i.EIR_ID,
                                        i.PEB_EXP_NO,
                                        i.PIB_IMP_NO
                                ) i
                           ,item_location il 
                            where i.item_key = il.item_key and il.stk_pch = 'C'
                                And i.ARR_TS > to_date('01/01/1901', 'dd/MM/yyyy') 
                            ORDER BY i.item_no,i.item_key";
                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCtrEnquiry(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCtrEnquiry>();
        }

        public List<ViewCtrEnquiry> GetListContainer(string lstCtrNos, int iday)
        {
            string strSelect = "";
            try
            {
                if (lstCtrNos.Trim() == "")
                {
                    return new List<ViewCtrEnquiry>();
                }
                lstCtrNos = lstCtrNos.Replace(",", "','");
                strSelect = string.Format($@"SELECT i.item_no,i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.SPECIAL_HDL_CODE, i.AGENT,
                                i.CUST_RELEASE_MARK, i.BILL_OF_LADING, i.BOOK_NO, 
                                GETSEALNO(i.ITEM_KEY, 'ITEM') AS SEAL_NO_CURRENT,
                                GET_ITEM_REMARKS(i.ITEM_KEY, 'N', 'N') REMARKS,i.item_type, i.arr_car,i.arr_ts, i.arr_by,
                                i.dep_car, i.dep_ts, i.dep_by, i.item_key,  
                                i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                i.RELEASE_NO, 
                                il.STK_REF, il.STK_CLASS,il.STACK,il.X,il.Y,il.Z,i.Owner, 
                                NVL((SELECT CLR_BY FROM ITEM_STOPS its WHERE its.ITEM_KEY = i.Item_Key AND ROWNUM <= 1), ' ') AS CLR_BY, 
                                CASE WHEN i.EIR_ID = ' ' THEN ' ' 
                                  ELSE NVL((SELECT OPERATION_METHOD FROM PREGATE_TRANSACT p WHERE i.EIR_ID = p.EIR_ID AND ROWNUM <= 1), ' ') 
                                END OPERATION_METHOD, 
                                i.PEB_EXP_NO AS ECN, PIB_IMP_NO AS ICN,
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang = 'X' AND ROWNUM <= 1), ' ') Exp_Custom_Check, 
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM THU_TUC_HAI_QUAN tq 
                                     WHERE tq.ITEM_KEY = i.ITEM_KEY AND tq.Loai_Hang != 'X' AND ROWNUM <= 1), ' ') Imp_Custom_Check, 
                                NVL((SELECT 'Y' AS Exp_Custom_Check FROM CUSTOM_CLEARANCE tq 
                                      WHERE tq.ITEM_KEY = i.ITEM_KEY AND ROWNUM <= 1), ' ') C80_Custom_Check ,
                                CASE WHEN i.ImoUno != '-' THEN i.ImoUno ELSE '' END ImoUno
                            FROM 
                              (SELECT  
                                i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.CUST_RELEASE_MARK, i.SPECIAL_HDL_CODE, i.AGENT,
                                i.BILL_OF_LADING, i.BOOK_NO, 
                                i.ITEM_KEY,
                                i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg,
                                i.RELEASE_NO, 
                                i.Owner, 
                                i.EIR_ID,
                                i.PEB_EXP_NO,
                                i.PIB_IMP_NO,
                                LISTAGG(trim(d.dgs_class) || '-' || trim(d.un_no) , ';') WITHIN GROUP (ORDER BY d.dgs_class , d.un_no ) AS ImoUno
                              FROM item i LEFT JOIN item_dangerous d ON (d.item_key = i.Item_key)
                              WHERE i.item_no in ('{lstCtrNos}') And i.ARR_TS > SYSDATE - {iday}
                              GROUP BY 
                                       i.item_no, i.site_id, i.fel, i.category, i.length, i.height, i.gross, i.SPECIAL_HDL_CODE, i.AGENT, i.CUST_RELEASE_MARK,
                                        i.BILL_OF_LADING, i.BOOK_NO, 
                                        i.ITEM_KEY,
                                        i.item_type, i.arr_car,i.arr_ts, i.arr_by, i.dep_car, i.dep_ts, i.dep_by,  
                                        i.line_oper, i.ORG_PORT,i.LOAD_PORT,i.DISCH_PORT,i.LL_DISCH_PORT, i.FDISCH_PORT, 
                                        i.PLACE_OF_DELIVERY,i.PLACE_OF_RECEIPT,i.CPA_XRAY_REQUIRED ,i.ITEM_CLASS, i.ISO, i.hist_flg, 
                                        i.RELEASE_NO, 
                                        i.Owner, 
                                        i.EIR_ID,
                                        i.PEB_EXP_NO,
                                        i.PIB_IMP_NO
                                ) i,
                            item_location il 
                         where i.item_key = il.item_key AND il.stk_pch = 'C'
                         ORDER BY i.item_no,i.item_key");

                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCtrEnquiry(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCtrEnquiry>();
        }
        public List<ViewCustomsReport> GetExportCtrCustomsReportByVessel(string vesId)
        {
            if (vesId.IsEmpty())
            {
                return new List<ViewCustomsReport>();
            }

            string strSelect = "";
            try
            {

                strSelect = string.Format($@"
                            SELECT 'X' LOAI_HANG,
                                i.ITEM_KEY, i.ITEM_NO, i.ISO, i.ARR_TS, i.DEP_TS, i.LL_DISCH_PORT AS CANG_DEN, i.GROSS,
	                            v.VES_ID, v.VES_CD, v.VES_NAME, v.OUT_VOYAGE, cm.FULL_NAME Customer,     
	                            hq.CHI_CUC_HQ, cc.CUSTOM_SEAL_NO , i.SEAL_NO_CURRENT AS LINER_SEAL_NO , cc.REMARKS, 
                                decode(hq.ITEM_KEY, NULL, 'N', 'Y') AS IS_TLHQ,	                            
                                hq.TO_KHAI, hq.NGAY_TO_KHAI, hq.NGAY_NHAP_MAY,
                                decode(cc.ITEM_KEY, NULL, 'N', 'Y') AS IS_CLEARED_C80,
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'Y'    
	                            WHEN (TO_CHAR(i.ARR_TS ,'YYYY') = '1900') THEN 'Y' ELSE 'N' END IS_COMMUNITY,    
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'    
	                            WHEN (TO_CHAR(i.ARR_TS ,'YYYY') != '1900' AND i.HIST_FLG <> 'Y') THEN 'Y' ELSE 'N' END IS_INYARD,    
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'       
	                            WHEN (TO_CHAR(i.DEP_TS ,'YYYY') != '1900' AND i.DEP_BY = 'V') THEN 'Y' ELSE 'N' END IS_OnVessel,  
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'    
	                            WHEN (TO_CHAR(i.DEP_TS ,'YYYY') != '1900' AND i.HIST_FLG = 'Y') THEN 'Y' ELSE 'N' END IS_DEPART,    
                            CASE 
	                            WHEN vde.VES_ID IS NOT NULL AND vde.VES_ID <> cc.VES_ID AND vde.VES_TYPE = 'F'  					  
		                            THEN 'Dang tren list xuat tau: ' || vde.Site_Id || '-' || vde.VES_NAME || '-' || vde.OUT_VOYAGE   
	                            ELSE ' '  END KHAC_TAU_XUAT  
                            FROM VESSEL_DETAILS v
	                            INNER JOIN ITEM i ON i.DEP_BY = 'V' AND i.DEP_CAR = v.TFC_CODE_E 
	                            LEFT OUTER JOIN THU_TUC_HAI_QUAN hq ON hq.ITEM_KEY = i.ITEM_KEY AND hq.LOAI_HANG = 'X'
	                            LEFT OUTER JOIN CUSTOM_CLEARANCE cc ON cc.ITEM_KEY = hq.ITEM_KEY
	                            LEFT OUTER JOIN CUSTOMER cm ON cm.CUST_REG_NO = cc.CUST_REG_NO
	                            LEFT OUTER JOIN EDI_ITEM_PRE_ADVICE edi ON edi.ITEM_KEY = i.ITEM_KEY  
	                            LEFT OUTER JOIN VESSEL_DETAILS vde ON edi.VES_ID = vde.VES_ID    
                            WHERE v.SITE_ID = '{GlobalSettings.SiteId}'  AND v.VES_ID =  '{vesId}' ");

                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCustomsReport(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCustomsReport>();
        }

        public List<ViewCustomsReport> GetImportCtrCustomsReportByVessel(string vesId)
        {
            string strSelect = "";
            try
            {

                strSelect = string.Format($@"
                            SELECT 'N' LOAI_HANG,
                                i.ITEM_KEY, i.ITEM_NO, i.ISO, i.ARR_TS, i.DEP_TS, i.PLACE_OF_DELIVERY AS CANG_DEN, i.GROSS,
	                            v.VES_ID, v.VES_CD, v.VES_NAME, v.OUT_VOYAGE, hq.TEN_DN_XNK AS CUSTOMER,      
	                            hq.CHI_CUC_HQ, ' ' CUSTOM_SEAL_NO , i.SEAL_NO_CURRENT AS LINER_SEAL_NO , ' ' REMARKS, 
	                            decode(hq.ITEM_KEY, NULL, 'N', 'Y') AS IS_TLHQ,	                            
                                hq.TO_KHAI, hq.NGAY_TO_KHAI, hq.NGAY_NHAP_MAY,
                                'N' AS IS_CLEARED_C80,
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'Y'    
	                            WHEN (TO_CHAR(i.ARR_TS ,'YYYY') = '1900') THEN 'Y' ELSE 'N' END IS_COMMUNITY,    
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'    
	                            WHEN (TO_CHAR(i.ARR_TS ,'YYYY') != '1900' AND i.HIST_FLG <> 'Y') THEN 'Y' ELSE 'N' END IS_INYARD,    
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'       
	                            WHEN (TO_CHAR(i.DEP_TS ,'YYYY') != '1900' AND i.DEP_BY = 'V') THEN 'Y' ELSE 'N' END IS_OnVessel,  
                            CASE WHEN i.ITEM_KEY IS NULL THEN 'N'    
	                            WHEN (TO_CHAR(i.DEP_TS ,'YYYY') != '1900' AND i.HIST_FLG = 'Y') THEN 'Y' ELSE 'N' END IS_DEPART,    
	                            ' ' KHAC_TAU_XUAT  
                            FROM VESSEL_DETAILS v
	                            INNER JOIN ITEM i ON i.ARR_BY = 'V' AND i.ARR_CAR = v.TFC_CODE_I 
	                            LEFT OUTER JOIN THU_TUC_HAI_QUAN hq ON hq.ITEM_KEY = i.ITEM_KEY AND hq.LOAI_HANG = 'N'
                            WHERE v.SITE_ID = '{GlobalSettings.SiteId}'  AND v.VES_ID =  '{vesId}' 
                            ");

                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCustomsReport(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCustomsReport>();
        }

        //string lsSQL = " Select ic.ROWID,CASE WHEN it.FEL IS NULL THEN 'E' ELSE it.FEL END FEL,ic.* " +
        //                    "FROM ITEM_CHARGES ic LEFT JOIN ITEM it ON it.ITEM_KEY = ic.ITEM_KEY AND ic.ITEM_KEY > 0 " +
        //                                       " INNER JOIN CHARGE_CODES cd ON ic.charge_cd=cd.charge_cd AND cd.charge_type='CTRCHARGES'";

        public List<ViewCustomsReport> GetTest(string vesId)
        {
            string strSelect = "";
            try
            {

                strSelect = string.Format($@"Select ic.ROWID,CASE WHEN it.FEL IS NULL THEN 'E' ELSE it.FEL END FEL,ic.* FROM ITEM_CHARGES ic LEFT JOIN ITEM it ON it.ITEM_KEY = ic.ITEM_KEY AND ic.ITEM_KEY > 0 INNER JOIN CHARGE_CODES cd ON ic.charge_cd=cd.charge_cd AND cd.charge_type='CTRCHARGES'");

                var queryResult = OraDatabase.ExecuteSql(strSelect);

                var result = (from r in queryResult.AsEnumerable()
                              select new ViewCustomsReport(r)).ToList();
                return result;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewCustomsReport>();
        }
        public CustomClearance GetCurrentCustomClearanceC80(int itemKey)
        {
            var sql =
                $@"SELECT *
                        FROM CUSTOM_CLEARANCE i
                            
                        WHERE i.ITEM_KEY = {itemKey}";

            var queryResult = OraDatabase.ExecuteSql(sql);

            if (queryResult.Rows.Count > 0)
            {
                return new CustomClearance(queryResult.Rows[0]);
            }

            return null;

        }
        public List<ViewCustomClearanceDto> GetListContainerByListItemNo(string lstItemNo, string vesId, string toKhai, string bookNo)
        {
            try
            {
                StringBuilder sqlW = new StringBuilder();

                if (!string.IsNullOrEmpty(vesId))
                {
                    sqlW.Append($" AND vessel_out.VES_ID = '{vesId}'");
                }

                if (!string.IsNullOrEmpty(lstItemNo.TrimEx()))
                {
                    lstItemNo = lstItemNo.Replace(",", "','");

                    sqlW.Append($" AND itm.ITEM_NO IN ('{lstItemNo}')");
                }

                if (!string.IsNullOrEmpty(toKhai))
                {

                    sqlW.Append($"AND cust_decl_all.CUST_DECL_NO = '{toKhai}'");
                }

                if (!string.IsNullOrEmpty(bookNo))
                {
                    sqlW.Append($" and itm.BOOK_NO ='{bookNo}'");
                }

                var sql = $@"
                    SELECT DISTINCT
                        itm.ITEM_KEY,
                        itm.ITEM_NO,
                        itm.FEL,
                        itm.ISO,
                        itm.CATEGORY,
                        CASE
                            WHEN cust_clr.ITEM_KEY IS NOT NULL THEN 'Y'
                            ELSE ' '
                        END AS IS_ON_CUSTOM_CLEARANCE,
                        CASE
                            WHEN itm_seal.ITEM_KEY IS NOT NULL
                                 AND itm_seal.SEAL_TYPE = 'LOP'
                                 AND EXTRACT(YEAR FROM itm_seal.SEAL_REMOVING_DATE) = 1900
                            THEN itm_seal.SEAL_NO
                            ELSE ' '
                        END AS LINESEALNO,
                        NVL(vgm_cust.CERTIFIED_WEIGHT, vgm_lop.CERTIFIED_WEIGHT) AS VGM,
                        CASE
                            WHEN custom_proc.ITEM_NO IS NOT NULL THEN custom_proc.TO_KHAI
                            ELSE ' '
                        END AS ECN_NO,
                        CASE
                            WHEN custom_proc.ITEM_KEY IS NOT NULL
                                 AND custom_proc.LOAI_HANG = 'X'
                            THEN 'Y'
                            ELSE ' '
                        END AS ISINTFCUSTOMERDECLARATION,
                        ' ' AS TOTAL_REF,
                        CASE
                            WHEN custom_proc.ITEM_KEY IS NOT NULL THEN custom_proc.CHI_CUC_HQ
                            ELSE ' '
                        END AS CHICUCHQ,
                        CASE
                            WHEN custom_office.CUCHQ IS NOT NULL THEN custom_office.CUCHQ
                            ELSE ' '
                        END AS CUCHQ,
                        CASE
                            WHEN cust_decl.ITEM_KEY IS NOT NULL THEN cust_decl.TAX_FILE_NO
                            ELSE ' '
                        END AS TAX_FILE_NO,
                        CASE
                            WHEN cust_info.TAX_FILE_NO IS NOT NULL THEN cust_info.CUST_REG_NO
                            ELSE ' '
                        END AS CUST_REG_NO,
                        CASE
                            WHEN cust_info.TAX_FILE_NO IS NOT NULL THEN cust_info.FULL_NAME
                            ELSE ' '
                        END AS FULL_NAME,
                        CASE
                            WHEN vessel_out.VES_ID IS NOT NULL THEN vessel_out.VES_CD
                            ELSE ' '
                        END AS VES_CD,
                        CASE
                            WHEN vessel_out.VES_ID IS NOT NULL THEN vessel_out.VES_NAME
                            ELSE ' '
                        END AS VES_NAME,
                        CASE
                            WHEN vessel_out.VES_ID IS NOT NULL THEN vessel_out.OUT_VOYAGE
                            ELSE ' '
                        END AS OUT_VOYAGE,
                        CASE
                            WHEN TRIM(SUBSTR(cust_clr.REMARKS, 1, 5)) = 'KDCHQ' THEN 'Y'
                            ELSE ' '
                        END AS IS_NOT_COMPARE_CUS,
                        CASE
                            WHEN ext_attr.ITEM_KEY IS NOT NULL
                                 AND ext_attr.CUSTOMS_CHECK_FLG = 'Y'
                            THEN 'Y'
                            ELSE ' '
                        END AS ISCONTEMPTY,
                        ' ' AS REMARK,
                        CASE
                            WHEN
                        itm_dangerous.ITEM_KEY IS NOT NULL THEN 'Y'
                            ELSE ' ' 
                       END AS IMO,
                         CASE 
                       		WHEN vessel_out.SITE_ID IS NOT NULL THEN
                        vessel_out.SITE_ID
                        	ELSE ' ' 
                        END AS SITE_ID,
                        CASE
                            WHEN pregate.OPERATION_METHOD IS NOT NULL 
                             THEN pregate.OPERATION_METHOD 
                            WHEN pregate.OPERATION_METHOD IS NULL 
                            AND vessel_in.VES_ID IS NOT NULL THEN 'NTAU'
                            WHEN itm_st.OPERATION_METHOD IS NOT NULL THEN itm_st.OPERATION_METHOD                            
                        END AS OPERATION_METHOD,
                        itm.LINE_OPER
                    FROM
                        ITEM itm
                        lEFT JOIN PREGATE_TRANSACT pregate ON pregate.ITEM_KEY = itm.ITEM_KEY AND pregate.R_D <>'D'
                        LEFT JOIN CUSTOM_CLEARANCE cust_clr ON cust_clr.ITEM_KEY = itm.ITEM_KEY
                        LEFT JOIN ITEM_SEAL itm_seal ON itm_seal.ITEM_KEY = itm.ITEM_KEY
                            AND itm_seal.CRT_TS = (
                                SELECT MAX(s2.CRT_TS)
                                FROM ITEM_SEAL s2
                                WHERE s2.ITEM_KEY = itm.ITEM_KEY
                                  AND s2.SEAL_TYPE = 'LOP'
                                  AND EXTRACT(YEAR FROM s2.SEAL_REMOVING_DATE) = 1900
                            )
                            AND EXTRACT(YEAR FROM itm_seal.SEAL_REMOVING_DATE) = 1900
                        LEFT JOIN ITEM_VGM vgm_lop ON vgm_lop.ITEM_KEY = itm.ITEM_KEY
                            AND vgm_lop.LOAD_LIST_FLG <> 'Y'
                            AND vgm_lop.RECEIVE_FROM = 'LOP'
                        LEFT JOIN ITEM_VGM vgm_cust ON vgm_cust.ITEM_KEY = itm.ITEM_KEY
                            AND vgm_cust.LOAD_LIST_FLG <> 'Y'
                            AND vgm_cust.RECEIVE_FROM = 'CUST'
                        LEFT JOIN THU_TUC_HAI_QUAN custom_proc ON custom_proc.ITEM_KEY = itm.ITEM_KEY
                            AND custom_proc.LOAI_HANG = 'X'
                        LEFT JOIN CHICUCHQ custom_office ON custom_office.CHICUCHQ = custom_proc.CHI_CUC_HQ
                        LEFT JOIN CUSTOMER_CUSTOMS_DECLARATION cust_decl ON cust_decl.ITEM_KEY = custom_proc.ITEM_KEY
                            AND custom_proc.TO_KHAI = cust_decl.CUST_DECL_NO
                        LEFT JOIN CUSTOMER_CUSTOMS_DECLARATION cust_decl_all ON cust_decl_all.ITEM_KEY = itm.ITEM_KEY
                        LEFT JOIN CUSTOMER cust_info ON cust_info.CUST_REG_NO = cust_decl.TAX_FILE_NO
                        LEFT JOIN VESSEL_DETAILS vessel_out ON vessel_out.VES_CD = itm.EXIT_VES_CD
                                AND vessel_out.OUT_VOYAGE=itm.EXIT_VOYAGE
                        LEFT JOIN VESSEL_DETAILS vessel_in ON vessel_in.TFC_CODE_I = itm.ARR_CAR
                            AND vessel_in.VES_TYPE <> 'B'
                        LEFT JOIN ITEM_EXTENSION_ATTRIBUTES ext_attr ON ext_attr.ITEM_KEY = itm.ITEM_KEY
                       LEFT JOIN ITEM_DANGEROUS itm_dangerous ON itm_dangerous.ITEM_KEY = itm.ITEM_KEY
                        LEFT JOIN ITEM_STRIP_STUFF itm_st ON itm_st.ITEM_KEY = itm.ITEM_KEY
                            
                    WHERE
                        itm.HIST_FLG <> 'Y'

                        AND EXTRACT(YEAR FROM itm.ARR_TS) > 1900
                            
                   {sqlW} ";

                var queryresult = OraDatabase.ExecuteSql(sql);

                List<ViewCustomClearanceDto> dataList = new List<ViewCustomClearanceDto>();

                foreach (DataRow row in queryresult.Rows)
                {
                    var dto = new ViewCustomClearanceDto
                    {
                        ItemKey = row["ITEM_KEY"].ToString().Trim(),
                        ItemNo = row["ITEM_NO"].ToString().Trim(),
                        Fel = row["FEL"].ToString().Trim(),
                        Iso = row["ISO"].ToString().Trim(),
                        Category = row["CATEGORY"].ToString().Trim(),
                        IsOnCustomClearance = row["IS_ON_CUSTOM_CLEARANCE"].ToString().Trim(),
                        isIntfCustomerDeclaration=row["ISINTFCUSTOMERDECLARATION"].ToString().Trim(),
                        LineSealNo = row["LINESEALNO"].ToString().Trim(),
                        Vgm = row.Field<decimal?>("VGM"),
                        ToKhai = row["ECN_NO"].ToString().Trim(),
                        TotalRef = row["TOTAL_REF"].ToString().Trim(),
                        Cuchq = row["CUCHQ"].ToString().Trim(),
                        ChiCucHq = row["CHICUCHQ"].ToString().Trim(),
                        TaxFileNo = row["TAX_FILE_NO"].ToString().Trim(),
                        CustRegNo = row["CUST_REG_NO"].ToString().Trim(),
                        FullName = row["FULL_NAME"].ToString().Trim(),
                        VesCd = row["VES_CD"].ToString().Trim(),
                        VesName = row["VES_NAME"].ToString().Trim(),
                        OutVoyage = row["OUT_VOYAGE"].ToString().Trim(),
                        IsNotCompareCus = row["IS_NOT_COMPARE_CUS"].ToString().Trim(),
                        Remark = row["REMARK"].ToString().Trim(),
                        IsContEmpty = row["ISCONTEMPTY"].ToString().Trim(),
                        OperationMethod = row["OPERATION_METHOD"].ToString().Trim(),
                        Imo = row["IMO"].ToString().Trim(),                      
                        SiteId = row["SITE_ID"].ToString().Trim(),
                       LineOper= row["LINE_OPER"].ToString().Trim()
                    };

                    dataList.Add(dto);
                }

                return dataList;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return new List<ViewCustomClearanceDto>();
        }

        public DataTable GetItemStopsExistSysCodeByItemKey(int itemKey, string codeTp)
        {
            try
            {
                string sql = $@"
                            SELECT * FROM ITEM_STOPS stop
                            INNER JOIN SYS_CODES s ON s.CODE_TP = '{codeTp}' AND s.CODE_REF = stop.STOP_CD
                            WHERE stop.ITEM_KEY = {itemKey} AND EXTRACT(YEAR FROM stop.CLR_TS) = 1900 ";

                DataTable queryResult = OraDatabase.ExecuteSql(sql);

                if (queryResult.Rows.Count <= 0)
                {
                    sql = $@"SELECT *
                            FROM ITEM_STOPS stop INNER
                                JOIN CONFIG_STOPCODE cs ON 
                                    cs.STOP_CODE = stop.STOP_CD 
                                    AND (cs.STOP_FLG = 'Y' OR cs.STOP_SCREEN_IDS = 'C80')
                            WHERE stop.ITEM_KEY = {itemKey} AND EXTRACT(YEAR FROM stop.CLR_TS) = 1900 ";

                    queryResult = OraDatabase.ExecuteSql(sql);
                }

                return queryResult;
            }
            catch (Exception ex)
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return new DataTable();
        }

        public List<Item> GetItemByItemKeysAndBookNo(List<int> itemKeys, string bookingNo)
        {
            var tosItemKeys = itemKeys.Select(x => x.ToString()).Aggregate((i1, i2) => $"{i1},{i2}");
            var sql = $@"SELECT * FROM ITEM WHERE ITEM_KEY IN ({tosItemKeys})";
            if (bookingNo != "")
                sql += $" AND BOOK_NO = '{bookingNo}'";
            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new Item(d)).ToList();

            return result;
        }

        public List<ItemLocation> GetItemLocationByItemKeys(List<int> itemKeys)
        {
            var tosItemKeys = itemKeys.Select(x => x.ToString()).Aggregate((i1, i2) => $"{i1},{i2}");
            var sql = $@"SELECT * FROM ITEM_LOCATION WHERE ITEM_KEY IN ({tosItemKeys}) AND STK_PCH = 'C'";
            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new ItemLocation(d)).ToList();

            return result;
        }

        public List<Operator> GetOperatorsByEmployeeNo(List<string> employeeNos)
        {
            if (employeeNos == null)
                return new List<Operator>();

            var tosEmployeeNos = "";
            employeeNos.ForEach(x =>
            {
                if (tosEmployeeNos != "")
                    tosEmployeeNos += ",";
                tosEmployeeNos += $"'{x}'";
            });

            var sql = $@"SELECT * FROM OPERATOR WHERE EMPLOYEE_NO IN ({tosEmployeeNos})";
            var queryResult = OraDatabase.ExecuteSql(sql);
            var result = (from d in queryResult.AsEnumerable()
                          select new Operator(d)).ToList();

            return result;
        }

        public DataTable GetContainerStuffStrips(List<string> itemNos, List<string> operationMethod)
        {
            try
            {
                itemNos = itemNos.Select(e => $"'{e.TrimEx()}'").ToList();
                string strItemNos = string.Join(GlobalSettings.Comma, itemNos);

                operationMethod = operationMethod.Select(e => $"'{e.TrimEx()}'").ToList();
                string strOperMethod = string.Join(GlobalSettings.Comma, operationMethod);

                string sql = $@"
                    SELECT i.ITEM_KEY
                        , I.ITEM_NO
                        , I.CATEGORY
                        , I.ISO
                        , I.FEL
                        , I.AGENT
                        , I.SLOT_CODE
                        , I.LINE_OPER
                        , I.BOOK_NO
                        , I.GROSS
                        , I.ARR_BY
                        , I.ARR_CAR
	                    , CASE WHEN 
                         	I.ARR_TS > TO_DATE('31/12/1900 23:00','dd/mm/yyyy hh24:mi')
                          THEN  TO_CHAR(I.ARR_TS, 'dd/mm/yyyy hh24:mi')  ELSE ' ' END ARR_TS
                        , I.OWNER
                        , I.LL_DISCH_PORT
                        , I.DEP_BY
                        , I.DEP_CAR
                        , TRIM(S.EIR_ID) EIR_ID
                        , V.VES_NAME
                        , V.OUT_VOYAGE
                    FROM ITEM I
                        JOIN ITEM_STRIP_STUFF S ON I.ITEM_KEY = S.ITEM_KEY
                        JOIN PREGATE_TRANSACT P ON S.EIR_ID = P.EIR_ID
                        LEFT JOIN VESSEL_DETAILS V ON I.DEP_CAR = V.TFC_CODE_E AND I.DEP_BY = 'V'
                    WHERE I.ITEM_NO IN ({strItemNos}) 
                        AND S.OPERATION_METHOD IN ({strOperMethod})
                        AND I.HIST_FLG <> 'Y'
                        AND P.HIST_FLG = 'Y'
                ";
                DataTable queryResult = OraDatabase.ExecuteSql(sql);
                return queryResult;
            }
            catch (Exception ex) when (!(ex is OutOfMemoryException || ex is StackOverflowException || ex is ThreadAbortException))
            {
                DaExceptionHandler.HandleException(ref ex);
            }
            return new DataTable();
        }
        public int CountContByVesId(string vesId)
        {
            string strSql = "";
            try
            {
                strSql = string.Format(@"SELECT count(0) FROM EDI_ITEM_PRE_ADVICE WHERE VES_ID ='{0}'", vesId);

                var queryResult = OraDatabase.ExecuteScalar(strSql).CheckIntEx();

                return queryResult;
            }
            catch (Exception ex) when (!(ex is OutOfMemoryException || ex is StackOverflowException || ex is ThreadAbortException))
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return 0;
        }
        public int CountContCustomClearanceByEcnNo(string ecnNo, string customDep, string subCustomDep, string custRegNo)
        {
            var sql = $@"
                      SELECT count(*)
                      FROM CUSTOM_CLEARANCE c
                      WHERE c.ECN_NO = '{ecnNo}' AND c.CUSTOM_DEP = '{customDep}' AND c.SUB_CUSTOM_DEP = '{subCustomDep}' AND c.CUST_REG_NO = '{custRegNo}'
                        ";

            var count = OraDatabase.ExecuteScalar(sql).CheckIntEx();
            return count;
        }
        public ItemLocation GetLastTrailerLocation(int itemKey)
        {
            try
            {
                var sql = $@"SELECT  loc.*
                            ,LAG(STK_CLASS, 1, ' ') OVER (ORDER BY EXEC_TS) AS STK_CLASS_PREV
                            ,LEAD (STK_CLASS, 1, ' ') OVER (ORDER BY EXEC_TS) AS STK_CLASS_NEXT
                        FROM ITEM_LOCATION loc
                        WHERE loc.ITEM_KEY = {itemKey} 
                            AND loc.ID <> -1
                        ORDER BY  loc.EXEC_TS ASC";

                DataTable queryResult = OraDatabase.ExecuteSql(sql);
                var result = (from record in queryResult.AsEnumerable()
                              let prev = record.Field<string>("STK_CLASS_PREV").TrimEx()
                              let next = record.Field<string>("STK_CLASS_NEXT").TrimEx()
                              where record.Field<string>("STK_CLASS").TrimEx() == ItemLocationStkClass.Trailer
                              && prev == ItemLocationStkClass.Yard
                              && (next.IsNullOrEmpty() || next == ItemLocationStkClass.Vessel)
                              select new ItemLocation(record)).LastOrDefault();

                return result;
            }
            catch (Exception ex) when (!(ex is OutOfMemoryException || ex is StackOverflowException || ex is ThreadAbortException))
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new ItemLocation();
        }
        public List<ViewReeferMonitor> GetReeferInfoRecDelByItemKey(int itemKey, string RD = "")
        {
            try
            {
                string sql =
                    $@"
                            SELECT
    	                       i.ITEM_KEY , i.ITEM_NO, i.LINE_OPER, i.ARR_CAR, i.ARR_BY,i.Arr_Ts, ivd.VES_NAME ARR_VES_NAME, i.DEP_BY
                               , i.DEP_CAR, evd.VES_NAME DEP_VES_NAME, decode(i.HIST_FLG, 'Y', 'Y', ' ') HIST_FLG, i.ISO, nvl(rm.INSPECT_TS
                               , GET_NULL_DATE()) INSPECT_TS ,rm.TEMP, rm.TEMP_TYPE, rm.COMMENTS, rm.OPER_NAME, nvl(rm.CRT_TS
                               , GET_NULL_DATE()) CRT_TS, rm.IS_CONNECTED, rm.STATE, cs.REEFER_MODEL, nvl(cs.REEFER_PRODUCED_TS
                               , GET_NULL_DATE()) REEFER_PRODUCED_TS, nvl(cs.PRODUCED_TS, GET_NULL_DATE ) PRODUCED_TS
                               , CASE WHEN i.MAX_GROSS = 0 THEN cs.MAX_GROSS_WEIGHT ELSE i.MAX_GROSS END  AS MAX_GROSS_WEIGHT, ir.SETTING_TEMP
                               , ir.TEMP_UNIT, ir.VENT_VOLUMETRIC_RATE, ir.VENT_VOLUMETRIC_UNIT, i.COMMOD, nvl(ir.PLUG_TS, GET_NULL_DATE) PLUG_TS
                               , nvl(ir.UNPLUG_TS, GET_NULL_DATE()) UNPLUG_TS, ir.NO_PLUGIN_REQUIRED, ir.MIN_TEMP, ir.MAX_TEMP, i.FEL
                               , ir.o2, ir.co2, ir.frozen_temp, ir.ingate_temp, ir.humidity, ir.ventilation, ir.outgate_temp
                               , rm.POST_PAID_FLG, rm.CANCELED_REASON, rm.DATASOURCE, i.AGENT, rm.CONFIRMED_FLG,rm.R_D

                            FROM ITEM i
                            LEFT JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
                            LEFT JOIN  REEFER_MONITOR rm ON rm.ITEM_KEY = i.ITEM_KEY AND (rm.R_D = '{RD}' OR rm.R_D = ' ')
                            LEFT JOIN VESSEL_DETAILS ivd ON ivd.TFC_CODE_I = i.ARR_CAR
                            LEFT JOIN	VESSEL_DETAILS evd ON evd.TFC_CODE_E = i.DEP_CAR
                            LEFT JOIN CONTAINER_STRUCTURE cs ON cs.ITEM_NO = i.ITEM_NO
                            WHERE substr(i.ISO, 3, 1) IN ('R', '3', '4')  AND  to_char(i.ARR_TS, 'yyyy') <> '1900'
                            AND i.item_key={itemKey} ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewReeferMonitor(d);

                return result.ToList();
            }
            catch (Exception ex) when (!(ex is OutOfMemoryException || ex is StackOverflowException || ex is ThreadAbortException))
            {
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewReeferMonitor>();
        }
        public List<ViewReeferMonitor> GetReeferInfoByItemKey(int itemKey)
        {
            try
            {

                string sql =
                    $@"
                            SELECT
    	                       i.ITEM_KEY , i.ITEM_NO, i.LINE_OPER, i.ARR_CAR, i.ARR_BY,i.Arr_Ts, ivd.VES_NAME ARR_VES_NAME, i.DEP_BY
                               , i.DEP_CAR, evd.VES_NAME DEP_VES_NAME, decode(i.HIST_FLG, 'Y', 'Y', ' ') HIST_FLG, i.ISO, nvl(rm.INSPECT_TS
                               , GET_NULL_DATE()) INSPECT_TS ,rm.TEMP, rm.TEMP_TYPE, rm.COMMENTS, rm.OPER_NAME, nvl(rm.CRT_TS
                               , GET_NULL_DATE()) CRT_TS, rm.IS_CONNECTED, rm.STATE, cs.REEFER_MODEL, nvl(cs.REEFER_PRODUCED_TS
                               , GET_NULL_DATE()) REEFER_PRODUCED_TS, nvl(cs.PRODUCED_TS, GET_NULL_DATE ) PRODUCED_TS
                               , CASE WHEN i.MAX_GROSS = 0 THEN cs.MAX_GROSS_WEIGHT ELSE i.MAX_GROSS END  AS MAX_GROSS_WEIGHT, ir.SETTING_TEMP
                               , ir.TEMP_UNIT, ir.VENT_VOLUMETRIC_RATE, ir.VENT_VOLUMETRIC_UNIT, i.COMMOD, nvl(ir.PLUG_TS, GET_NULL_DATE) PLUG_TS
                               , nvl(ir.UNPLUG_TS, GET_NULL_DATE()) UNPLUG_TS, ir.NO_PLUGIN_REQUIRED, ir.MIN_TEMP, ir.MAX_TEMP, i.FEL
                               , ir.o2, ir.co2, ir.frozen_temp, ir.ingate_temp, ir.humidity, ir.ventilation, ir.outgate_temp
                               , rm.POST_PAID_FLG, rm.CANCELED_REASON, rm.DATASOURCE, i.AGENT, rm.CONFIRMED_FLG,rm.R_D

                            FROM ITEM i
                            LEFT JOIN ITEM_REEFER ir ON ir.ITEM_KEY = i.ITEM_KEY
                            LEFT JOIN  REEFER_MONITOR rm ON rm.ITEM_KEY = i.ITEM_KEY
                            LEFT JOIN VESSEL_DETAILS ivd ON ivd.TFC_CODE_I = i.ARR_CAR
                            LEFT JOIN	VESSEL_DETAILS evd ON evd.TFC_CODE_E = i.DEP_CAR
                            LEFT JOIN CONTAINER_STRUCTURE cs ON cs.ITEM_NO = i.ITEM_NO
                            WHERE substr(i.ISO, 3, 1) IN ('R', '3', '4')  AND  to_char(i.ARR_TS, 'yyyy') <> '1900'
                            AND i.item_key={itemKey} ";

                var queryResult = OraDatabase.ExecuteSql(sql);

                var result = from d in queryResult.AsEnumerable()
                             select new ViewReeferMonitor(d);

                return result.ToList();
            }
            catch (Exception ex) when (!(ex is OutOfMemoryException || ex is StackOverflowException || ex is ThreadAbortException))
            {
                //Write log
                DaExceptionHandler.HandleException(ref ex);
            }

            return new List<ViewReeferMonitor>();
        }
    }
}
