/* Copyright (c) Business Objects 2006. All rights reserved. */

// LOCALIZATION STRING

// Strings for calendar.js and calendar_param.js
var L_Today     = "Oggi";
var L_January   = "Gennaio";
var L_February  = "Febbraio";
var L_March     = "Marzo";
var L_April     = "Aprile";
var L_May       = "Maggio";
var L_June      = "Giugno";
var L_July      = "Luglio";
var L_August    = "Agosto";
var L_September = "Settembre";
var L_October   = "Ottobre";
var L_November  = "Novembre";
var L_December  = "Dicembre";
var L_Su        = "Do";
var L_Mo        = "Lu";
var L_Tu        = "Ma";
var L_We        = "Me";
var L_Th        = "Gi";
var L_Fr        = "Ve";
var L_Sa        = "Sa";

// Strings for prompts.js and prompts_param.js
var L_YYYY          = "aaaa";
var L_MM            = "mm";
var L_DD            = "gg";
var L_BadNumber     = "Questo parametro \u00E8 di tipo \"Numero\" e pu\u00F2 contenere solo un simbolo negativo, cifre (\"0-9\"), simboli di raggruppamento cifre o un simbolo decimale. Correggere il valore del parametro immesso.";
var L_BadCurrency   = "Questo parametro \u00E8 di tipo \"Valuta\" e pu\u00F2 contenere solo un segno meno, cifre (\"0-9\"), simboli di raggruppamento cifre o un simbolo decimale. Correggere il valore del parametro immesso.";
var L_BadDate       = "Questo parametro \u00E8 di tipo \"Data\" e deve essere nel formato \"%1\" dove \"aaaa\" \u00E8 l\'anno a quattro cifre, \"mm\" \u00E8 il mese (ad esempio Gennaio = 1), e \"gg\" \u00E8 il giorno del mese.";
var L_BadDateTime   = "Questo parametro \u00E8 di tipo \"DataOra\" e il formato corretto \u00E8 \"%1 hh:mm:ss\". \"aaaa\" \u00E8 l\'anno a quattro cifre, \"mm\" \u00E8 il mese (ad esempio Gennaio = 1), \"gg\" \u00E8 il giorno del mese, \"hh\" \u00E8 l\'ora in formato a 24 ore, \"mm\" sono i minuti e \"ss\" sono i secondi.";
var L_BadTime       = "Questo parametro \u00E8 di tipo \"Ora\" e deve essere nel formato \"hh,mm,ss\" dove \"hh\" \u00E8 l\'ora in formato a 24 ore, \"mm\" sono i minuti e \"ss\" sono i secondi.";
var L_NoValue       = "Nessun valore";
var L_BadValue      = "Per impostare \"Nessun valore\", \u00E8 necessario impostare i valori Da e A su \"Nessun valore\".";
var L_BadBound      = "Impossibile impostare \"Nessun limite inferiore\" insieme a \"Nessun limite superiore\".";
var L_NoValueAlready = "Questo parametro \u00E8 gi\u00E0 impostato su \"Nessun valore\". Rimuovere \"Nessun valore\" prima di aggiungere altri valori.";
var L_RangeError    = "L\'inizio dell\'intervallo non pu\u00F2 essere maggiore della fine dell\'intervallo.";
var L_NoDateEntered = "\u00C8 necessario immettere una data.";
var L_Empty         = "Immettere un valore.";

// Strings for filter dialog
var L_closeDialog="Chiudi finestra";

var L_SetFilter = "Imposta filtro";
var L_OK        = "OK";
var L_Cancel    = "Annulla";

 /* Crystal Decisions Confidential Proprietary Information */
