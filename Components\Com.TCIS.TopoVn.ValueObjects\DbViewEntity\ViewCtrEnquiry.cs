using System;
using System.Data;

namespace Com.TCIS.TopoVn.ValueObjects
{
    /// <summary>
    /// Table : VIEW_ITEM
    /// </summary>
    [ScalarClass(DbTableName = "VIEW_CONTAINER", DisplayName = "Tồn Bãi")]
    public class ViewCtrEnquiry : DbView
    {
        #region Properties

        /// <summary>
        /// Column : ITEM NO
        /// </summary>
        [ScalarProperty(DbColumnName = "ITEM_NO", DbDataLength = 12, DisplayName = "Item No")]
        public string ItemNo { get; set; }

        /// <summary>
        /// Column : SITE_ID
        /// </summary>
        [ScalarProperty(DbColumnName = "SITE_ID", DbDataLength = 6, DisplayName = "Site ID")]
        public string SiteId { get; set; }

        /// <summary>
        /// Column : FEL
        /// </summary>
        [ScalarProperty(DbColumnName = "FEL", DbDataLength = 1, DisplayName = "FE")]
        public string Fel { get; set; }

        /// <summary>
        /// Column : CATEGORY
        /// </summary>
        [ScalarProperty(DbColumnName = "CATEGORY", DbDataLength = 1, DisplayName = "Category")]
        public string Category { get; set; }

        /// <summary>
        /// Column : GROSS
        /// </summary>
        [ScalarProperty(DbColumnName = "GROSS", DbDataLength = 12, DisplayName = "Gross")]
        public string Gross { get; set; }

        /// <summary>
        /// Column : ECN
        /// </summary>
        [ScalarProperty(DbColumnName = "ECN", DisplayName = "ECN")]
        public string Ecn { get; set; }

        /// <summary>
        /// Column : ICN
        /// </summary>
        [ScalarProperty(DbColumnName = "ICN", DisplayName = "ICN")]
        public string Icn { get; set; }

        /// <summary>
        /// Column : BILL_OF_LADING
        /// </summary>
        [ScalarProperty(DbColumnName = "BILL_OF_LADING", DbDataLength = 20, DisplayName = "Bill Off Lading")]
        public string BillOflading { get; set; }

        /// <summary>
        /// Column : SEAL_NO_CURRENT
        /// </summary>
        [ScalarProperty(DbColumnName = "SEAL_NO_CURRENT", DbDataLength = 20, DisplayName = "Seal No Current")]
        public string SealNoCurrent { get; set; }

        /// <summary>
        /// Column : BOOK_NO
        /// </summary>
        [ScalarProperty(DbColumnName = "BOOK_NO", DbDataLength = 20, DisplayName = "Book No")]
        public string BookNo { get; set; }

        /// <summary>
        /// Column : REMARKS
        /// </summary>
        [ScalarProperty(DbColumnName = "REMARKS", DbDataLength = 4000, DisplayName = "Remarks")]
        public string Remarks { get; set; }

        /// <summary>
        /// Column : SPECIAL_HDL_CODE
        /// </summary>
        [ScalarProperty(DbColumnName = "SPECIAL_HDL_CODE", DbDataLength = 4000, DisplayName = "SpecialHdlCode")]
        public string SpecialHdlCode { get; set; }

        /// <summary>
        /// Column : ARR_CAR
        /// </summary>
        [ScalarProperty(DbColumnName = "ARR_CAR", DbDataLength = 12, DisplayName = "Arr Car")]
        public string ArrCar { get; set; }

        /// <summary>
        /// Column : ARR_TS
        /// </summary>
        [ScalarProperty(DbColumnName = "ARR_TS", DbDataLength = 7, DisplayName = "Arr Time")]
        public DateTime ArrTs { get; set; }

        /// <summary>
        /// Column : ARR_BY
        /// </summary>
        [ScalarProperty(DbColumnName = "ARR_BY", DbDataLength = 7, DisplayName = "Arr By")]
        public string ArrBy { get; set; }

        /// <summary>
        /// Column : DEP_CAR
        /// </summary>
        [ScalarProperty(DbColumnName = "DEP_CAR", DbDataLength = 12, DisplayName = "Dep Car")]
        public string DepCar { get; set; }

        /// <summary>
        /// Column : DEP_TS
        /// </summary>
        [ScalarProperty(DbColumnName = "DEP_TS", DbDataLength = 7, DisplayName = "Dep Time")]
        public DateTime DepTs { get; set; }

        /// <summary>
        /// Column : DEP_BY
        /// </summary>
        [ScalarProperty(DbColumnName = "DEP_BY", DbDataLength = 1, DisplayName = "Dep By")]
        public string DepBy { get; set; }

        /// <summary>
        /// Column : ITEM_KEY
        /// </summary>
        [ScalarProperty(DbColumnName = "ITEM_KEY", DbDataLength = 9, DisplayName = "Item Key")]
        public int ItemKey { get; set; }

        /// <summary>
        /// Column : LINE_OPER
        /// </summary>
        [ScalarProperty(DbColumnName = "LINE_OPER", DbDataLength = 7, DisplayName = "Line Oper")]
        public string LineOper { get; set; }        
        
        /// <summary>
        /// Column : LINE_OPER
        /// </summary>
        [ScalarProperty(DbColumnName = "AGENT", DbDataLength = 7, DisplayName = "Agent")]
        public string Agent { get; set; }

        /// <summary>
        /// Column : ORG_PORT
        /// </summary>
        [ScalarProperty(DbColumnName = "ORG_PORT", DbDataLength = 5, DisplayName = "Org Port")]
        public string OrgPort { get; set; }

        /// <summary>
        /// Column : LOAD_PORT
        /// </summary>
        [ScalarProperty(DbColumnName = "LOAD_PORT", DbDataLength = 5, DisplayName = "Load Port")]
        public string Pol { get; set; }

        /// <summary>
        /// Column : DISCH_PORT
        /// </summary>
        [ScalarProperty(DbColumnName = "DISCH_PORT", DbDataLength = 5, DisplayName = "Disch Port")]
        public string Pod { get; set; }

        /// <summary>
        /// Column : LL_DISCH_PORT
        /// </summary>
        [ScalarProperty(DbColumnName = "LL_DISCH_PORT", DbDataLength = 5, DisplayName = "LL Disch Port")]
        public string LlPod { get; set; }

        /// <summary>
        /// Column : FDISCH_PORT
        /// </summary>
        [ScalarProperty(DbColumnName = "FDISCH_PORT", DbDataLength = 5, DisplayName = "FDisch Port")]
        public string Fpod { get; set; }

        /// <summary>
        /// Column : PLACE_OF_DELIVERY
        /// </summary>
        [ScalarProperty(DbColumnName = "PLACE_OF_DELIVERY", DbDataLength = 8, DisplayName = "Place Of Delivery")]
        public string PlaceOfDelivery { get; set; }

        /// <summary>
        /// Column : PLACE_OF_RECEIPT
        /// </summary>
        [ScalarProperty(DbColumnName = "PLACE_OF_RECEIPT", DbDataLength = 8, DisplayName = "Place Of Receipt")]
        public string PlaceOfReceipt { get; set; }

        /// <summary>
        /// Column : ISO
        /// </summary>
        [ScalarProperty(DbColumnName = "ISO", DbDataLength = 4, DisplayName = "ISO")]
        public string Iso { get; set; }

        /// <summary>
        /// Column : hist_flg
        /// </summary>
        [ScalarProperty(DbColumnName = "HIST_FLG", DbDataLength = 1, DisplayName = "Hist Flg")]
        public string HistFlg { get; set; }

        /// <summary>
        /// Column : RELEASE_NO
        /// </summary>
        [ScalarProperty(DbColumnName = "RELEASE_NO", DbDataLength = 20, DisplayName = "Release No")]
        public string ReleaseNo { get; set; }

        /// <summary>
        /// Column : STK_REF
        /// </summary>
        [ScalarProperty(DbColumnName = "STK_REF", DbDataLength = 1, DisplayName = "STK REF")]
        public string StkRef { get; set; }

        /// <summary>
        /// Column : STK_CLASS
        /// </summary>
        [ScalarProperty(DbColumnName = "STK_CLASS", DbDataLength = 1, DisplayName = "STK CLASS")]
        public string StkClass { get; set; }

        /// <summary>
        /// CurLocation
        /// </summary> 
        public string Location
        {
            get
            {
                return $"{StkClass} {Stack} {X} {Y} {Z}";
            }
        }

/// <summary>
/// Column : STACK
/// </summary>
[ScalarProperty(DbColumnName = "STACK", DbDataLength = 3, DisplayName = "STACK")]
        public string Stack { get; set; }

        /// <summary>
        /// Column : X
        /// </summary>
        [ScalarProperty(DbColumnName = "X", DbDataLength = 3, DisplayName = "X")]
        public string X { get; set; }

        /// <summary>
        /// Column : Y
        /// </summary>
        [ScalarProperty(DbColumnName = "Y", DbDataLength = 3, DisplayName = "Y")]
        public string Y { get; set; }

        /// <summary>
        /// Column : Z
        /// </summary>
        [ScalarProperty(DbColumnName = "Z", DbDataLength = 2, DisplayName = "Z")]
        public string Z { get; set; }

        /// <summary>
        /// Column : Owner
        /// </summary>
        [ScalarProperty(DbColumnName = "Owner", DbDataLength = 3, DisplayName = "Owner")]
        public string Owner { get; set; }

        /// <summary>
        /// Column : CLR_BY
        /// </summary>
        [ScalarProperty(DbColumnName = "CLR_BY", DbDataLength = 25, DisplayName = "CLR_BY")]
        public string ClrBy { get; set; }

        /// <summary>
        /// Column : OPERATION_METHOD
        /// </summary>
        [ScalarProperty(DbColumnName = "OPERATION_METHOD", DbDataLength = 20, DisplayName = "OPERATION_METHOD")]
        public string OperationMethod { get; set; }

        /// <summary>
        /// Column : Exp_Custom_Check
        /// </summary>
        [ScalarProperty(DbColumnName = "Exp_Custom_Check", DbDataLength = 1, DisplayName = "Exp_Custom_Check")]
        public string ExpCustomCheck { get; set; }

        /// <summary>
        /// Column : Imp_Custom_Check
        /// </summary>
        [ScalarProperty(DbColumnName = "Imp_Custom_Check", DbDataLength = 1, DisplayName = "Imp_Custom_Check")]
        public string ImpCustomCheck { get; set; }

        /// <summary>
        /// Column : C80_Custom_Check
        /// </summary>
        [ScalarProperty(DbColumnName = "C80_Custom_Check", DbDataLength = 1, DisplayName = "C80_Custom_Check")]
        public string C80CustomCheck { get; set; }

        /// <summary>
        /// Column : Commodity
        /// </summary>
        [ScalarProperty(DbColumnName = "COMMODITY")]
        public string Commodity { get; set; }

        /// <summary>
        /// ImoUno
        /// </summary>
        [ScalarProperty(DbColumnName = "ImoUno")]
        public string ImoUno { get; set; }

        /// <summary>
        /// IsDangerous
        /// </summary>
        public string IsDangerous { get { return !string.IsNullOrWhiteSpace(ImoUno) ? "Y" : string.Empty; } }

        #endregion

        /// <summary>
        /// Constructor with an DataRow object. 
        /// This function will map data in the given DataRow object to its properties
        /// </summary>
        /// <param name="row"></param>
        public ViewCtrEnquiry(DataRow row)
            : base(row)
        {
        }

        /// <summary>
        /// Constructor with non argument
        /// </summary>
        public ViewCtrEnquiry()
            : base(null)
        {
        }
    }
}
