/* Copyright (c) Business Objects 2006. All rights reserved. */

var L_bobj_crv_MainReport = "\uC8FC \uBCF4\uACE0\uC11C";
// Viewer Toolbar tooltips
var L_bobj_crv_FirstPage = "\uCCAB \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_PrevPage = "\uC774\uC804 \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_NextPage = "\uB2E4\uC74C \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_LastPage = "\uB9C8\uC9C0\uB9C9 \uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_ParamPanel = "\uB9E4\uAC1C \uBCC0\uC218 \uCC3D";
var L_bobj_crv_Parameters = "\uB9E4\uAC1C \uBCC0\uC218";
var L_bobj_crv_GroupTree = "\uADF8\uB8F9 \uD2B8\uB9AC";
var L_bobj_crv_DrillUp = "\uB4DC\uB9B4\uC5C5";
var L_bobj_crv_Refresh = "\uBCF4\uACE0\uC11C \uC0C8\uB85C \uACE0\uCE68";
var L_bobj_crv_Zoom = "\uD655\uB300/\uCD95\uC18C";
var L_bobj_crv_PageNav = "\uD398\uC774\uC9C0 \uD0D0\uC0C9";
var L_bobj_crv_SelectPage = "\uD398\uC774\uC9C0\uB85C \uC774\uB3D9";
var L_bobj_crv_SearchText = "\uD14D\uC2A4\uD2B8 \uAC80\uC0C9";
var L_bobj_crv_Export = "\uBCF4\uACE0\uC11C \uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_Print = "\uBCF4\uACE0\uC11C \uC778\uC1C4";
var L_bobj_crv_TabList = "\uD0ED \uBAA9\uB85D";
var L_bobj_crv_Close = "\uB2EB\uAE30";
var L_bobj_crv_Logo=  "Business Objects \uB85C\uACE0";
var L_bobj_crv_FileMenu = "\uD30C\uC77C \uBA54\uB274";

var L_bobj_crv_File = "\uD30C\uC77C";

var L_bobj_crv_Show = "\uD45C\uC2DC";
var L_bobj_crv_Hide = "\uC228\uAE30\uAE30";

var L_bobj_crv_Find = "\uCC3E\uAE30...";
var L_bobj_crv_of = "%2 \uC911 %1"; // Example: Page "1 of 3"

var L_bobj_crv_submitBtnLbl = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ActiveXPrintDialogTitle = "\uC778\uC1C4";
var L_bobj_crv_PDFPrintDialogTitle = "PDF\uB85C \uC778\uC1C4";
var L_bobj_crv_PrintRangeLbl = "\uD398\uC774\uC9C0 \uBC94\uC704:";
var L_bobj_crv_PrintAllLbl = "\uBAA8\uB4E0 \uD398\uC774\uC9C0";
var L_bobj_crv_PrintPagesLbl = "\uD398\uC774\uC9C0 \uC120\uD0DD";
var L_bobj_crv_PrintFromLbl = "\uC2DC\uC791:";
var L_bobj_crv_PrintToLbl = "\uB05D:";
var L_bobj_crv_PrintInfoTitle = "PDF\uB85C \uC778\uC1C4:";
var L_bobj_crv_PrintInfo1 = '\uC778\uC1C4\uD558\uB824\uBA74 PDF\uB85C \uB0B4\uBCF4\uB0B4\uC57C \uD569\uB2C8\uB2E4. \uBB38\uC11C\uAC00 \uC5F4\uB824 \uC788\uC73C\uBA74 PDF \uC77D\uAE30 \uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC5D0\uC11C \uC778\uC1C4 \uC635\uC158\uC744 \uC120\uD0DD\uD558\uC2ED\uC2DC\uC624.';
var L_bobj_crv_PrintInfo2 = '\uCC38\uACE0: \uC778\uC1C4\uD558\uB824\uBA74 PDF \uC77D\uAE30 \uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC774 \uC124\uCE58\uB418\uC5B4 \uC788\uC5B4\uC57C \uD569\uB2C8\uB2E4(\uC608: Adobe Reader).';
var L_bobj_crv_PrintPageRangeError = "\uC720\uD6A8\uD55C \uD398\uC774\uC9C0 \uBC94\uC704\uB97C \uC785\uB825\uD569\uB2C8\uB2E4.";

var L_bobj_crv_ExportBtnLbl = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ExportDialogTitle = "\uB0B4\uBCF4\uB0B4\uAE30";
var L_bobj_crv_ExportFormatLbl = "\uD30C\uC77C \uD615\uC2DD:";
var L_bobj_crv_ExportInfoTitle = "\uB0B4\uBCF4\uB0B4\uAE30 \uBC29\uBC95:";

var L_bobj_crv_ParamsApply = "\uC801\uC6A9";
var L_bobj_crv_ParamsAdvDlg = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12 \uD3B8\uC9D1";
var L_bobj_crv_ParamsDeleteTooltip = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12 \uC0AD\uC81C";
var L_bobj_crv_ParamsAddValue = "\uD074\uB9AD\uD558\uC5EC \uCD94\uAC00...";
var L_bobj_crv_ParamsApplyTip = "\uC801\uC6A9 \uB2E8\uCD94(\uC0AC\uC6A9\uD568)";
var L_bobj_crv_ParamsApplyDisabledTip = "\uC801\uC6A9 \uB2E8\uCD94(\uC0AC\uC6A9 \uC548 \uD568)";
var L_bobj_crv_ParamsDlgTitle = "\uAC12 \uC785\uB825";
var L_bobj_crv_ParamsCalBtn = "\uB2EC\uB825 \uB2E8\uCD94";
var L_bobj_crv_Reset= "\uC6D0\uB798\uB300\uB85C";
var L_bobj_crv_ResetTip = "\uC7AC\uC124\uC815 \uB2E8\uCD94(\uC0AC\uC6A9\uD568)";
var L_bobj_crv_ResetDisabledTip = "\uC6D0\uB798\uB300\uB85C \uB2E8\uCD94(\uC0AC\uC6A9 \uC548 \uD568)";
var L_bobj_crv_ParamsDirtyTip = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC774 \uBCC0\uACBD\uB418\uC5C8\uC2B5\uB2C8\uB2E4. \uBCC0\uACBD \uB0B4\uC6A9\uC744 \uC801\uC6A9\uD558\uB824\uBA74 [\uC801\uC6A9] \uB2E8\uCD94\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsDataTip = "\uB370\uC774\uD130 \uBC18\uC785 \uB9E4\uAC1C \uBCC0\uC218\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsMaxNumDefaultValues = "\uB354 \uB9CE\uC740 \uD56D\uBAA9\uC744 \uBCF4\uB824\uBA74 \uC5EC\uAE30\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624...";
var L_bobj_crv_paramsOpenAdvance = "\'%1\'\uC758 \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB2E8\uCD94";

var L_bobj_crv_ParamsInvalidTitle = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC774 \uC798\uBABB\uB418\uC5C8\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsTooLong = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC740 %1\uC790\uB97C \uCD08\uACFC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsTooShort = "\uB9E4\uAC1C \uBCC0\uC218 \uAC12\uC740 \uCD5C\uC18C %1\uC790 \uC774\uC0C1\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadNumber = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC22B\uC790\"\uC774\uBA70, \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadCurrency = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uD1B5\uD654\"\uC774\uBA70, \uC74C\uC218 \uBD80\uD638, \uC22B\uC790(\"0-9\"), \uC22B\uC790 \uADF8\uB8F9 \uAE30\uD638 \uB610\uB294 \uC18C\uC218\uC810 \uAE30\uD638\uB9CC \uC0AC\uC6A9\uD560 \uC218 \uC788\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadDate = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC\"\uC774\uACE0 \"%1\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadTime = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC2DC\uAC04\"\uC774\uACE0 \"hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsBadDateTime = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uB0A0\uC9DC \uC2DC\uAC04\"\uC774\uACE0 \"%1 hh:mm:ss\" \uD615\uC2DD\uC73C\uB85C \uC9C0\uC815\uD574\uC57C \uD569\uB2C8\uB2E4. \uC5EC\uAE30\uC11C \"yyyy\"\uB294 \uC5F0\uB3C4(4\uC790\uB9AC), \"mm\"\uC740 \uC6D4(\uC608: 1\uC6D4 = 1), \"dd\"\uB294 \uD574\uB2F9 \uC6D4\uC758 \uB0A0\uC9DC, \"hh\"\uB294 \uC2DC\uAC04(24\uC2DC\uAC04\uC81C), \"mm\"\uC740 \uBD84, \"ss\"\uB294 \uCD08\uB97C \uB098\uD0C0\uB0C5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsMinTooltip = "%1 \uAC12\uC744 %2\uBCF4\uB2E4 \uD06C\uAC70\uB098 \uAC19\uAC8C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsMaxTooltip = "%1 \uAC12\uC744 %2\uBCF4\uB2E4 \uC791\uAC70\uB098 \uAC19\uAC8C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsMinAndMaxTooltip = "%1 \uAC12\uC744 %2\uACFC(\uC640) %3 \uC0AC\uC774\uC758 \uAC12\uC73C\uB85C \uC9C0\uC815\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ParamsStringMinOrMaxTooltip = "\uC774 \uD544\uB4DC\uC5D0 \uB300\uD55C %1 \uAE38\uC774\uB294 %2\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsStringMinAndMaxTooltip = "%1-%2\uC790 \uC0AC\uC774 \uAC12\uC774\uC5B4\uC57C \uD569\uB2C8\uB2E4.";
var L_bobj_crv_ParamsYearToken = "yyyy";
var L_bobj_crv_ParamsMonthToken = "mm";
var L_bobj_crv_ParamsDayToken = "dd";
var L_bobj_crv_ParamsReadOnly = "\uC774 \uB9E4\uAC1C \uBCC0\uC218\uC758 \uD615\uC2DD\uC740 \"\uC77D\uAE30 \uC804\uC6A9\"\uC785\uB2C8\uB2E4.";
var L_bobj_crv_ParamsNoValue = "\uAC12 \uC5C6\uC74C";
var L_bobj_crv_ParamsDuplicateValue = "\uC911\uBCF5 \uAC12\uC740 \uD5C8\uC6A9\uB418\uC9C0 \uC54A\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ParamsEnterOptional = "%1 \uC785\uB825(\uC120\uD0DD \uC0AC\uD56D)";
var L_bobj_crv_ParamsNoneSelected= "(\uC120\uD0DD\uD55C \uD56D\uBAA9 \uC5C6\uC74C)";
var L_bobj_crv_ParamsClearValues= "\uAC12 \uC9C0\uC6B0\uAE30";
var L_bobj_crv_ParamsMoreValues= "%1 \uB2E4\uB978 \uAC12...";
var L_bobj_crv_ParamsMoreValue= "%1 \uB2E4\uB978 \uAC12...";
var L_bobj_crv_Error = "\uC624\uB958";
var L_bobj_crv_OK = "\uD655\uC778";
var L_bobj_crv_Cancel = "\uCDE8\uC18C";
var L_bobj_crv_showDetails = "\uC138\uBD80 \uC815\uBCF4 \uD45C\uC2DC";
var L_bobj_crv_hideDetails = "\uC138\uBD80 \uC815\uBCF4 \uC228\uAE30\uAE30";
var L_bobj_crv_RequestError = "\uC694\uCCAD\uC744 \uCC98\uB9AC\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_ServletMissing = "\uBE44\uB3D9\uAE30 \uC694\uCCAD\uC744 \uCC98\uB9AC\uD558\uB294 CrystalReportViewerServlet\uC5D0 \uBDF0\uC5B4\uB97C \uC5F0\uACB0\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.\n\uC751\uC6A9 \uD504\uB85C\uADF8\uB7A8\uC758 web.xml \uD30C\uC77C\uC5D0\uC11C Servlet \uBC0F Servlet-Mapping\uC774 \uC62C\uBC14\uB974\uAC8C \uC120\uC5B8\uB418\uC5C8\uB294\uC9C0 \uD655\uC778\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_FlashRequired = "\uC774 \uCEE8\uD150\uD2B8\uB97C \uC0AC\uC6A9\uD558\uB824\uBA74 Adobe Flash Player 9 \uC774\uC0C1\uC774 \uD544\uC694\uD569\uB2C8\uB2E4. {0}\uC124\uCE58\uD558\uB824\uBA74 \uC5EC\uAE30\uB97C \uD074\uB9AD\uD558\uC2ED\uC2DC\uC624.";
var L_bobj_crv_ReadOnlyInPanel= "\uD328\uB110\uC5D0\uC11C \uC774 \uB9E4\uAC1C \uBCC0\uC218\uB97C \uD3B8\uC9D1\uD560 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4. \uD574\uB2F9 \uAC12\uC744 \uC218\uC815\uD558\uB824\uBA74 \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB300\uD654 \uC0C1\uC790\uB97C \uC5EC\uC2ED\uC2DC\uC624.";

var L_bobj_crv_Tree_Drilldown_Node = "%1 \uB178\uB4DC \uB4DC\uB9B4\uB2E4\uC6B4";

var L_bobj_crv_ReportProcessingMessage = "\uBB38\uC11C\uB97C \uCC98\uB9AC\uD558\uB294 \uB3D9\uC548 \uC7A0\uC2DC \uAE30\uB2E4\uB824 \uC8FC\uC2ED\uC2DC\uC624";
var L_bobj_crv_PrintControlProcessingMessage = "Crystal Reports \uC778\uC1C4 \uCEE8\uD2B8\uB864\uC774 \uB85C\uB4DC\uB418\uB294 \uB3D9\uC548 \uAE30\uB2E4\uB824 \uC8FC\uC2ED\uC2DC\uC624.";

var L_bobj_crv_SundayShort = "S";
var L_bobj_crv_MondayShort = "M";
var L_bobj_crv_TuesdayShort = "T";
var L_bobj_crv_WednesdayShort = "W";
var L_bobj_crv_ThursdayShort = "T ";
var L_bobj_crv_FridayShort = "F";
var L_bobj_crv_SaturdayShort = "S";

var L_bobj_crv_Minimum = "\uCD5C\uC18C\uAC12";
var L_bobj_crv_Maximum = "\uCD5C\uB300\uAC12";

var L_bobj_crv_Date = "\uB0A0\uC9DC";
var L_bobj_crv_Time = "\uC2DC\uAC04";
var L_bobj_crv_DateTime = "\uB0A0\uC9DC \uC2DC\uAC04";
var L_bobj_crv_Boolean = "\uBD80\uC6B8";
var L_bobj_crv_Number = "\uC22B\uC790";
var L_bobj_crv_Text = "\uD14D\uC2A4\uD2B8";

var L_bobj_crv_InteractiveParam_NoAjax = "\uC0AC\uC6A9 \uC911\uC778 \uC6F9 \uBE0C\uB77C\uC6B0\uC800\uAC00 \uB9E4\uAC1C \uBCC0\uC218 \uCC3D\uC744 \uD45C\uC2DC\uD558\uC9C0 \uC54A\uB3C4\uB85D \uC124\uC815\uB418\uC5C8\uC2B5\uB2C8\uB2E4.";
var L_bobj_crv_AdvancedDialog_NoAjax= "\uBDF0\uC5B4\uC5D0\uC11C \uACE0\uAE09 \uD504\uB86C\uD504\uD2B8 \uB300\uD654 \uC0C1\uC790\uB97C \uC5F4 \uC218 \uC5C6\uC2B5\uB2C8\uB2E4.";

var L_bobj_crv_EnableAjax= "\uBE44\uB3D9\uAE30 \uC694\uCCAD\uC744 \uC0AC\uC6A9\uD558\uB824\uBA74 \uAD00\uB9AC\uC790\uC5D0\uAC8C \uBB38\uC758\uD558\uC2ED\uC2DC\uC624.";

var L_bobj_crv_LastRefreshed = "\uB9C8\uC9C0\uB9C9 \uC0C8\uB85C \uACE0\uCE68";

var L_bobj_crv_Collapse = "\uCD95\uC18C";

var L_bobj_crv_CatalystTip = "\uC628\uB77C\uC778 \uB9AC\uC18C\uC2A4";
