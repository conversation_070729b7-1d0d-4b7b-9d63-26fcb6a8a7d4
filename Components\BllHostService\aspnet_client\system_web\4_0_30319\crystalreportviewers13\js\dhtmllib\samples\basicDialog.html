<!--
=============================================================
WebIntelligence(r) Report Panel
Copyright(c) 2001-2003 Business Objects S.A.
All rights reserved

Use and support of this software is governed by the terms
and conditions of the software license agreement and support
policy of Business Objects S.A. and/or its subsidiaries. 
The Business Objects products and technology are protected
by the US patent number 5,555,403 and 6,247,008

File: basicDialog.html
=============================================================
-->

<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
		<title>Basic dialog window</title>
			
		<script language="javascript" src="../dom.js"></script>
		
		<script language="javascript">
			var skin=parent._skin?parent._skin:"skin_standard";
			var lang=parent._lang?parent._lang:"en";
			
			initDom("../images/"+skin+"/",lang)
			styleSheet()
		</script>
		
		<script language="javascript" src="../dialog.js"></script>
	
		<script language="javascript">
						
			// 
			// GLOBALS
			//		
			
			// Widgets
			var basicDlg = null;
			var info = null;
			var okButton = null;
			var cancelButton = null;
			var helpButton = null;
			var applyButton = null;
						
			// Used in resizeCB
			var buttonsWidth	= 60;
			var buttonsHeight	= 52;
			var dlgWidth		= 450;
			var dlgHeight		= 300;
			var frmZoneWidth	= 0.99*dlgWidth;
			var frmZoneHeight	= 0.9*dlgHeight;
			var initialized		= false;
		</script>					
		

		<script language="javascript">
															        
			
			// function okCB
			// Called when click on the OK button or when click on the apply button
			function okCB( )
			{
			}
			
			// function cancelCB
			// called when click on the CANCEL button
			function cancelCB()
			{	
				basicDlg.show(false);			
			}
			
			// function helpCB
			// called when click on the HELP button
			function helpCB()
			{
				parent.showAlertDialog("Not implemented yet !","Development version",0);
				basicDlg.show(false);				
			}
			
			// Load widgets
			function loadCB()
			{	
				if (!initialized)
				{	
					initialized=true;
					
					// --------------------------------------------------------
					// Create widgets
					// --------------------------------------------------------
					
					// Dialog window (id,title,width,height,defaultCB,cancelCB)
					basicDlg = newDialogBoxWidget("basicDlg","Title",dlgWidth,dlgHeight,
						okCB,cancelCB,true);
			        			
					// Details info zone
					info = newInfoWidget("info","info","","")							
			
					// Zone
					framezone = newFrameZoneWidget("framezone",frmZoneWidth,frmZoneHeight);
					
					// OK Button						
					okButton = newButtonWidget("okButton", "OK", 
						"okCB()", buttonsWidth, "OK", 
						"OK", 0, 0);
						
					// Cancel Button						
					cancelButton = newButtonWidget("cancelButton", "Cancel", 
						"cancelCB()", buttonsWidth, "Cancel", 
						"Cancel", 0, 0);
						
					// Help Button						
					helpButton = newButtonWidget("helpButton", "Help", 
						"helpCB()", buttonsWidth, "Help", 
						"Help", 0, 0);
						
					// Apply button						
					applyButton = newButtonWidget("applyButton", "Apply", 
						"okCB(true)", buttonsWidth, "Apply", 
						"Apply", 0, 0);											
				}
				
				if ( !_curWin.basicDlgInitialized )
				{
					_curWin.basicDlgInitialized = true;
					
					// --------------------------------------------------------
					// Write the widgets
					// --------------------------------------------------------			
			
					// Build the dialog box
					targetApp(
						basicDlg.beginHTML() +
							
							'<table cellspacing="0" cellpadding="5" border="0" class="dialogzone"><tbody>'+

							'<tr>'+
								'<td>'+
									
									framezone.beginHTML() +
										'<table class="dialogzone" cellspacing="0" cellpadding="0" border="0"><tbody>' +
										'<tr>' +
											'<td>'+
												'Basic dialog box <BR>' +
												'Basic dialog box <BR>' +
												'Basic dialog box <BR>' +
												'Basic dialog box <BR>' +
												'Basic dialog box <BR>' +
												'Basic dialog box <BR>' +
											'</td>'+
										'</tr>' +
										'</tbody></table>'+
									framezone.endHTML() +
									
								'</td>' +
							'</tr>' +							
  					   				
							'<tr>' +
   								'<td align="center" valign="right">' +
   									info.getHTML() +
   								'</td>' +
   							'</tr>' + 
   				
   							'<tr>' +
   								'<td align="right" valign="center">' +
   									'<table class="dialogzone" cellspacing="0" cellpadding="0" border="0"><tbody><tr>' +
   										'<td>' +   						
   											okButton.getHTML() +
   										'</td>' +
   										'<td>' + getSpace(5,1)+ '</td>' +
   										'<td>' +
   											cancelButton.getHTML() +
   										'</td>' +
   										'<td>' + getSpace(5,1)+ '</td>' +
   										'<td>' +
   											applyButton.getHTML() +
   										'</td>' +
   										'<td>' + getSpace(5,1)+ '</td>' +
   										'<td>' +
   											helpButton.getHTML() +
   										'</td>' +
									'</tr></tbody></table>'+   						
   									'</table>' +
   								'</td>' +
   							'</tr>' + 			
							'</table>' +

						basicDlg.endHTML()				
					);
				}
				
				// --------------------------------------------------------
				// Init widgets
				// --------------------------------------------------------
				basicDlg.init();				
				info.init();
				okButton.init();
				cancelButton.init();
				helpButton.init();	
				applyButton.init();
																									
				// --------------------------------------------------------
				// Show the dialog box
				// --------------------------------------------------------											
				basicDlg.show(true);
			}			
					
			// 
			// END OF FUNCTIONS
			//		

		</script>

	</head>

	<body class="dialogzone" onload="setTimeout('loadCB()',1)">	
	
	
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
		this is a sample text
	
		<!--<iframe src="http://www.ulead.com/ma/samples/slider-1/slider-1.htm" width="500" height"=300"></iframe>-->

	
	</body>
	
</html>