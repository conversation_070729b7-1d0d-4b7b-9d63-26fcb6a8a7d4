.pePromptFieldset
{    
    border: 1px solid;
    border-color: #A3A3BC;
    padding: 0;
}

.peRangeFieldsetLegend
{
    background-color:#E4E4EC;
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
    font-weight: normal;
}

.pePromptBorder   
{
    border-top: 1px solid #FFFFFF;
    border-bottom: none;
    border-left: none;
    border-right: none;    
}

.pePromptUnitHeader   
{
    background-color: #E4E4EC;
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
    font-weight: normal;    
    border-bottom: 1px solid #A3A3BC;
}

.PEUH2   
{
    background-color: #E4E4EC;
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
    font-weight: normal;    
}

.pePromptUnitHeaderTextLeft   
{
    background-color: #E4E4EC;
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
    font-weight: normal;        
}

.pePromptUnitHeaderTextRight
{
    background-color: #E4E4EC;
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;   
}

.pePromptElement  
{
    background-color: #E4E4EC;    
}

.pePromptElementText 
{
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
}

.pePromptingText  
{
    color: black;
    font-family: Tahoma, verdana;
    font-size: 8pt;
}

.pePromptRuler    
{
    color: #A3A3BC;
    height: 1px;
}

.pePromptMessage
{
    color: black;
    font-family: Tahoma, verdana;
    font-weight: normal;
    font-size: 8pt;
}
            
.pePromptTextBox 
{
    font-size: 8pt;
    background-color: #FFFFFF;
    font-family: Tahoma, verdana;
    width: 300px;
}

td.pePromptButtonSpacer
{
    FONT-FAMILY: Tahoma;
    FONT-SIZE: 8pt;
}

td.pePromptButton
{
	color: black;
	font-size: 8pt;
	font-family: Tahoma;
	font-weight: normal;
	text-decoration:none;
	cursor: pointer;
}

div.pePromptButton a
{
	color: black;
	font-size: 8pt;
	font-family: Tahoma;
	font-weight: normal;
	text-decoration:none;
	cursor: pointer;
}

div.pePromptButton a:hover
{
	color: #0000FF;	
}

.pePromptDropDown 
{
    font-size: 8pt;
    font-family: Tahoma, verdana;
    width: 300px;
}

.peBatchDropDown 
{
    font-size: 8pt;
    font-family: Tahoma, verdana;
    width: 100px;
}

.pePromptListBox
{
    font-size: 8pt;
    font-family: Tahoma, verdana;
    width: 300px;    
}

.peExpandCollapseImageColumn
{
    width: 3%;
}          

.peExpandCollapseTitleColumn
{
    text-align: left;
} 

.pePromptUnitTable      
{
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
    border-style:hidden;
}

.peExpandCollapseAnchor
{
    text-decoration: none;
    font-size: 8pt;
    font-family: Tahoma, verdana;
    font-weight: normal;
    color: black;
}

.peExpandCollapseImage
{
    border: 0;
}                
/*-----------------INcalendar------------------------------------------------*/
div.INcalendarWindow
{
    position:absolute;
    display: none;
    background:#ffffff;
} 
div.INcalendarTitle
{
    border-left:1px solid #aaaaaa;
    border-top:1px solid #999999;
    border-right:1px solid #333333;

    width:100%;
    height:20px;
} 
td.INtitleText
{
    cursor:default;
    width:100%;
    color: black;
    font-family: Tahoma, verdana;
    font-weight: 900;
    font-size: 9pt;
} 
td.INclose
{
    width:14px;
    cursor:pointer;
} 
div.INcalendar
{
	width:100%;
} 
div.INcalendarTop
{
    border-left:1px solid #aaaaaa;
    border-right:1px solid #333333;

	height:70px;
	width:100%;
} 
div.INcalendarBottom
{
    border-left:1px solid #aaaaaa;
    border-right:1px solid #333333;
    border-bottom:1px solid #222222;
	height:160px;/*(250-20-70)*/
	width:100%;
} 
A.focusDay:link { color:#ff0000; text-decoration: none; font:12pt Tahoma, helvetica; }
A.focusDay:hover { color: #ff0000; text-decoration: none; font:12pt Tahoma, helvetica; }
A.focusDay:visited { color: #ff0000; text-decoration: none; font:12pt Tahoma, helvetica; }
A.weekday:link { color: blue; text-decoration: none; font: 12pt Tahoma, helvetica; }
A.weekday:hover { color: darkred; font: 12pt Tahoma, helvetica; }
A.weekday:visited { color: blue; text-decoration: none; font:12pt Tahoma, helvetica; }

/*dateColor        = "blue";          // TEXT COLOR OF THE LISTED DATES (1-28+)
//focusColor       = "#ff0000";       // TEXT COLOR OF THE SELECTED DATE (OR CURRENT DATE)
//hoverColor       = "darkred";       // TEXT COLOR OF A LINK WHEN YOU HOVER OVER IT
//fontStyle        = "12pt Tahoma, helvetica";           // TEXT STYLE FOR DATES
*/

.titlezone
{
	color:#375487;
	font-family:"Tahoma","sans-serif";
	font-size:11px;
	font-weight:normal;
}

.dialogbox
{
	background-color:#E4E4EC;
	color:#6A85AE;
	font-family:"Tahoma","sans-serif";
	font-size:11px;
	border-top:2px solid #F0F0F0;
	border-left:2px solid #F0F0F0;
	border-bottom:2px solid #808080;
	border-right:2px solid #808080;
}

.textinputs
{
	font-family:"Tahoma","sans-serif";
	font-size:11px;
	background-color:white;
	border: 1px solid #636384;
	padding-left:2px;
	padding-right:2px;
}

/*=========*/
/* Buttons */
/*=========*/

.wizbutton
{
	color:black;
	font-family:"Tahoma","sans-serif";
	font-size:11px;
	font-weight:normal;
	text-decoration:none;
}

.wizbuttongray
{
	color:#909090;
	font-family:"Tahoma","sans-serif";
	font-size:11px;
	font-weight:normal;
	text-decoration:none;
}

a.wizbutton:hover
{
	color:black;
	text-decoration:underline;
}

a.wizbuttongray:hover
{
	color:#909090;
}
/*----------------------------------------------------------------------------------------------*/
