<?xml version="1.0"?>
<!--<PERSON><PERSON> chú-->
<!--1. <PERSON><PERSON><PERSON>, nên lấy cấu hình mới nhất từ app.config-->
<!--
    2. TOS services, cung cấp các function của TOS để nơi khác gọi.
    <PERSON><PERSON> vậ<PERSON>, <PERSON>hi implement chỉ nên implement các function từ lớp TosServicesBE
-->
<configuration>
  <configSections>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
    <section name="unity" type="Microsoft.Practices.Unity.Configuration.UnityConfigurationSection, Microsoft.Practices.Unity.Configuration, Version=2.1.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
  </configSections>
  <log4net>
    <root>
      <level value="ALL"/>
      <appender-ref ref="RollingFileAppender"/>
    </root>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <!--file type="log4net.Util.PatternString"
            value="%appdomain_%property{log4net:HostName}.log"/-->
      <file value="Log\service.log"/>
      <encoding value="utf-8"/>
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock"/>
      <rollingStyle value="Composite"/>
      <datePattern value="yyyyMMdd"/>
      <maxSizeRollBackups value="100"/>
      <maximumFileSize value="15MB"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date %-5level %logger:%newline%property{tab}%message%newline"/>
      </layout>
      <filter type="log4net.Filter.LevelRangeFilter">
        <levelMin value="DEBUG"/>
        <levelMax value="ERROR"/>
      </filter>
    </appender>
  </log4net>
  <appSettings>
    <!--OracleHomePath-->
    <add key="OracleHomePath" value="C:\inetpub\wwwroot\oracle"/>
    <add key="GoliveFlg" value="1"/>
    <add key="DataSource" value="VNTCI_TEST_02"/>
    <add key="DbUserID" value="rr6VQnKPNxDAyHQDwoU5ow=="/>
    <add key="DbPassword" value="kmz6kIkkWlNk/IMHmZa4dQ=="/>
    <add key="DbServer" value="172.24.50.153"/>
    <add key="DbConnectType" value="TNS"/>
    <!--Encryption configuration-->
    <add key="EncryptKey" value="ABCDEF"/>
    <add key="HomePort" value="VNTCI"/>
    <add key="SiteId" value="TCI"/>
  </appSettings>
  <unity>
    <alias alias="transient" type="Microsoft.Practices.Unity.TransientLifetimeManager, Microsoft.Practices.Unity, Version=2.1.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    <alias alias="perResolve" type="Microsoft.Practices.Unity.PerResolveLifetimeManager, Microsoft.Practices.Unity, Version=2.1.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    <alias alias="singleton" type="Microsoft.Practices.Unity.ContainerControlledLifetimeManager, Microsoft.Practices.Unity, Version=2.1.505.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
    <alias alias="BlExceptionHandler" type="Com.TCIS.TopoVn.ValueObjects.BlExceptionHandler, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarDischarge" type="Com.TCIS.TopoVn.Bll.CoprarDischarge, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IContainerBE" type="Com.TCIS.TopoVn.Bll.IContainerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ContainerBE" type="Com.TCIS.TopoVn.Bll.ContainerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IYardConsolidationRequestBE" type="Com.TCIS.TopoVn.Bll.IYardConsolidationRequestBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="YardConsolidationRequestBE" type="Com.TCIS.TopoVn.Bll.YardConsolidationRequestBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IVesselBE" type="Com.TCIS.TopoVn.Bll.IVesselBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="VesselBE" type="Com.TCIS.TopoVn.Bll.VesselBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISequenceBE" type="Com.TCIS.TopoVn.Bll.ISequenceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SequenceBE" type="Com.TCIS.TopoVn.Bll.SequenceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICoprarBaplieBE" type="Com.TCIS.TopoVn.Bll.ICoprarBaplieBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarBaplieBE" type="Com.TCIS.TopoVn.Bll.CoprarBaplieBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISystemCodeBE" type="Com.TCIS.TopoVn.Bll.ISystemCodeBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SystemCodeBE" type="Com.TCIS.TopoVn.Bll.SystemCodeBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarBaplie" type="Com.TCIS.TopoVn.Bll.CoprarBaplie, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarLoad" type="Com.TCIS.TopoVn.Bll.CoprarLoad, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ItemBundle" type="Com.TCIS.TopoVn.Bll.ItemBundle, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEirExpiryBE" type="Com.TCIS.TopoVn.Bll.IEirExpiryBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EirExpiryBE" type="Com.TCIS.TopoVn.Bll.EirExpiryBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IInvoiceBE" type="Com.TCIS.TopoVn.Bll.IInvoiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="InvoiceBE" type="Com.TCIS.TopoVn.Bll.InvoiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IInvoiceDE" type="Com.TCIS.TopoVn.Dal.IInvoiceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="InvoiceDE" type="Com.TCIS.TopoVn.Dal.InvoiceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IProductivityBE" type="Com.TCIS.TopoVn.Bll.IProductivityBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ProductivityBE" type="Com.TCIS.TopoVn.Bll.ProductivityBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IThuTucHaiQuanBE" type="Com.TCIS.TopoVn.Bll.IThuTucHaiQuanBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ThuTucHaiQuanBE" type="Com.TCIS.TopoVn.Bll.ThuTucHaiQuanBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IThuTucHaiQuanDE" type="Com.TCIS.TopoVn.Dal.IThuTucHaiQuanDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ThuTucHaiQuanDE" type="Com.TCIS.TopoVn.Dal.ThuTucHaiQuanDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IOperationBE" type="Com.TCIS.TopoVn.Bll.IOperationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="OperationBE" type="Com.TCIS.TopoVn.Bll.OperationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IGateInBE" type="Com.TCIS.TopoVn.Bll.IGateInBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="GateInBE" type="Com.TCIS.TopoVn.Bll.GateInBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="GateInExtra" type="Com.TCIS.TopoVn.Bll.GateInExtra, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IGateOutBE" type="Com.TCIS.TopoVn.Bll.IGateOutBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="GateOutBE" type="Com.TCIS.TopoVn.Bll.GateOutBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IStartUpBE" type="Com.TCIS.TopoVn.Bll.IStartUpBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="StartUpBE" type="Com.TCIS.TopoVn.Bll.StartUpBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IModuleAlertBE" type="Com.TCIS.TopoVn.Bll.IModuleAlertBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ModuleAlertBE" type="Com.TCIS.TopoVn.Bll.ModuleAlertBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITopxInterfaceBE" type="Com.TCIS.TopoVn.Bll.ITopxInterfaceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TopxInterfaceBE" type="Com.TCIS.TopoVn.Bll.TopxInterfaceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEirBE" type="Com.TCIS.TopoVn.Bll.IEirBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EirBE" type="Com.TCIS.TopoVn.Bll.EirBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EirExtra" type="Com.TCIS.TopoVn.Bll.EirExtra, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IReportBE" type="Com.TCIS.TopoVn.Bll.IReportBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ReportBE" type="Com.TCIS.TopoVn.Bll.ReportBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IReportDE" type="Com.TCIS.TopoVn.Dal.IReportDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ReportDE" type="Com.TCIS.TopoVn.Dal.ReportDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CommonCatalog" type="Com.TCIS.TopoVn.Bll.CommonCatalog, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IPartnerBE" type="Com.TCIS.TopoVn.Bll.IPartnerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PartnerBE" type="Com.TCIS.TopoVn.Bll.PartnerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IPartnerDE" type="Com.TCIS.TopoVn.Dal.IPartnerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PartnerDE" type="Com.TCIS.TopoVn.Dal.PartnerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IApplicationBE" type="Com.TCIS.TopoVn.Bll.IApplicationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ApplicationBE" type="Com.TCIS.TopoVn.Bll.ApplicationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IApplicationDE" type="Com.TCIS.TopoVn.Dal.IApplicationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ApplicationDE" type="Com.TCIS.TopoVn.Dal.ApplicationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IAuditBE" type="Com.TCIS.TopoVn.Bll.IAuditBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="AuditBE" type="Com.TCIS.TopoVn.Bll.AuditBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IAuditDE" type="Com.TCIS.TopoVn.Dal.IAuditDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="AuditDE" type="Com.TCIS.TopoVn.Dal.AuditDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IBillingBE" type="Com.TCIS.TopoVn.Bll.IBillingBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="BillingBE" type="Com.TCIS.TopoVn.Bll.BillingBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IBillingDE" type="Com.TCIS.TopoVn.Dal.IBillingDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="BillingDE" type="Com.TCIS.TopoVn.Dal.BillingDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEdiMessageBE" type="Com.TCIS.TopoVn.Bll.IEdiMessageBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EdiMessageBE" type="Com.TCIS.TopoVn.Bll.EdiMessageBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IContainerDE" type="Com.TCIS.TopoVn.Dal.IContainerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ContainerDE" type="Com.TCIS.TopoVn.Dal.ContainerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEquipmentBE" type="Com.TCIS.TopoVn.Bll.IEquipmentBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EquipmentBE" type="Com.TCIS.TopoVn.Bll.EquipmentBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEquipmentDE" type="Com.TCIS.TopoVn.Dal.IEquipmentDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EquipmentDE" type="Com.TCIS.TopoVn.Dal.EquipmentDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICommonBE" type="Com.TCIS.TopoVn.Bll.ICommonBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CommonBE" type="Com.TCIS.TopoVn.Bll.CommonBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICheProdBE" type="Com.TCIS.TopoVn.Bll.ICheProdBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CheProdBE" type="Com.TCIS.TopoVn.Bll.CheProdBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IContainerMaintenanceBE" type="Com.TCIS.TopoVn.Bll.IContainerMaintenanceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ContainerMaintenanceBE" type="Com.TCIS.TopoVn.Bll.ContainerMaintenanceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IErrorLogBE" type="Com.TCIS.TopoVn.Bll.IErrorLogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ErrorLogBE" type="Com.TCIS.TopoVn.Bll.ErrorLogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDemDetBE" type="Com.TCIS.TopoVn.Bll.IDemDetBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DemDetBE" type="Com.TCIS.TopoVn.Bll.DemDetBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDemDetDE" type="Com.TCIS.TopoVn.Dal.IDemDetDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DemDetDE" type="Com.TCIS.TopoVn.Dal.DemDetDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDemDetDispatchOrdersBE" type="Com.TCIS.TopoVn.Bll.IDemDetDispatchOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DemDetDispatchOrdersBE" type="Com.TCIS.TopoVn.Bll.DemDetDispatchOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IOperationDE" type="Com.TCIS.TopoVn.Dal.IOperationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="OperationDE" type="Com.TCIS.TopoVn.Dal.OperationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDepositBE" type="Com.TCIS.TopoVn.Bll.IDepositBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DepositBE" type="Com.TCIS.TopoVn.Bll.DepositBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDepositDE" type="Com.TCIS.TopoVn.Dal.IDepositDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DepositDE" type="Com.TCIS.TopoVn.Dal.DepositDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEmptyReceivOrdersBE" type="Com.TCIS.TopoVn.Bll.IEmptyReceivOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EmptyReceivOrdersBE" type="Com.TCIS.TopoVn.Bll.EmptyReceivOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEmptyReleaseOrdersBE" type="Com.TCIS.TopoVn.Bll.IEmptyReleaseOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EmptyReleaseOrdersBE" type="Com.TCIS.TopoVn.Bll.EmptyReleaseOrdersBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IGrossWeightServiceBE" type="Com.TCIS.TopoVn.Bll.IGrossWeightServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="GrossWeightServiceBE" type="Com.TCIS.TopoVn.Bll.GrossWeightServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IPortTransferBE" type="Com.TCIS.TopoVn.Bll.IPortTransferBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PortTransferBE" type="Com.TCIS.TopoVn.Bll.PortTransferBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IPortTransferDE" type="Com.TCIS.TopoVn.Dal.IPortTransferDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PortTransferDE" type="Com.TCIS.TopoVn.Dal.PortTransferDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IStuffStripContainerBE" type="Com.TCIS.TopoVn.Bll.IStuffStripContainerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="StuffStripContainerBE" type="Com.TCIS.TopoVn.Bll.StuffStripContainerBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IStripStuffContainerDE" type="Com.TCIS.TopoVn.Dal.IStripStuffContainerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="StripStuffContainerDE" type="Com.TCIS.TopoVn.Dal.StripStuffContainerDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEdiServerLogBE" type="Com.TCIS.TopoVn.Bll.IEdiServerLogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EdiServerLogBE" type="Com.TCIS.TopoVn.Bll.EdiServerLogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEdiServerLogDE" type="Com.TCIS.TopoVn.Dal.IEdiServerLogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EdiServerLogDE" type="Com.TCIS.TopoVn.Dal.EdiServerLogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICoprarDischargeBE" type="Com.TCIS.TopoVn.Bll.ICoprarDischargeBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarDischargeBE" type="Com.TCIS.TopoVn.Bll.CoprarDischargeBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICoprarDischargeDE" type="Com.TCIS.TopoVn.Dal.ICoprarDischargeDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarDischargeDE" type="Com.TCIS.TopoVn.Dal.CoprarDischargeDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICoprarLoadBE" type="Com.TCIS.TopoVn.Bll.ICoprarLoadBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CoprarLoadBE" type="Com.TCIS.TopoVn.Bll.CoprarLoadBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISystemInterfaceBE" type="Com.TCIS.TopoVn.Bll.ISystemInterfaceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SystemInterfaceBE" type="Com.TCIS.TopoVn.Bll.SystemInterfaceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IYardConsolidationRequestDE" type="Com.TCIS.TopoVn.Dal.IYardConsolidationRequestDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="YardConsolidationRequestDE" type="Com.TCIS.TopoVn.Dal.YardConsolidationRequestDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICatalogBE" type="Com.TCIS.TopoVn.Bll.ICatalogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CatalogBE" type="Com.TCIS.TopoVn.Bll.CatalogBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICatalogDE" type="Com.TCIS.TopoVn.Dal.ICatalogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CatalogDE" type="Com.TCIS.TopoVn.Dal.CatalogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IErrorLogDE" type="Com.TCIS.TopoVn.Dal.IErrorLogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ErrorLogDE" type="Com.TCIS.TopoVn.Dal.ErrorLogDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IExternalTransportBE" type="Com.TCIS.TopoVn.Billing.Bll.IExternalTransportBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ExternalTransportBE" type="Com.TCIS.TopoVn.Billing.Bll.ExternalTransportBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IExternalTransportDE" type="Com.TCIS.TopoVn.Dal.IExternalTransportDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ExternalTransportDE" type="Com.TCIS.TopoVn.Dal.ExternalTransportDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IImportExcelBE" type="Com.TCIS.TopoVn.Bll.IImportExcelBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ImportExcelBE" type="Com.TCIS.TopoVn.Bll.ImportExcelBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IImportExcelDE" type="Com.TCIS.TopoVn.Dal.IImportExcelDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ImportExcelDE" type="Com.TCIS.TopoVn.Dal.ImportExcelDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISequenceDE" type="Com.TCIS.TopoVn.Dal.ISequenceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SequenceDE" type="Com.TCIS.TopoVn.Dal.SequenceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISystemCodeDE" type="Com.TCIS.TopoVn.Dal.ISystemCodeDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SystemCodeDE" type="Com.TCIS.TopoVn.Dal.SystemCodeDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IVesselDE" type="Com.TCIS.TopoVn.Dal.IVesselDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="VesselDE" type="Com.TCIS.TopoVn.Dal.VesselDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="BllBase" type="Com.TCIS.TopoVn.Bll.BllBase, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITopxInterfaceDE" type="Com.TCIS.TopoVn.Dal.ITopxInterfaceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TopxInterfaceDE" type="Com.TCIS.TopoVn.Dal.TopxInterfaceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TopxExpertInterfaceDE" type="Com.TCIS.TopoVn.Dal.TopxExpertInterfaceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IContainerBillingServiceBE" type="Com.TCIS.TopoVn.Bll.IContainerBillingServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ContainerBillingServiceBE" type="Com.TCIS.TopoVn.Bll.ContainerBillingServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IPregateServiceBE" type="Com.TCIS.TopoVn.Bll.IPregateServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PregateServiceBE" type="Com.TCIS.TopoVn.Bll.PregateServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IMrServiceBE" type="Com.TCIS.TopoVn.Bll.IMrServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="MrServiceBE" type="Com.TCIS.TopoVn.Bll.MrServiceBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IMrServiceDE" type="Com.TCIS.TopoVn.Dal.IMrServiceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="MrServiceDE" type="Com.TCIS.TopoVn.Dal.MrServiceDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISystemMonitorBE" type="Com.TCIS.TopoVn.Bll.ISystemMonitorBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SystemMonitorBE" type="Com.TCIS.TopoVn.Bll.SystemMonitorBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ISystemMonitorDE" type="Com.TCIS.TopoVn.Dal.ISystemMonitorDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="SystemMonitorDE" type="Com.TCIS.TopoVn.Dal.SystemMonitorDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IExtTransportRateBE" type="Com.TCIS.TopoVn.Bll.IExtTransportRateBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ExtTransportRateBE" type="Com.TCIS.TopoVn.Bll.ExtTransportRateBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IExtTransportRateDE" type="Com.TCIS.TopoVn.Dal.IExtTransportRateDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ExtTransportRateDE" type="Com.TCIS.TopoVn.Dal.ExtTransportRateDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--nv.dung 2017.04.12 -  Empty Receival Depot-->
    <!--////////////////////////////////////////-->
    <alias alias="IEmptyReceivalDepotBE" type="Com.TCIS.TopoVn.Bll.IEmptyReceivalDepotBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EmptyReceivalDepotBE" type="Com.TCIS.TopoVn.Bll.EmptyReceivalDepotBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IEmptyReceivalDepotDE" type="Com.TCIS.TopoVn.Dal.IEmptyReceivalDepotDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="EmptyReceivalDepotDE" type="Com.TCIS.TopoVn.Dal.EmptyReceivalDepotDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--nv.dung 2017.04.12 -  Printer setting -->
    <alias alias="IPrinterSettingBE" type="Com.TCIS.TopoVn.Bll.IPrinterSettingBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="PrinterSettingBE" type="Com.TCIS.TopoVn.Bll.PrinterSettingBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--<alias alias="IPrinterSettingDE" type="Com.TCIS.TopoVn.Dal.IPrinterSettingDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null" />
    <alias alias="PrinterSettingDE" type="Com.TCIS.TopoVn.Dal.PrinterSettingDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null" />-->
    <alias alias="DeliveryOrderBE" type="Com.TCIS.TopoVn.Bll.DeliveryOrderBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDeliveryOrderBE" type="Com.TCIS.TopoVn.Bll.IDeliveryOrderBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DeliveryOrderDE" type="Com.TCIS.TopoVn.Dal.DeliveryOrderDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDeliveryOrderDE" type="Com.TCIS.TopoVn.Dal.IDeliveryOrderDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TruckBE" type="Com.TCIS.TopoVn.Bll.TruckBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITruckBE" type="Com.TCIS.TopoVn.Bll.ITruckBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ICableHookBE" type="Com.TCIS.TopoVn.Bll.ICableHookBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="CableHookBE" type="Com.TCIS.TopoVn.Bll.CableHookBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--////////////////////////////////////////-->
    <!--Tích hợp TOS - Hải quan -->
    <alias alias="ITosCustomsIntergrationBE" type="Com.TCIS.TopoVn.Bll.ITosCustomsIntergrationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TosCustomsIntergrationBE" type="Com.TCIS.TopoVn.Bll.TosCustomsIntergrationBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITosCustomsIntegrationDE" type="Com.TCIS.TopoVn.Dal.ITosCustomsIntegrationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TosCustomsIntegrationDE" type="Com.TCIS.TopoVn.Dal.TosCustomsIntegrationDE, Com.TCIS.TopoVn.Dal, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--Document  -->
    <alias alias="IGeneralBE" type="Com.TCIS.TopoVn.Bll.IGeneralBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="GeneralBE" type="Com.TCIS.TopoVn.Bll.GeneralBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IDocumentBE" type="Com.TCIS.TopoVn.Bll.IDocumentBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="DocumentBE" type="Com.TCIS.TopoVn.Bll.DocumentBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!-- Alias ValueObjects -->
    <alias alias="ITg1Cim" type="Com.TCIS.TopoVn.ValueObjects.ITg1Cim, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg1cim" type="Com.TCIS.TopoVn.ValueObjects.Tg1cim, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopxCtr" type="Com.TCIS.TopoVn.ValueObjects.IntfTopxCtr, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg1Che" type="Com.TCIS.TopoVn.ValueObjects.ITg1Che, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg1che" type="Com.TCIS.TopoVn.ValueObjects.Tg1che, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopxChe" type="Com.TCIS.TopoVn.ValueObjects.IntfTopxChe, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg1Scd" type="Com.TCIS.TopoVn.ValueObjects.ITg1Scd, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg1scd" type="Com.TCIS.TopoVn.ValueObjects.Tg1scd, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopxSysCd" type="Com.TCIS.TopoVn.ValueObjects.IntfTopxSysCd, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg1Vsm" type="Com.TCIS.TopoVn.ValueObjects.ITg1Vsm, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg1vsm" type="Com.TCIS.TopoVn.ValueObjects.Tg1vsm, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopxVsched" type="Com.TCIS.TopoVn.ValueObjects.IntfTopxVsched, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg2Alt" type="Com.TCIS.TopoVn.ValueObjects.ITg2Alt, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg2alt" type="Com.TCIS.TopoVn.ValueObjects.Tg2alt, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopoAlert" type="Com.TCIS.TopoVn.ValueObjects.IntfTopoAlert, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg2Cpm" type="Com.TCIS.TopoVn.ValueObjects.ITg2Cpm, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg2cpm" type="Com.TCIS.TopoVn.ValueObjects.Tg2cpm, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopoPosUpd" type="Com.TCIS.TopoVn.ValueObjects.IntfTopoPosUpd, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITg2Vpt" type="Com.TCIS.TopoVn.ValueObjects.ITg2Vpt, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="Tg2vpt" type="Com.TCIS.TopoVn.ValueObjects.Tg2vpt, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IntfTopoPlanPos" type="Com.TCIS.TopoVn.ValueObjects.IntfTopoPlanPos, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITopXInterfaceMessageType" type="Com.TCIS.TopoVn.ValueObjects.ITopXInterfaceMessageType, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TopXInterfaceMessageType" type="Com.TCIS.TopoVn.ValueObjects.TopXInterfaceMessageType, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TopXExpertInterfaceMessageType" type="Com.TCIS.TopoVn.ValueObjects.TopXExpertInterfaceMessageType, Com.TCIS.TopoVn.ValueObjects, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="IScheduleReportBE" type="Com.TCIS.TopoVn.Bll.IScheduleReportBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ScheduleReportBe" type="Com.TCIS.TopoVn.Bll.ScheduleReportBe, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="ITosServicesBE" type="Com.TCIS.TopoVn.Bll.ITosServicesBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <alias alias="TosServicesBE" type="Com.TCIS.TopoVn.Bll.TosServicesBE, Com.TCIS.TopoVn.Bll, Version=*******, Culture=neutral, PublicKeyToken=null"/>
    <!--End Alias ValueObjects -->
    <container>
      <!--Mapping TOPX value object-->
      <register type="ITg1Cim" mapTo="Tg1cim">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg1Che" mapTo="Tg1che">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg1Scd" mapTo="Tg1scd">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg1Vsm" mapTo="Tg1vsm">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg2Alt" mapTo="Tg2alt">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg2Cpm" mapTo="Tg2cpm">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITg2Vpt" mapTo="Tg2vpt">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITopXInterfaceMessageType" mapTo="TopXInterfaceMessageType">
        <lifetime type="perResolve"/>
      </register>
      <!--End Mapping TOPX value object-->
      <register type="BlExceptionHandler">
        <lifetime type="perResolve"/>
      </register>
      <register type="CoprarDischarge">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="YardConsolidationRequestBE" dependencyType="IYardConsolidationRequestBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="CoprarBaplieBE" dependencyType="ICoprarBaplieBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
      </register>
      <register type="CoprarBaplie">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
      </register>
      <register type="CoprarLoad">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
      </register>
      <register type="ItemBundle">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEirExpiryBE" mapTo="EirExpiryBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IInvoiceBE" mapTo="InvoiceBE">
        <lifetime type="perResolve"/>
        <property name="InvoiceDE" dependencyType="IInvoiceDE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="GeneralBE" dependencyType="IGeneralBE"/>
      </register>
      <register type="IGeneralBE" mapTo="GeneralBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IDocumentBE" mapTo="DocumentBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IInvoiceDE" mapTo="InvoiceDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IProductivityBE" mapTo="ProductivityBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IThuTucHaiQuanBE" mapTo="ThuTucHaiQuanBE">
        <lifetime type="perResolve"/>
        <property name="ThuTucHaiQuanDE" dependencyType="IThuTucHaiQuanDE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="YardConsolidationRequestBE" dependencyType="IYardConsolidationRequestBE"/>
      </register>
      <register type="IThuTucHaiQuanDE" mapTo="ThuTucHaiQuanDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IGateInBE" mapTo="GateInBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="GateInExtra">
        <lifetime type="perResolve"/>
      </register>
      <register type="IGateOutBE" mapTo="GateOutBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IStartUpBE" mapTo="StartUpBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IModuleAlertBE" mapTo="ModuleAlertBE">
        <lifetime type="perResolve"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
      </register>
      <register type="IEirBE" mapTo="EirBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="EirExtra">
        <lifetime type="perResolve"/>
      </register>
      <register type="IReportBE" mapTo="ReportBE">
        <lifetime type="perResolve"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="ReportDE" dependencyType="IReportDE"/>
      </register>
      <register type="IReportDE" mapTo="ReportDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="CommonCatalog">
        <lifetime type="perResolve"/>
      </register>
      <register type="IPartnerBE" mapTo="PartnerBE">
        <lifetime type="perResolve"/>
        <property name="PartnerDE" dependencyType="IPartnerDE"/>
      </register>
      <register type="IPartnerDE" mapTo="PartnerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IApplicationBE" mapTo="ApplicationBE">
        <lifetime type="perResolve"/>
        <property name="ApplicationDE" dependencyType="IApplicationDE"/>
      </register>
      <register type="IApplicationDE" mapTo="ApplicationDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IAuditBE" mapTo="AuditBE">
        <lifetime type="perResolve"/>
        <property name="AuditDE" dependencyType="IAuditDE"/>
      </register>
      <register type="IAuditDE" mapTo="AuditDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IBillingBE" mapTo="BillingBE">
        <lifetime type="perResolve"/>
        <property name="BillingDE" dependencyType="IBillingDE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
      </register>
      <register type="IBillingDE" mapTo="BillingDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEdiMessageBE" mapTo="EdiMessageBE">
        <lifetime type="perResolve"/>
        <property name="ContainerDE" dependencyType="IContainerDE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
      </register>
      <register type="IContainerDE" mapTo="ContainerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEquipmentBE" mapTo="EquipmentBE">
        <lifetime type="perResolve"/>
        <property name="EquipmentDE" dependencyType="IEquipmentDE"/>
      </register>
      <register type="IEquipmentDE" mapTo="EquipmentDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ICommonBE" mapTo="CommonBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ICheProdBE" mapTo="CheProdBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IContainerMaintenanceBE" mapTo="ContainerMaintenanceBE">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="EdiMessageBE" dependencyType="IEdiMessageBE"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
      </register>
      <register type="ICoprarBaplieBE" mapTo="CoprarBaplieBE">
        <lifetime type="perResolve"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="ErrorLogBE" dependencyType="IErrorLogBE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
      </register>
      <register type="IDemDetBE" mapTo="DemDetBE">
        <lifetime type="perResolve"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="DemDetDE" dependencyType="IDemDetDE"/>
        <property name="CommonBE" dependencyType="ICommonBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="PartnerBE" dependencyType="IPartnerBE"/>
      </register>
      <register type="IDemDetDE" mapTo="DemDetDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IDemDetDispatchOrdersBE" mapTo="DemDetDispatchOrdersBE">
        <lifetime type="perResolve"/>
        <property name="OperationDE" dependencyType="IOperationDE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="PartnerBE" dependencyType="IPartnerBE"/>
        <property name="DemDetBE" dependencyType="IDemDetBE"/>
      </register>
      <register type="IOperationDE" mapTo="OperationDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IDepositBE" mapTo="DepositBE">
        <lifetime type="perResolve"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="DepositDE" dependencyType="IDepositDE"/>
      </register>
      <register type="IDepositDE" mapTo="DepositDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEmptyReceivOrdersBE" mapTo="EmptyReceivOrdersBE">
        <lifetime type="perResolve"/>
        <property name="OperationDE" dependencyType="IOperationDE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="PartnerBE" dependencyType="IPartnerBE"/>
      </register>
      <register type="IOperationDE" mapTo="OperationDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEmptyReleaseOrdersBE" mapTo="EmptyReleaseOrdersBE">
        <lifetime type="perResolve"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="PartnerBE" dependencyType="IPartnerBE"/>
      </register>
      <register type="IGrossWeightServiceBE" mapTo="GrossWeightServiceBE">
        <lifetime type="perResolve"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="InvoiceBE" dependencyType="IInvoiceBE"/>
      </register>
      <register type="IPortTransferBE" mapTo="PortTransferBE">
        <lifetime type="perResolve"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="EquipmentBE" dependencyType="IEquipmentBE"/>
        <property name="PortTransferDE" dependencyType="IPortTransferDE"/>
      </register>
      <register type="IPortTransferDE" mapTo="PortTransferDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IStuffStripContainerBE" mapTo="StuffStripContainerBE">
        <lifetime type="perResolve"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="EquipmentBE" dependencyType="IEquipmentBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="StripStuffContainerDE" dependencyType="IStripStuffContainerDE"/>
        <property name="CommonBE" dependencyType="ICommonBE"/>
        <property name="ContainerMaintenanceBE" dependencyType="IContainerMaintenanceBE"/>
        <property name="YardConsolidationRequestBE" dependencyType="IYardConsolidationRequestBE"/>
        <property name="EdiMessageBE" dependencyType="IEdiMessageBE"/>
      </register>
      <register type="IStripStuffContainerDE" mapTo="StripStuffContainerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IEdiServerLogBE" mapTo="EdiServerLogBE">
        <lifetime type="perResolve"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="EdiServerLogDE" dependencyType="IEdiServerLogDE"/>
      </register>
      <register type="IEdiServerLogDE" mapTo="EdiServerLogDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ICoprarDischargeBE" mapTo="CoprarDischargeBE">
        <lifetime type="perResolve"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
        <property name="CoprarDischargeDE" dependencyType="ICoprarDischargeDE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="CoprarLoadBE" dependencyType="ICoprarLoadBE"/>
        <property name="ErrorLogBE" dependencyType="IErrorLogBE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="CoprarBaplieBE" dependencyType="ICoprarBaplieBE"/>
        <property name="YardConsolidationRequestBE" dependencyType="IYardConsolidationRequestBE"/>
      </register>
      <register type="ICoprarDischargeDE" mapTo="CoprarDischargeDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ISystemInterfaceBE" mapTo="SystemInterfaceBE">
        <lifetime type="perResolve"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
      </register>
      <register type="IYardConsolidationRequestBE" mapTo="YardConsolidationRequestBE">
        <lifetime type="perResolve"/>
        <property name="OperationBE" dependencyType="IOperationBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="YardConsolidationRequestDE" dependencyType="IYardConsolidationRequestDE"/>
        <property name="BillingBE" dependencyType="IBillingBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="PartnerBE" dependencyType="IPartnerBE"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
      </register>
      <register type="IYardConsolidationRequestDE" mapTo="YardConsolidationRequestDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IOperationBE" mapTo="OperationBE">
        <lifetime type="perResolve"/>
        <property name="OperationDE" dependencyType="IOperationDE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
      </register>
      <register type="IOperationDE" mapTo="OperationDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ICoprarLoadBE" mapTo="CoprarLoadBE">
        <lifetime type="perResolve"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
        <property name="VesselBE" dependencyType="IVesselBE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
      </register>
      <register type="ICatalogBE" mapTo="CatalogBE">
        <lifetime type="perResolve"/>
        <property name="CatalogDE" dependencyType="ICatalogDE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
      </register>
      <register type="ICatalogDE" mapTo="CatalogDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IErrorLogBE" mapTo="ErrorLogBE">
        <lifetime type="perResolve"/>
        <property name="ErrorLogDE" dependencyType="IErrorLogDE"/>
      </register>
      <register type="IErrorLogDE" mapTo="ErrorLogDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IExternalTransportBE" mapTo="ExternalTransportBE">
        <lifetime type="perResolve"/>
        <property name="ExternalTransportDE" dependencyType="IExternalTransportDE"/>
      </register>
      <register type="IExternalTransportDE" mapTo="ExternalTransportDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IImportExcelBE" mapTo="ImportExcelBE">
        <lifetime type="perResolve"/>
        <property name="ImportExcelDE" dependencyType="IImportExcelDE"/>
        <property name="CatalogBE" dependencyType="ICatalogBE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="ContainerMaintenanceBE" dependencyType="IContainerMaintenanceBE"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
        <property name="YardConsolidationRequestBE" dependencyType="IYardConsolidationRequestBE"/>
      </register>
      <register type="IImportExcelDE" mapTo="ImportExcelDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ISequenceBE" mapTo="SequenceBE">
        <lifetime type="perResolve"/>
        <property name="SequenceDE" dependencyType="ISequenceDE"/>
      </register>
      <register type="ISequenceDE" mapTo="SequenceDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ISystemCodeBE" mapTo="SystemCodeBE">
        <lifetime type="perResolve"/>
        <property name="SystemCodeDE" dependencyType="ISystemCodeDE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="SystemInterfaceBE" dependencyType="ISystemInterfaceBE"/>
      </register>
      <register type="ISystemCodeDE" mapTo="SystemCodeDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IContainerBE" mapTo="ContainerBE">
        <lifetime type="perResolve"/>
        <property name="ContainerDE" dependencyType="IContainerDE"/>
        <property name="AuditBE" dependencyType="IAuditBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="EdiMessageBE" dependencyType="IEdiMessageBE"/>
        <property name="VesselDE" dependencyType="IVesselDE"/>
        <property name="SequenceBE" dependencyType="ISequenceBE"/>
        <property name="TopxInterfaceDE" dependencyType="ITopxInterfaceDE"/>
      </register>
      <register type="IContainerDE" mapTo="ContainerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IVesselDE" mapTo="VesselDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="BllBase">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITopxInterfaceBE" mapTo="TopxInterfaceBE">
        <lifetime type="perResolve"/>
        <property name="ContainerDE" dependencyType="IContainerDE"/>
        <property name="VesselDE" dependencyType="IVesselDE"/>
        <property name="TopxInterfaceDE" dependencyType="ITopxInterfaceDE"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
      </register>
      <register type="IContainerDE" mapTo="ContainerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IVesselDE" mapTo="VesselDE">
        <lifetime type="perResolve"/>
      </register>
      <!--TOPX INTF DE-->
      <register type="ITopxInterfaceDE" mapTo="TopxInterfaceDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IVesselBE" mapTo="VesselBE">
        <lifetime type="perResolve"/>
        <property name="VesselDE" dependencyType="IVesselDE"/>
        <property name="ContainerDE" dependencyType="IContainerDE"/>
        <property name="TopxInterfaceBE" dependencyType="ITopxInterfaceBE"/>
        <property name="SystemInterfaceBE" dependencyType="ISystemInterfaceBE"/>
        <property name="CoprarLoadBE" dependencyType="ICoprarLoadBE"/>
        <property name="TopxInterfaceDE" dependencyType="ITopxInterfaceDE"/>
      </register>
      <register type="IVesselDE" mapTo="VesselDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IContainerDE" mapTo="ContainerDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IMrServiceBE" mapTo="MrServiceBE">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="MrServiceDE" dependencyType="IMrServiceDE"/>
        <property name="ApplicationBE" dependencyType="IApplicationBE"/>
        <property name="SystemCodeBE" dependencyType="ISystemCodeBE"/>
      </register>
      <register type="IMrServiceDE" mapTo="MrServiceDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IContainerBillingServiceBE" mapTo="ContainerBillingServiceBE">
        <lifetime type="perResolve"/>
        <property name="ContainerBE" dependencyType="IContainerBE"/>
        <property name="BillingBE" dependencyType="IBillingBE"/>
      </register>
      <register type="IPregateServiceBE" mapTo="PregateServiceBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ISystemMonitorBE" mapTo="SystemMonitorBE">
        <lifetime type="perResolve"/>
        <property name="SystemMonitorDE" dependencyType="ISystemMonitorDE"/>
      </register>
      <register type="ISystemMonitorDE" mapTo="SystemMonitorDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IExtTransportRateBE" mapTo="ExtTransportRateBE">
        <lifetime type="perResolve"/>
        <property name="ExtTransportRateDE" dependencyType="IExtTransportRateDE"/>
      </register>
      <register type="IExtTransportRateDE" mapTo="ExtTransportRateDE">
        <lifetime type="perResolve"/>
      </register>
      <!--nv.dung 2017.04.12 -  Empty Receival Depot-->
      <!--////////////////////////////////////////-->
      <register type="IEmptyReceivalDepotBE" mapTo="EmptyReceivalDepotBE">
        <lifetime type="perResolve"/>
        <property name="EmptyReceivalDepotDE" dependencyType="IEmptyReceivalDepotDE"/>
        <property name="SequenceDE" dependencyType="ISequenceDE"/>
      </register>
      <register type="IEmptyReceivalDepotDE" mapTo="EmptyReceivalDepotDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IPrinterSettingBE" mapTo="PrinterSettingBE">
        <lifetime type="perResolve"/>
        <!--<property name="PrinterSettingDE" dependencyType="IPrinterSettingDE" />-->
      </register>
      <register type="IDeliveryOrderDE" mapTo="DeliveryOrderDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IDeliveryOrderBE" mapTo="DeliveryOrderBE">
        <lifetime type="perResolve"/>
        <property name="DeliveryOrderDe" dependencyType="IDeliveryOrderDE"/>
        <property name="ContainerBe" dependencyType="IContainerBE"/>
        <property name="SequenceDe" dependencyType="ISequenceDE"/>
      </register>
      <register type="ITruckBE" mapTo="TruckBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ICableHookBE" mapTo="CableHookBE">
        <lifetime type="perResolve"/>
      </register>
      <!--////////////////////////////////////////-->
      <register type="ITosCustomsIntergrationBE" mapTo="TosCustomsIntergrationBE">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITosCustomsIntegrationDE" mapTo="TosCustomsIntegrationDE">
        <lifetime type="perResolve"/>
      </register>
      <register type="IScheduleReportBE" mapTo="ScheduleReportBe">
        <lifetime type="perResolve"/>
      </register>
      <register type="ITosServicesBE" mapTo="TosServicesBE">
        <lifetime type="perResolve"/>
      </register>
    </container>
  </unity>
  <system.diagnostics>
    <sources>
      <!-- This section defines the logging configuration for My.Application.Log -->
      <source name="DefaultSource" switchName="DefaultSwitch">
        <listeners>
          <add name="FileLog"/>
          <!-- Uncomment the below section to write to the Application Event Log -->
          <!--<add name="EventLog"/>-->
        </listeners>
      </source>
    </sources>
    <switches>
      <add name="DefaultSwitch" value="Information"/>
    </switches>
    <sharedListeners>
      <add name="FileLog" type="Microsoft.VisualBasic.Logging.FileLogTraceListener, Microsoft.VisualBasic, Version=8.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" initializeData="FileLogWriter"/>
      <!-- Uncomment the below section and replace APPLICATION_NAME with the name of your application to write to the Application Event Log -->
      <!--<add name="EventLog" type="System.Diagnostics.EventLogTraceListener" initializeData="APPLICATION_NAME"/> -->
    </sharedListeners>
  </system.diagnostics>
  <startup useLegacyV2RuntimeActivationPolicy="true">
    <supportedRuntime version="v4.0"/>
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Vbe.Interop" publicKeyToken="71E9BCE111E9429C" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="office" publicKeyToken="71E9BCE111E9429C" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-12.0.0.0" newVersion="12.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="office" publicKeyToken="71e9bce111e9429c" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Vbe.Interop" publicKeyToken="71e9bce111e9429c" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.VisualBasic" publicKeyToken="b03f5f7f11d50a3a" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.7.2" />
      </system.Web>
  -->
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri=""/>
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400"/>
      </providers>
    </roleManager>
    <compilation targetFramework="4.7.2"/>
  </system.web>
  <system.serviceModel>
    <extensions>
      <behaviorExtensions>
        <add name="Unity" type="Com.TCIS.Common.Services.Wcf.UnityInstanceProviderEndpointBehavior, Com.TCIS.Common.Services.Wcf, Version=*******, Culture=neutral"/>
      </behaviorExtensions>
    </extensions>
    <behaviors>
      <endpointBehaviors>
        <behavior name="UnityBehavior">
          <Unity/>
        </behavior>
      </endpointBehaviors>
      <serviceBehaviors>
        <behavior name="WSDLDisplayer">
          <serviceMetadata httpGetEnabled="true"/>
          <serviceDebug includeExceptionDetailInFaults="true"/>
        </behavior>
      </serviceBehaviors>
    </behaviors>
    <bindings>
      <basicHttpBinding>
        <binding name="LargeMessageBasicHttpBinding" closeTimeout="00:10:00" openTimeout="00:10:00" receiveTimeout="00:10:00" sendTimeout="00:10:00" allowCookies="false" bypassProxyOnLocal="false" hostNameComparisonMode="StrongWildcard" maxBufferPoolSize="**********" maxBufferSize="**********" maxReceivedMessageSize="**********" textEncoding="utf-8" transferMode="Buffered" useDefaultWebProxy="true" messageEncoding="Text">
          <readerQuotas maxDepth="32" maxStringContentLength="**********" maxArrayLength="16384" maxBytesPerRead="4096" maxNameTableCharCount="16384"/>
          <security mode="None">
            <transport clientCredentialType="None" proxyCredentialType="None" realm=""/>
            <message clientCredentialType="UserName" algorithmSuite="Default"/>
          </security>
        </binding>
      </basicHttpBinding>
      <wsDualHttpBinding>
        <binding name="EnquireCCBinding">
          <security mode="None"/>
        </binding>
      </wsDualHttpBinding>
    </bindings>
    <services>
      <service name="Com.TCIS.TopoVn.Bll.TosServicesBE" behaviorConfiguration="WSDLDisplayer">
        <endpoint address="TosServiceAddress" behaviorConfiguration="UnityBehavior" binding="basicHttpBinding" contract="Com.TCIS.TopoVn.Bll.ITosServicesBE" name="TosServiceHttpBinding"/>
        <endpoint address="mex" binding="mexHttpBinding" contract="IMetadataExchange"/>
      </service>
    </services>
    <serviceHostingEnvironment multipleSiteBindingsEnabled="true"/>
    <client>
      <endpoint address="http://localhost/SNPBridge/PaymentService.asmx" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="Gateway.PaymentServiceSoap" name="PaymentServiceSoap"/>
      <endpoint address="http://**************:8004/Snp.Eport.Gateway/OrderIntfGateway.svc" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="Eport_OrderIntfGateway.IOrderIntfGateway" name="BasicHttpBinding_IOrderIntfGateway"/>
      <endpoint address="http://**************/eInvoiceGetway/EInvoiceGateway.svc" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="EInvoiceGateway.IEInvoiceServiceBE" name="BasicHttpBinding_IEInvoiceServiceBE"/>
      <endpoint address="http://**************/CustomsIntfGatewayServices/CustomsIntfGatewayServices.svc" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="CCIntfGatewayServcies.ICustomsIntfGatewayBE" name="CustomsIntfGatewayBEBinding1"/>
      <endpoint address="http://**************/IntfHQ/TOPCTL/TOSWebServices/CustomsIntegrationServices.svc/CustomsIntegrationBEServiceAddress" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="CustomsIntegrationServices.ICustomsIntegrationBE" name="CustomsIntegrationBEBinding"/>
      <endpoint address="http://**************/CustomsIntfGatewayServices/EnquireCCServices.svc" binding="wsDualHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="EnquireCCServices.IEnquireCC" name="EnquireCCBinding"/>
      <endpoint address="http://************/EBillingGateway/EInvoiceGateway.svc" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="EInvoiceGateway.IEBillingGatewayBE" name="LargeMessageBasicHttpBinding1"/>
      <endpoint address="http://************/WCFSmartDevice_Test/FTPServices.svc/FTPServicesBEServiceAddress" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="FTPServices.IFTPServicesBE" name="LargeMessageBasicHttpBinding2"/>
      <endpoint address="http://************/FTPServicesGateway/FTPServiceGateway.svc" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="FTPServicesGateway.IFTPServiceGatewayBE" name="LargeMessageBasicHttpBinding3"/>
      <endpoint address="http://**************/IntfHQ-Ver2.4/TOPHIT/TOSWebServices/CustomsIntegrationServices.svc/CustomsIntegrationBEServiceAddress" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="TosCustomsIntegrationServices.ICustomsIntegrationBE" name="LargeMessageBasicHttpBinding"/>
      <endpoint address="http://**************/FastInvIntegrationServices/FastInvIntegrationServices.svc/FastInvIntegrationServiceAddress" binding="basicHttpBinding" bindingConfiguration="LargeMessageBasicHttpBinding" contract="FastInvIntegrationServices.IFastInvIntegrationBE" name="FastInvIntegrationBEBinding"/>
    </client>
  </system.serviceModel>
</configuration>
<!--ProjectGuid: {F8DA2517-A27D-49B9-977A-962BF6E0CCC5}-->